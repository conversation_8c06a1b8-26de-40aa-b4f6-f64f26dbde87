// Admin login functionality
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');

    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // 显示加载中提示
        const submitBtn = loginForm.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
        submitBtn.disabled = true;

        try {
            // 调用API进行登录
            const result = await ApiClient.adminLogin({ username, password });

            if (result && result.success) {
                // 登录成功，检查是否有重定向URL
                const urlParams = new URLSearchParams(window.location.search);
                const redirectUrl = urlParams.get('redirect');

                if (redirectUrl) {
                    // 如果有重定向URL，则跳转到该URL
                    window.location.href = redirectUrl;
                } else {
                    // 否则跳转到管理员仪表板
                    window.location.href = 'dashboard.html';
                }
            } else {
                // 登录失败
                alert(result ? result.message : '登录失败，请重试');
            }
        } catch (error) {
            console.error('登录错误:', error);
            alert('登录失败，请重试');
        } finally {
            // 恢复按钮状态
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
        }
    });
});
