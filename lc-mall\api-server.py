import http.server
import socketserver
import json
import os
import time
from urllib.parse import urlparse, parse_qs

# 数据文件路径
DATA_DIR = os.path.join(os.getcwd(), "lc-mall", "data")
PRODUCTS_FILE = os.path.join(DATA_DIR, "products.json")
ORDERS_FILE = os.path.join(DATA_DIR, "orders.json")
MESSAGES_FILE = os.path.join(DATA_DIR, "messages.json")
SMS_RECORDS_FILE = os.path.join(DATA_DIR, "sms-records.json")
USERS_FILE = os.path.join(DATA_DIR, "users.json")

# 确保数据目录存在
os.makedirs(DATA_DIR, exist_ok=True)

# 初始化数据文件
def init_data_files():
    # 初始化产品数据
    if not os.path.exists(PRODUCTS_FILE):
        with open(PRODUCTS_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

    # 初始化订单数据
    if not os.path.exists(ORDERS_FILE):
        with open(ORDERS_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

    # 初始化留言数据
    if not os.path.exists(MESSAGES_FILE):
        with open(MESSAGES_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

    # 初始化SMS记录数据
    if not os.path.exists(SMS_RECORDS_FILE):
        with open(SMS_RECORDS_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

    # 初始化用户数据
    if not os.path.exists(USERS_FILE):
        with open(USERS_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

# 读取数据
def read_data(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return []

# 写入数据
def write_data(file_path, data):
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, ensure_ascii=False, indent=2, fp=f)

class APIHandler(http.server.BaseHTTPRequestHandler):
    def _set_headers(self, status_code=200, content_type='application/json'):
        self.send_response(status_code)
        self.send_header('Content-type', content_type)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_OPTIONS(self):
        self._set_headers()

    def do_GET(self):
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        query_params = parse_qs(parsed_url.query)

        # 产品API
        if path == '/api/products':
            products = read_data(PRODUCTS_FILE)
            self._set_headers()
            self.wfile.write(json.dumps(products, ensure_ascii=False).encode('utf-8'))

        # 订单API
        elif path == '/api/orders':
            orders = read_data(ORDERS_FILE)
            self._set_headers()
            self.wfile.write(json.dumps(orders, ensure_ascii=False).encode('utf-8'))

        # 留言API
        elif path == '/api/messages':
            messages = read_data(MESSAGES_FILE)
            self._set_headers()
            self.wfile.write(json.dumps(messages, ensure_ascii=False).encode('utf-8'))

        # SMS记录API
        elif path == '/api/sms-records':
            sms_records = read_data(SMS_RECORDS_FILE)
            self._set_headers()
            self.wfile.write(json.dumps(sms_records, ensure_ascii=False).encode('utf-8'))

        # 获取所有用户
        elif path == '/api/users':
            users = read_data(USERS_FILE)
            self._set_headers()
            self.wfile.write(json.dumps(users, ensure_ascii=False).encode('utf-8'))

        # 用户API - 检查手机号是否存在
        elif path == '/api/users/check':
            phone = query_params.get('phone', [''])[0]
            if not phone:
                self._set_headers(400)
                self.wfile.write(json.dumps({"error": "Missing phone parameter"}).encode('utf-8'))
                return

            users = read_data(USERS_FILE)
            exists = any(user.get('phone') == phone for user in users)

            self._set_headers()
            self.wfile.write(json.dumps({"exists": exists}, ensure_ascii=False).encode('utf-8'))

        else:
            self._set_headers(404)
            self.wfile.write(json.dumps({"error": "Not found"}).encode('utf-8'))

    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)

        try:
            data = json.loads(post_data.decode('utf-8'))
        except json.JSONDecodeError:
            self._set_headers(400)
            self.wfile.write(json.dumps({"error": "Invalid JSON"}).encode('utf-8'))
            return

        # 添加产品
        if self.path == '/api/products':
            products = read_data(PRODUCTS_FILE)

            # 生成新ID
            new_id = 1
            if products:
                new_id = max(p.get('id', 0) for p in products) + 1

            data['id'] = new_id
            products.append(data)

            write_data(PRODUCTS_FILE, products)

            self._set_headers()
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

        # 添加订单
        elif self.path == '/api/orders':
            orders = read_data(ORDERS_FILE)

            # 生成订单ID
            order_id = f"LC-{int(time.time())}-{len(orders) + 1}"
            data['id'] = order_id

            # 添加订单时间
            if 'date' not in data:
                data['date'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())

            orders.append(data)
            write_data(ORDERS_FILE, orders)

            # 更新产品库存
            products = read_data(PRODUCTS_FILE)
            for item in data.get('items', []):
                product_id = item.get('id')
                quantity = item.get('quantity', 0)

                for product in products:
                    if product.get('id') == product_id:
                        current_stock = product.get('stock', 0)
                        if current_stock > 0:
                            product['stock'] = max(0, current_stock - quantity)

            write_data(PRODUCTS_FILE, products)

            self._set_headers()
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

        # 添加留言
        elif self.path == '/api/messages':
            messages = read_data(MESSAGES_FILE)

            # 生成留言ID
            message_id = f"MSG-{int(time.time())}-{len(messages) + 1}"
            data['id'] = message_id

            # 添加留言时间
            if 'date' not in data:
                data['date'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())

            messages.append(data)
            write_data(MESSAGES_FILE, messages)

            self._set_headers()
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

        # 添加SMS记录
        elif self.path == '/api/sms-records':
            sms_records = read_data(SMS_RECORDS_FILE)

            # 生成记录ID
            record_id = f"SMS-{int(time.time())}-{len(sms_records) + 1}"
            data['id'] = record_id

            # 添加记录时间
            if 'date' not in data:
                data['date'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())

            sms_records.append(data)
            write_data(SMS_RECORDS_FILE, sms_records)

            self._set_headers()
            self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

        # 用户注册
        elif self.path == '/api/users/register':
            # 验证必要字段
            required_fields = ['phone', 'name', 'company']
            for field in required_fields:
                if field not in data:
                    self._set_headers(400)
                    self.wfile.write(json.dumps({"error": f"Missing required field: {field}"}).encode('utf-8'))
                    return

            users = read_data(USERS_FILE)

            # 检查手机号是否已存在
            if any(user.get('phone') == data['phone'] for user in users):
                self._set_headers(400)
                self.wfile.write(json.dumps({"error": "手机号已注册，请直接登录"}).encode('utf-8'))
                return

            # 生成用户ID
            new_id = 1
            if users:
                new_id = max(user.get('id', 0) for user in users) + 1

            # 创建新用户
            new_user = {
                'id': new_id,
                'phone': data['phone'],
                'name': data['name'],
                'company': data['company'],
                'wechat': data.get('wechat', ''),
                'position': data.get('position', ''),
                'address': data.get('address', ''),
                'created_at': time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime()),
                'last_login': None
            }

            users.append(new_user)
            write_data(USERS_FILE, users)

            # 返回用户信息
            self._set_headers()
            self.wfile.write(json.dumps(new_user, ensure_ascii=False).encode('utf-8'))

        # 用户登录
        elif self.path == '/api/users/login':
            # 验证必要字段
            if 'phone' not in data:
                self._set_headers(400)
                self.wfile.write(json.dumps({"error": "请输入手机号码"}).encode('utf-8'))
                return

            users = read_data(USERS_FILE)

            # 查找用户
            user = next((user for user in users if user.get('phone') == data['phone']), None)

            if not user:
                self._set_headers(401)
                self.wfile.write(json.dumps({"error": "该手机号未注册，请先注册"}).encode('utf-8'))
                return

            # 更新最后登录时间
            user['last_login'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
            write_data(USERS_FILE, users)

            # 返回用户信息
            self._set_headers()
            self.wfile.write(json.dumps(user, ensure_ascii=False).encode('utf-8'))

        else:
            self._set_headers(404)
            self.wfile.write(json.dumps({"error": "Not found"}).encode('utf-8'))

def run_api_server(host="localhost", port=9081):
    # 初始化数据文件
    init_data_files()

    # 启动服务器
    handler = APIHandler
    with socketserver.TCPServer((host, port), handler) as httpd:
        print(f"API Server running at http://{host}:{port}/")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nShutting down API server...")
            httpd.server_close()

if __name__ == "__main__":
    run_api_server()
