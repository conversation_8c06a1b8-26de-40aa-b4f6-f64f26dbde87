/* 头部背景样式 */
.header-background {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    margin-bottom: 30px;
    background-color: #f0f8ff;
    background-image:
        linear-gradient(to right, rgba(100, 180, 255, 0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(100, 180, 255, 0.1) 1px, transparent 1px),
        linear-gradient(to right, rgba(100, 180, 255, 0.05) 0.5px, transparent 0.5px),
        linear-gradient(to bottom, rgba(100, 180, 255, 0.05) 0.5px, transparent 0.5px);
    background-size: 20px 20px, 20px 20px, 5px 5px, 5px 5px;
    box-shadow: inset 0 0 20px rgba(100, 180, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 化学公式容器 */
.chemical-formulas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none; /* 确保不会干扰用户交互 */
}

.header-content {
    text-align: center;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    z-index: 2; /* 确保内容显示在化学公式之上 */
    position: relative; /* 确保z-index生效 */
}

.header-content h1 {
    color: #0066cc;
    font-size: 2.5rem;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 700;
}

.header-content p {
    color: #333;
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.5;
}

/* 添加一些装饰元素 */
.header-background::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(100, 180, 255, 0.2) 0%, rgba(100, 180, 255, 0) 70%);
    border-radius: 50%;
    z-index: 1;
}

.header-background::after {
    content: '';
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(100, 180, 255, 0.2) 0%, rgba(100, 180, 255, 0) 70%);
    border-radius: 50%;
    z-index: 1;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .header-background {
        height: 200px;
    }

    .header-content h1 {
        font-size: 1.8rem;
    }

    .header-content p {
        font-size: 1rem;
    }
}
