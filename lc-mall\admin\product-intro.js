// 产品介绍管理脚本
document.addEventListener('DOMContentLoaded', async function() {
    // DOM元素
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const editButtons = document.querySelectorAll('.edit-btn');
    const modal = document.getElementById('edit-modal');
    const closeButtons = document.querySelectorAll('.close, .close-modal');
    const modalTitle = document.getElementById('modal-title');
    const categoryForm = document.getElementById('category-form');
    const categoryTitleInput = document.getElementById('category-title');
    const categoryContentInput = document.getElementById('category-content');
    const saveCategoryBtn = document.getElementById('save-category-btn');
    
    // 产品介绍数据
    let productIntroData = null;
    
    // 加载产品介绍数据
    async function loadProductIntroData() {
        try {
            const response = await fetch('/api/product-intro');
            if (!response.ok) {
                throw new Error('Failed to load product intro data');
            }
            
            productIntroData = await response.json();
            updateUI();
        } catch (error) {
            console.error('Error loading product intro data:', error);
            
            // 如果API调用失败，尝试从本地JSON文件加载
            try {
                const response = await fetch('/lc-mall/data/product-intro.json');
                if (!response.ok) {
                    throw new Error('Failed to load local product intro data');
                }
                
                productIntroData = await response.json();
                updateUI();
            } catch (localError) {
                console.error('Error loading local product intro data:', localError);
                alert('无法加载产品介绍数据，请刷新页面重试。');
            }
        }
    }
    
    // 更新UI
    function updateUI() {
        if (!productIntroData) return;
        
        // 更新类别卡片
        const categoryCards = document.querySelectorAll('.category-card');
        categoryCards.forEach(card => {
            const editBtn = card.querySelector('.edit-btn');
            const categoryId = editBtn.getAttribute('data-id');
            const categoryData = productIntroData.categories[categoryId];
            
            if (categoryData) {
                card.querySelector('h3').textContent = categoryData.title;
                card.querySelector('.category-content p').textContent = categoryData.content;
            }
        });
        
        // 更新表格预览
        const tableCards = document.querySelectorAll('.table-card');
        tableCards.forEach(card => {
            const editBtn = card.querySelector('.edit-btn');
            const tableId = editBtn.getAttribute('data-id');
            const tableData = productIntroData.tables[tableId];
            
            if (tableData && tableData.length > 0) {
                const tableBody = card.querySelector('tbody');
                tableBody.innerHTML = '';
                
                // 只显示前3行作为预览
                const previewData = tableData.slice(0, 3);
                
                previewData.forEach(row => {
                    const tr = document.createElement('tr');
                    
                    const nameTd = document.createElement('td');
                    nameTd.textContent = row.name;
                    tr.appendChild(nameTd);
                    
                    const modelTd = document.createElement('td');
                    modelTd.textContent = row.model;
                    tr.appendChild(modelTd);
                    
                    const dosageTd = document.createElement('td');
                    dosageTd.textContent = row.dosage || '——';
                    tr.appendChild(dosageTd);
                    
                    const featuresTd = document.createElement('td');
                    // 截断过长的特点文本
                    const truncatedFeatures = row.features.length > 30 
                        ? row.features.substring(0, 30) + '...' 
                        : row.features;
                    featuresTd.textContent = truncatedFeatures;
                    tr.appendChild(featuresTd);
                    
                    tableBody.appendChild(tr);
                });
            }
        });
    }
    
    // 标签页切换
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        });
    });
    
    // 打开编辑模态框
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const type = this.getAttribute('data-type');
            
            if (type === 'category') {
                openCategoryEditModal(id);
            } else if (type === 'table') {
                // 重定向到产品介绍页面的表格编辑模式
                window.location.href = '../product-intro.html?edit=table&id=' + id;
            }
        });
    });
    
    // 打开类别编辑模态框
    function openCategoryEditModal(categoryId) {
        if (!productIntroData) return;
        
        const categoryData = productIntroData.categories[categoryId];
        if (!categoryData) return;
        
        // 设置模态框标题
        modalTitle.textContent = '编辑类别: ' + categoryData.title;
        
        // 填充表单
        categoryTitleInput.value = categoryData.title;
        categoryContentInput.value = categoryData.content;
        
        // 显示类别表单
        categoryForm.style.display = 'block';
        
        // 保存按钮点击事件
        saveCategoryBtn.onclick = function() {
            saveCategoryData(categoryId);
        };
        
        // 显示模态框
        modal.style.display = 'block';
    }
    
    // 保存类别数据
    async function saveCategoryData(categoryId) {
        if (!productIntroData) return;
        
        const title = categoryTitleInput.value.trim();
        const content = categoryContentInput.value.trim();
        
        if (!title) {
            alert('请输入类别标题');
            return;
        }
        
        if (!content) {
            alert('请输入类别描述');
            return;
        }
        
        // 更新数据
        productIntroData.categories[categoryId] = {
            title: title,
            content: content
        };
        
        try {
            // 保存到API
            const response = await fetch('/api/product-intro/category', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    categoryId: categoryId,
                    data: {
                        title: title,
                        content: content
                    }
                })
            });
            
            if (!response.ok) {
                throw new Error('Failed to save category data');
            }
            
            // 更新UI
            updateUI();
            
            // 关闭模态框
            modal.style.display = 'none';
            
            // 显示成功消息
            alert('类别数据保存成功');
        } catch (error) {
            console.error('Error saving category data:', error);
            alert('保存失败，请重试');
        }
    }
    
    // 关闭模态框
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    // 加载数据
    loadProductIntroData();
});
