/* 用户状态显示修复 */

/* 用户欢迎信息 */
.user-welcome {
    color: white !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
    display: flex !important;
    align-items: center !important;
    margin-right: 10px !important;
}

.user-welcome i {
    color: white !important;
    margin-right: 5px !important;
}

.user-welcome span {
    color: white !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
    max-width: 150px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 退出按钮 */
#logout-btn {
    background-color: #1a73e8 !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 5px 10px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    display: inline-block !important;
    text-align: center !important;
    margin-left: 5px !important;
    position: relative !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

#logout-btn span {
    color: white !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    position: relative !important;
    z-index: 2 !important;
}

/* 确保在移动设备上也能正确显示 */
@media (max-width: 768px) {
    .user-welcome {
        font-size: 12px !important;
        max-width: 100px !important;
    }
    
    .user-welcome span {
        max-width: 80px !important;
    }
    
    #logout-btn {
        padding: 4px 8px !important;
        font-size: 12px !important;
    }
}
