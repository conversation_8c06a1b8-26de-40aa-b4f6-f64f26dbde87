/**
 * 微信支付模块
 * 提供微信支付相关功能
 */

// 微信支付客户端
const WeChatPay = {
    /**
     * 创建微信支付订单
     * @param {Object} orderData - 订单数据
     * @returns {Promise<Object>} - 支付结果
     */
    async createOrder(orderData) {
        try {
            return await ApiClient.createWechatPayOrder(orderData);
        } catch (error) {
            console.error('创建微信支付订单错误:', error);
            throw error;
        }
    },

    /**
     * 查询微信支付订单状态
     * @param {string} orderId - 订单ID
     * @returns {Promise<Object>} - 订单状态
     */
    async queryOrder(orderId) {
        try {
            return await ApiClient.queryWechatPayOrder(orderId);
        } catch (error) {
            console.error('查询微信支付订单错误:', error);
            throw error;
        }
    },

    /**
     * 显示微信支付收款码
     * @param {string} orderId - 订单ID
     * @param {number} totalAmount - 订单金额
     * @returns {Promise<boolean>} - 支付结果
     */
    async showPaymentQRCode(orderId, totalAmount) {
        return new Promise((resolve, reject) => {
            try {
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'wechat-pay-modal';
                modal.innerHTML = `
                    <div class="wechat-pay-content">
                        <span class="close">&times;</span>
                        <h2>微信支付</h2>
                        <div class="order-info">
                            <p><strong>订单号：</strong>${orderId}</p>
                            <p><strong>支付金额：</strong>¥${totalAmount.toFixed(2)}</p>
                        </div>
                        <p class="payment-instruction">请使用微信扫描下方收款码完成支付</p>
                        <div class="wechat-pay-qrcode">
                            <img src="/lc-mall/images/payment/wx_pay.jpg" alt="微信收款码" class="payment-qrcode-img">
                        </div>
                        <div class="payment-notice">
                            <p class="notice-text">⚠️ 支付完成后，请点击下方"我已完成支付"按钮</p>
                            <p class="notice-text">💡 我们会在收到款项后尽快处理您的订单</p>
                        </div>
                        <div class="payment-actions">
                            <button class="btn-payment-confirm" id="confirm-payment">我已完成支付</button>
                            <button class="btn-payment-cancel" id="cancel-payment">取消支付</button>
                        </div>
                    </div>
                `;

                // 添加到页面
                document.body.appendChild(modal);

                // 关闭按钮事件
                const closeBtn = modal.querySelector('.close');
                const cancelBtn = modal.querySelector('#cancel-payment');
                const confirmBtn = modal.querySelector('#confirm-payment');

                const closeModal = () => {
                    modal.remove();
                    resolve(false);
                };

                closeBtn.addEventListener('click', closeModal);
                cancelBtn.addEventListener('click', closeModal);

                // 确认支付按钮事件
                confirmBtn.addEventListener('click', () => {
                    // 显示确认对话框
                    if (confirm('请确认您已完成微信支付？\n\n我们会在收到款项后处理您的订单。')) {
                        modal.remove();
                        resolve(true);
                    }
                });

                // 显示模态框
                modal.style.display = 'block';

                // 点击模态框外部关闭
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        closeModal();
                    }
                });

            } catch (error) {
                console.error('显示微信支付收款码错误:', error);
                reject(error);
            }
        });
    },

    /**
     * 轮询订单状态
     * @param {string} orderId - 订单ID
     * @param {HTMLElement} modal - 模态框元素
     * @param {Function} resolve - Promise resolve函数
     * @param {Function} reject - Promise reject函数
     * @private
     */
    async _pollOrderStatus(orderId, modal, resolve, reject) {
        try {
            // 最大轮询次数
            const maxPolls = 60; // 最多轮询60次，即10分钟
            let pollCount = 0;

            // 轮询间隔（毫秒）
            const pollInterval = 10000; // 10秒

            // 轮询函数
            const poll = async () => {
                try {
                    pollCount++;

                    // 查询订单状态
                    const result = await this.queryOrder(orderId);

                    // 更新状态消息
                    const messageElement = modal.querySelector('#payment-message');

                    if (result.success) {
                        if (result.status === 'paid') {
                            // 支付成功
                            messageElement.textContent = '支付成功！正在跳转...';
                            messageElement.style.color = '#4CAF50';

                            // 移除加载动画
                            const spinner = modal.querySelector('.spinner');
                            if (spinner) spinner.remove();

                            // 延迟关闭模态框
                            setTimeout(() => {
                                modal.remove();
                                resolve(true);
                            }, 2000);

                            return;
                        } else {
                            // 继续等待支付
                            messageElement.textContent = `等待支付...（${pollCount}/${maxPolls}）`;
                        }
                    } else {
                        // 查询失败
                        messageElement.textContent = `查询支付状态失败: ${result.error}`;
                        messageElement.style.color = '#F44336';
                    }

                    // 检查是否达到最大轮询次数
                    if (pollCount >= maxPolls) {
                        messageElement.textContent = '支付超时，请重新下单';
                        messageElement.style.color = '#F44336';

                        // 移除加载动画
                        const spinner = modal.querySelector('.spinner');
                        if (spinner) spinner.remove();

                        // 添加关闭按钮
                        const closeButton = document.createElement('button');
                        closeButton.textContent = '关闭';
                        closeButton.className = 'btn btn-primary';
                        closeButton.addEventListener('click', () => {
                            modal.remove();
                            resolve(false);
                        });

                        const paymentStatus = modal.querySelector('.payment-status');
                        paymentStatus.appendChild(closeButton);

                        return;
                    }

                    // 继续轮询
                    setTimeout(poll, pollInterval);
                } catch (error) {
                    console.error('轮询订单状态错误:', error);

                    // 更新状态消息
                    const messageElement = modal.querySelector('#payment-message');
                    messageElement.textContent = `查询支付状态错误: ${error.message}`;
                    messageElement.style.color = '#F44336';

                    // 移除加载动画
                    const spinner = modal.querySelector('.spinner');
                    if (spinner) spinner.remove();

                    // 添加关闭按钮
                    const closeButton = document.createElement('button');
                    closeButton.textContent = '关闭';
                    closeButton.className = 'btn btn-primary';
                    closeButton.addEventListener('click', () => {
                        modal.remove();
                        resolve(false);
                    });

                    const paymentStatus = modal.querySelector('.payment-status');
                    paymentStatus.appendChild(closeButton);
                }
            };

            // 开始轮询
            poll();
        } catch (error) {
            console.error('轮询订单状态错误:', error);
            reject(error);
        }
    }
};

// 导出微信支付客户端
window.WeChatPay = WeChatPay;
