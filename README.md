# 高性能Web服务器
服务器使用aiohttp库提供异步处理能力，是最推荐的选项。

### 特点:
- 异步处理请求，支持高并发
- 智能缓存控制，提高加载速度
- 请求限流，防止服务器过载
- 完整的CORS支持
- 详细的日志记录


### 依赖:
- Python 3.7+
- aiohttp
- aiohttp_cors

Nginx配置
gdlongchi.cn

sudo apt-get install nginx
sudo systemctl status nginx
vim /etc/nginx/sites-available/gdlongchi.cn
sudo ln -s /etc/nginx/sites-available/gdlongchi.cn /etc/nginx/sites-enabled/
sudo nginx -t
sudo rm /etc/nginx/sites-enabled/default
sudo systemctl reload nginx

 py enhanced-server.py --host 0.0.0.0 --port 8091 --root F:\github\LongChiMall