/* 移动端优化样式 */

/* 基础样式优化 */
@media (max-width: 768px) {
    body {
        font-size: 14px;
    }

    .container {
        padding: 0 1rem;
    }

    /* 顶部栏优化 */
    .top-bar {
        display: none; /* 在移动端隐藏顶部栏 */
    }

    .top-bar-content {
        flex-direction: column;
        gap: 0.5rem;
    }

    .logo {
        height: 40px;
    }

    .header-company-info h2 {
        font-size: 1.2rem;
        margin-bottom: 0.25rem;
    }

    .header-company-info p {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }

    .hotline {
        font-size: 0.8rem;
    }

    .hotline span {
        font-size: 0.9rem;
    }

    /* 导航栏优化 */
    header {
        background-color: var(--primary-color);
        padding: 0.5rem 0;
        position: sticky;
        top: 0;
        z-index: 100;
    }

    .header-container {
        justify-content: space-between;
        padding: 0 1rem;
        align-items: center;
    }

    /* 移动端Logo */
    .mobile-logo {
        display: flex;
        align-items: center;
    }

    .mobile-logo .logo {
        height: 30px;
    }

    /* 移动导航菜单优化 */
    .nav-container {
        position: fixed;
        top: 0;
        left: -100%;
        background-color: var(--bg-color);
        width: 80%;
        height: 100vh;
        padding: 4rem 1.5rem 2rem;
        overflow-y: auto;
        transition: left 0.3s ease;
        z-index: 1000;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }

    .nav-container.active {
        left: 0;
    }

    /* 移动导航遮罩层 */
    .mobile-nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        animation: fadeIn 0.3s ease;
    }

    nav ul li a {
        color: var(--text-color);
        font-size: 1rem;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    nav ul li a.active {
        color: var(--primary-color);
        font-weight: 600;
    }

    nav ul li a::after {
        display: none;
    }

    /* 购物车图标优化 */
    .cart-icon {
        margin-left: 1rem;
    }

    .cart-icon a {
        width: 36px;
        height: 36px;
        background-color: var(--accent-color);
    }

    /* 浮动购物车优化 */
    .floating-cart {
        bottom: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
        background-color: var(--accent-color);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    /* 产品布局优化 */
    .product-layout {
        flex-direction: column;
    }

    .category-sidebar {
        width: 100%;
        margin-bottom: 1rem;
    }

    .category-sidebar h3 {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        text-align: center;
    }

    .category-nav {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        padding: 0.75rem 0.5rem;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        background-color: var(--bg-light);
        border-radius: var(--radius);
        box-shadow: var(--shadow-sm);
        margin-bottom: 1rem;
    }

    .category-nav::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .category-nav li {
        flex: 0 0 auto;
        margin-right: 0.75rem;
    }

    .category-nav li:last-child {
        margin-right: 0;
        padding-right: 0.5rem;
    }

    .category-link {
        white-space: nowrap;
        padding: 0.5rem 0.75rem;
        background-color: var(--bg-color);
        border-radius: var(--radius-full);
        border: 1px solid var(--border-color);
        font-size: 0.85rem;
        box-shadow: var(--shadow-sm);
        transition: all 0.2s ease;
    }

    .category-link.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        box-shadow: var(--shadow);
        transform: translateY(-2px);
    }

    /* 产品卡片优化 */
    .product-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        width: 100%;
    }

    .product-card {
        border-radius: var(--radius);
        box-shadow: var(--shadow-sm);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 100%;
        margin-bottom: 1rem;
        position: relative;
        background-color: var(--bg-color);
    }

    .product-card:active {
        transform: scale(0.98);
    }

    .product-card-header {
        position: relative;
        width: 100%;
        height: 120px;
        overflow: hidden;
    }

    .product-card-header img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
    }

    .product-category-tag {
        position: absolute;
        top: 0.5rem;
        left: 0.5rem;
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
        border-radius: var(--radius-full);
    }

    .product-badges {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .product-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        border-radius: var(--radius-full);
    }

    .product-info {
        padding: 0.75rem;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .product-info h3 {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 1.2em;
    }

    .product-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
        font-size: 0.7rem;
        min-height: 1.2em;
    }

    .product-rating {
        font-size: 0.7rem;
    }

    .product-stock {
        font-size: 0.7rem;
        margin-bottom: 0.25rem;
        min-height: 1em;
    }

    .price {
        font-size: 1rem;
        font-weight: 600;
        color: var(--accent-color);
        margin-bottom: 0.5rem;
    }

    .product-actions {
        display: flex;
        justify-content: space-between;
        gap: 0.5rem;
        margin-top: auto;
    }

    .product-actions a {
        flex: 1;
        padding: 0.4rem 0.5rem;
        font-size: 0.75rem;
        text-align: center;
    }

    /* 模态框优化 */
    .modal-content {
        width: 95%;
        padding: 1rem;
        margin: 5% auto;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-content h2 {
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }

    .close {
        top: 0.75rem;
        right: 0.75rem;
        font-size: 1.5rem;
    }

    /* 产品详情模态框 */
    .product-modal-content {
        flex-direction: column;
        gap: 1rem;
    }

    .product-modal-image {
        width: 100%;
        height: auto;
        max-height: 200px;
        overflow: hidden;
        border-radius: var(--radius);
    }

    .product-modal-image img {
        width: 100%;
        height: auto;
        object-fit: cover;
    }

    .product-modal-info {
        width: 100%;
    }

    .product-modal-info h2 {
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }

    /* 购物车模态框 */
    .cart-item {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
        flex-wrap: wrap;
    }

    .cart-item-image {
        width: 60px;
        height: 60px;
    }

    .cart-item-details {
        padding-left: 0.75rem;
    }

    .cart-item-name {
        font-size: 0.9rem;
    }

    .cart-item-price {
        font-size: 0.9rem;
    }

    .cart-item-actions {
        width: 100%;
        margin-top: 0.5rem;
        margin-left: 0;
        justify-content: flex-end;
    }

    .cart-quantity {
        font-size: 0.8rem;
    }

    .cart-total {
        padding: 0.75rem 0;
        font-size: 1rem;
    }

    /* 页脚优化 */
    .footer-new {
        padding: 2rem 1rem 1rem;
    }

    .footer-nav {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    .footer-nav a {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .footer-info {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .company-info p {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }

    .qrcode-container img {
        width: 100px;
        height: 100px;
    }

    .copyright {
        font-size: 0.7rem;
        padding: 0.75rem 0;
    }
}

/* 回到顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 1rem;
    left: 1rem;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 99;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:active {
    transform: scale(0.95);
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
    .product-container {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .product-card-header {
        height: 100px;
    }

    .product-card h4 {
        font-size: 0.8rem;
    }

    .product-price {
        font-size: 0.9rem;
    }
}
