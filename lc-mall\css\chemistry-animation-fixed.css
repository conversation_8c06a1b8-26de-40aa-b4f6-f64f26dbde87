/* 化学实验动画样式 */
.chemistry-animation {
    position: relative;
    width: 100%;
    height: 600px;
    background-color: #0a1929;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* 实验容器 */
.lab-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    max-width: 1000px;
    height: 400px;
    z-index: 5;
    position: relative;
}

/* 烧杯 */
.beaker {
    position: relative;
    width: 150px;
    height: 180px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px 5px 20px 20px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.3);
    transform-origin: bottom center;
    animation: beaker-shake 8s infinite;
}

.beaker-left {
    animation-delay: 0s;
    margin-right: 50px;
}

.beaker-right {
    animation-delay: 4s;
    margin-left: 50px;
}

/* 试管 */
.test-tube {
    position: relative;
    width: 60px;
    height: 200px;
    background-color: rgba(255, 255, 255, 0.15);
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 30px 30px 5px 5px;
    overflow: hidden;
    transform: rotate(-10deg);
    box-shadow: 0 0 15px rgba(255, 100, 255, 0.4);
    z-index: 10;
    animation: tube-pulse 3s infinite;
}

.tube-liquid {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 75%;
    background: linear-gradient(to bottom, rgba(255, 50, 150, 0.6), rgba(150, 50, 255, 0.8));
    animation: tube-colorShift 8s infinite alternate;
}

.reaction-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    filter: blur(10px);
    animation: glow-pulse 2s infinite;
}

.tube-bubbles {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 75%;
}

.tube-bubble {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: bubble-rise 4s infinite;
}

.tube-bubble:nth-child(1) {
    width: 10px;
    height: 10px;
    left: 20%;
    bottom: 10%;
    animation-delay: 0s;
}

.tube-bubble:nth-child(2) {
    width: 8px;
    height: 8px;
    left: 50%;
    bottom: 30%;
    animation-delay: 1s;
}

.tube-bubble:nth-child(3) {
    width: 12px;
    height: 12px;
    left: 30%;
    bottom: 50%;
    animation-delay: 2s;
}

/* 烧杯液体 */
.liquid {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70%;
    transition: all 1s ease;
}

.liquid-blue {
    background-color: rgba(0, 150, 255, 0.6);
    animation: liquid-bubbles 2s infinite, liquid-colorChange 10s infinite alternate;
}

.liquid-green {
    background-color: rgba(0, 200, 100, 0.6);
    animation: liquid-bubbles 3s infinite, liquid-colorChange2 10s infinite alternate;
}

/* 气泡 */
.bubbles {
    position: absolute;
    width: 100%;
    height: 70%;
    bottom: 0;
    left: 0;
}

.bubble {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    animation: bubble-rise 4s infinite;
}

.beaker-left .bubble:nth-child(1) { width: 8px; height: 8px; left: 10%; bottom: 5%; animation-delay: 0s; }
.beaker-left .bubble:nth-child(2) { width: 12px; height: 12px; left: 30%; bottom: 10%; animation-delay: 0.5s; }
.beaker-left .bubble:nth-child(3) { width: 6px; height: 6px; left: 50%; bottom: 15%; animation-delay: 1s; }
.beaker-left .bubble:nth-child(4) { width: 10px; height: 10px; left: 70%; bottom: 5%; animation-delay: 1.5s; }
.beaker-left .bubble:nth-child(5) { width: 7px; height: 7px; left: 20%; bottom: 20%; animation-delay: 2s; }
.beaker-left .bubble:nth-child(6) { width: 9px; height: 9px; left: 40%; bottom: 25%; animation-delay: 2.5s; }
.beaker-left .bubble:nth-child(7) { width: 11px; height: 11px; left: 60%; bottom: 15%; animation-delay: 3s; }
.beaker-left .bubble:nth-child(8) { width: 5px; height: 5px; left: 80%; bottom: 20%; animation-delay: 3.5s; }
.beaker-left .bubble:nth-child(9) { width: 8px; height: 8px; left: 25%; bottom: 30%; animation-delay: 4s; }
.beaker-left .bubble:nth-child(10) { width: 10px; height: 10px; left: 45%; bottom: 5%; animation-delay: 4.5s; }

.beaker-right .bubble:nth-child(1) { width: 10px; height: 10px; left: 15%; bottom: 10%; animation-delay: 0.2s; }
.beaker-right .bubble:nth-child(2) { width: 8px; height: 8px; left: 35%; bottom: 15%; animation-delay: 0.7s; }
.beaker-right .bubble:nth-child(3) { width: 12px; height: 12px; left: 55%; bottom: 5%; animation-delay: 1.2s; }
.beaker-right .bubble:nth-child(4) { width: 6px; height: 6px; left: 75%; bottom: 20%; animation-delay: 1.7s; }
.beaker-right .bubble:nth-child(5) { width: 9px; height: 9px; left: 25%; bottom: 25%; animation-delay: 2.2s; }
.beaker-right .bubble:nth-child(6) { width: 7px; height: 7px; left: 45%; bottom: 10%; animation-delay: 2.7s; }
.beaker-right .bubble:nth-child(7) { width: 11px; height: 11px; left: 65%; bottom: 15%; animation-delay: 3.2s; }
.beaker-right .bubble:nth-child(8) { width: 5px; height: 5px; left: 85%; bottom: 25%; animation-delay: 3.7s; }
.beaker-right .bubble:nth-child(9) { width: 8px; height: 8px; left: 30%; bottom: 5%; animation-delay: 4.2s; }
.beaker-right .bubble:nth-child(10) { width: 10px; height: 10px; left: 50%; bottom: 20%; animation-delay: 4.7s; }

/* 分子 */
.molecule {
    position: absolute;
    border-radius: 50%;
    opacity: 0.8;
    box-shadow: 0 0 10px currentColor;
    animation: molecule-float 6s infinite ease-in-out;
}

.beaker-left .molecule {
    background-color: rgba(0, 200, 255, 0.8);
    color: rgba(0, 200, 255, 0.8);
}

.beaker-right .molecule {
    background-color: rgba(100, 255, 100, 0.8);
    color: rgba(100, 255, 100, 0.8);
}

.molecule-1 {
    width: 15px;
    height: 15px;
    left: 20%;
    bottom: 20%;
    animation-duration: 5s;
}

.molecule-2 {
    width: 12px;
    height: 12px;
    left: 40%;
    bottom: 30%;
    animation-duration: 7s;
    animation-delay: 1s;
}

.molecule-3 {
    width: 18px;
    height: 18px;
    left: 60%;
    bottom: 25%;
    animation-duration: 6s;
    animation-delay: 2s;
}

.molecule-4 {
    width: 10px;
    height: 10px;
    left: 30%;
    bottom: 40%;
    animation-duration: 8s;
    animation-delay: 3s;
}

.molecule-5 {
    width: 14px;
    height: 14px;
    left: 70%;
    bottom: 15%;
    animation-duration: 9s;
    animation-delay: 4s;
}

/* 烧杯上方的烟雾 */
.smoke {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 40px;
}

.smoke-particle {
    position: absolute;
    width: 30px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: smoke-rise 5s infinite;
}

.smoke-particle:nth-child(1) {
    animation-delay: 0s;
    left: 10px;
}

.smoke-particle:nth-child(2) {
    animation-delay: 1s;
    left: 30px;
}

.smoke-particle:nth-child(3) {
    animation-delay: 2s;
    left: 50px;
}

.smoke-particle:nth-child(4) {
    animation-delay: 3s;
    left: 70px;
}

.smoke-particle:nth-child(5) {
    animation-delay: 4s;
    left: 90px;
}

/* 闪电效果 */
.lightning-container {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 3;
}

.lightning {
    position: absolute;
    top: 20%;
    width: 4px;
    height: 200px;
    background-color: rgba(255, 255, 255, 0.8);
    filter: blur(1px);
    animation: lightning-flash 10s infinite;
}

.lightning:nth-child(1) {
    left: 30%;
    animation-delay: 0s;
}

.lightning:nth-child(2) {
    right: 30%;
    animation-delay: 5s;
}

/* 分子连接线 */
.molecular-connections {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.connection {
    position: absolute;
    height: 2px;
    background: linear-gradient(to right, rgba(0, 200, 255, 0.5), rgba(100, 255, 100, 0.5));
    animation: connection-pulse 4s infinite;
}

.connection:nth-child(1) {
    width: 200px;
    top: 40%;
    left: 30%;
    transform: rotate(20deg);
    animation-delay: 0s;
}

.connection:nth-child(2) {
    width: 150px;
    top: 50%;
    left: 40%;
    transform: rotate(-15deg);
    animation-delay: 1s;
}

.connection:nth-child(3) {
    width: 180px;
    top: 60%;
    left: 35%;
    transform: rotate(5deg);
    animation-delay: 2s;
}

/* 动画关键帧定义 */
@keyframes beaker-shake {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(1deg); }
    50% { transform: rotate(-1deg); }
    75% { transform: rotate(1deg); }
}

@keyframes tube-pulse {
    0%, 100% { transform: rotate(-10deg) scale(1); }
    50% { transform: rotate(-10deg) scale(1.05); }
}

@keyframes tube-colorShift {
    0% { background: linear-gradient(to bottom, rgba(255, 50, 150, 0.6), rgba(150, 50, 255, 0.8)); }
    50% { background: linear-gradient(to bottom, rgba(50, 150, 255, 0.6), rgba(150, 50, 255, 0.8)); }
    100% { background: linear-gradient(to bottom, rgba(50, 255, 150, 0.6), rgba(100, 50, 255, 0.8)); }
}

@keyframes glow-pulse {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes bubble-rise {
    0% { transform: translateY(0) scale(1); opacity: 0.4; }
    100% { transform: translateY(-100px) scale(1.5); opacity: 0; }
}

@keyframes molecule-float {
    0% { transform: translate(0, 0); }
    25% { transform: translate(10px, -15px); }
    50% { transform: translate(0, -30px); }
    75% { transform: translate(-10px, -15px); }
    100% { transform: translate(0, 0); }
}

@keyframes smoke-rise {
    0% { transform: translateY(0) scale(1); opacity: 0.3; }
    100% { transform: translateY(-40px) scale(2); opacity: 0; }
}

@keyframes lightning-flash {
    0%, 100% { opacity: 0; transform: scaleY(0.5); }
    5%, 15% { opacity: 0.8; transform: scaleY(1); }
    10% { opacity: 0.4; }
    20% { opacity: 0; transform: scaleY(0.5); }
}

@keyframes connection-pulse {
    0%, 100% { opacity: 0.3; height: 2px; }
    50% { opacity: 0.7; height: 3px; }
}

@keyframes liquid-bubbles {
    0% { box-shadow: 0 0 0 rgba(255, 255, 255, 0.1) inset; }
    50% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.3) inset; }
    100% { box-shadow: 0 0 0 rgba(255, 255, 255, 0.1) inset; }
}

@keyframes liquid-colorChange {
    0% { background-color: rgba(0, 150, 255, 0.6); }
    50% { background-color: rgba(0, 100, 200, 0.6); }
    100% { background-color: rgba(50, 100, 255, 0.6); }
}

@keyframes liquid-colorChange2 {
    0% { background-color: rgba(0, 200, 100, 0.6); }
    50% { background-color: rgba(100, 200, 50, 0.6); }
    100% { background-color: rgba(50, 150, 100, 0.6); }
}
