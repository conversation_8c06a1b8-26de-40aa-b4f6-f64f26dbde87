// 留言页面功能
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const contactForm = document.getElementById('contact-form');
    const captchaImg = document.getElementById('captcha-img');
    const refreshCaptchaBtn = document.getElementById('refresh-captcha');
    
    // 验证码生成
    function generateCaptcha() {
        // 在实际应用中，这应该是从服务器获取的
        // 这里只是模拟一个简单的验证码
        const captchaText = Math.random().toString(36).substring(2, 8).toUpperCase();
        localStorage.setItem('captchaText', captchaText);
        
        // 使用placeholder.com生成一个带有验证码文本的图片
        captchaImg.src = `https://via.placeholder.com/120x40/CCCCCC/333333?text=${captchaText}`;
    }
    
    // 初始化验证码
    generateCaptcha();
    
    // 刷新验证码
    if (refreshCaptchaBtn) {
        refreshCaptchaBtn.addEventListener('click', function(e) {
            e.preventDefault();
            generateCaptcha();
        });
    }
    
    // 表单提交
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 获取表单数据
            const name = document.getElementById('name').value;
            const phone = document.getElementById('phone').value;
            const email = document.getElementById('email').value || '';
            const company = document.getElementById('company').value || '';
            const message = document.getElementById('message').value;
            const captcha = document.getElementById('captcha').value;
            
            // 验证验证码
            const storedCaptcha = localStorage.getItem('captchaText');
            if (captcha.toUpperCase() !== storedCaptcha) {
                alert('验证码错误，请重新输入！');
                document.getElementById('captcha').value = '';
                generateCaptcha();
                return;
            }
            
            // 创建留言对象
            const messageData = {
                id: Date.now(),
                name: name,
                phone: phone,
                email: email,
                company: company,
                message: message,
                date: new Date().toISOString(),
                status: 'unread'
            };
            
            // 保存到localStorage
            const messages = JSON.parse(localStorage.getItem('messages')) || [];
            messages.push(messageData);
            localStorage.setItem('messages', JSON.stringify(messages));
            
            // 发送短信通知
            if (window.smsService) {
                window.smsService.sendSmsNotification(window.smsService.SMS_TYPES.MESSAGE, messageData);
            }
            
            // 显示成功消息
            alert('感谢您的留言，我们会尽快与您联系！');
            
            // 重置表单和验证码
            contactForm.reset();
            generateCaptcha();
        });
    }
});
