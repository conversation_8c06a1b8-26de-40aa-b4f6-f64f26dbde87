<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 龙驰新材料</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header class="admin-header">
        <h1><img src="../images/logo.png" alt="龙驰新材料"> 龙驰新材料管理后台</h1>
        <nav class="admin-nav">
            <a href="dashboard.html" class="active"><i class="fas fa-tachometer-alt"></i> 控制面板</a>
            <a href="products.html"><i class="fas fa-box"></i> 产品管理</a>
            <a href="orders.html"><i class="fas fa-shopping-cart"></i> 订单管理</a>
            <a href="users.html"><i class="fas fa-users"></i> 用户管理</a>
            <a href="messages.html"><i class="fas fa-comments"></i> 留言管理</a>
            <a href="sms.html"><i class="fas fa-sms"></i> 短信记录</a>
            <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出</a>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-content">

            <div class="admin-card">
                <h2>系统概览</h2>
                <div class="table-responsive">
                    <table class="stats-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-box"></i> 产品总数</th>
                                <th><i class="fas fa-shopping-cart"></i> 订单总数</th>
                                <th><i class="fas fa-users"></i> 客户总数</th>
                                <th><i class="fas fa-comments"></i> 留言总数</th>
                                <th><i class="fas fa-sms"></i> 短信总数</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td id="product-count">9</td>
                                <td id="order-count">3</td>
                                <td id="customer-count">1</td>
                                <td id="message-count">1</td>
                                <td id="sms-count">2</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="admin-card">
                <h2>最近订单</h2>
                <div class="table-responsive">
                    <table class="product-table" id="recent-orders">
                        <thead>
                            <tr>
                                <th>订单编号</th>
                                <th>客户</th>
                                <th>日期</th>
                                <th>金额</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="empty-table">暂无订单数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="admin-card">
                <h2>最近留言</h2>
                <div class="table-responsive">
                    <table class="product-table" id="recent-messages">
                        <thead>
                            <tr>
                                <th>姓名</th>
                                <th>电话</th>
                                <th>留言内容</th>
                                <th>日期</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="empty-table">暂无留言数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="admin-card">
                <h2>最近短信</h2>
                <div class="table-responsive">
                    <table class="product-table" id="recent-sms">
                        <thead>
                            <tr>
                                <th>类型</th>
                                <th>接收人</th>
                                <th>标题</th>
                                <th>内容</th>
                                <th>发送时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="empty-table">暂无短信数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入API客户端 -->
    <script src="../js/api-client.js"></script>
    <!-- 引入管理后台脚本 -->
    <script src="admin.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>
