// 留言管理功能
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const messagesTable = document.getElementById('messages-table');
    const messageTable = document.getElementById('message-table'); // 新的表格ID
    const emptyMessages = document.getElementById('empty-messages');
    const messageSearch = document.getElementById('message-search');
    const searchBtn = document.getElementById('search-btn');
    const statusFilter = document.getElementById('status-filter');
    const messageModal = document.getElementById('message-modal');
    const messageDetail = document.getElementById('message-detail');
    const markReadBtn = document.getElementById('mark-read-btn');
    const markRepliedBtn = document.getElementById('mark-replied-btn');
    const deleteMessageBtn = document.getElementById('delete-message-btn');

    // 当前选中的留言ID
    let currentMessageId = null;

    // 留言数据
    let messages = [];

    // 初始化
    init();

    // 初始化函数
    async function init() {
        // 设置事件监听器
        setupEventListeners();

        // 加载留言数据
        await fetchMessages();
    }

    // 从API获取留言数据
    async function fetchMessages() {
        try {
            // 显示加载中提示
            messagesTable.innerHTML = '<tr><td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在加载留言数据...</td></tr>';

            // 从API获取留言数据
            const apiMessages = await ApiClient.getMessages();

            if (apiMessages && apiMessages.length > 0) {
                messages = apiMessages;
                loadMessages();
            } else {
                // 没有留言数据
                messagesTable.innerHTML = '';
                emptyMessages.style.display = 'flex';
            }
        } catch (error) {
            console.error('加载留言数据失败:', error);
            messagesTable.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载留言数据失败，请刷新页面重试</td></tr>';
        }
    }

    // 加载留言数据
    function loadMessages(filteredMessages = null) {
        // 使用过滤后的留言或所有留言
        const messagesToDisplay = filteredMessages || messages;

        // 清空表格
        messagesTable.innerHTML = '';

        // 检查是否有留言
        if (messagesToDisplay.length === 0) {
            emptyMessages.style.display = 'flex';
            return;
        }

        // 隐藏空留言提示
        emptyMessages.style.display = 'none';

        // 按时间倒序排序留言（最新的在前面）
        const sortedMessages = [...messagesToDisplay].sort((a, b) => new Date(b.date) - new Date(a.date));

        // 遍历留言并添加到表格
        sortedMessages.forEach(message => {
            // 格式化日期
            const messageDate = new Date(message.date);
            const formattedDate = `${messageDate.getFullYear()}-${(messageDate.getMonth() + 1).toString().padStart(2, '0')}-${messageDate.getDate().toString().padStart(2, '0')} ${messageDate.getHours().toString().padStart(2, '0')}:${messageDate.getMinutes().toString().padStart(2, '0')}`;

            // 创建表格行
            const tr = document.createElement('tr');

            // 设置状态样式
            let statusClass = '';
            let statusText = '';

            switch (message.status) {
                case 'unread':
                    statusClass = 'status-pending';
                    statusText = '未读';
                    break;
                case 'read':
                    statusClass = 'status-processing';
                    statusText = '已读';
                    break;
                case 'replied':
                    statusClass = 'status-completed';
                    statusText = '已回复';
                    break;
                default:
                    statusClass = 'status-pending';
                    statusText = '未读';
            }

            // 截断留言内容，如果太长
            const truncatedMessage = message.message.length > 30 ? message.message.substring(0, 30) + '...' : message.message;

            // 设置表格行内容
            tr.innerHTML = `
                <td>${message.id}</td>
                <td>${message.name}</td>
                <td>${message.phone}</td>
                <td>${truncatedMessage}</td>
                <td>${formattedDate}</td>
                <td><span class="status ${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn-icon view-message" data-id="${message.id}"><i class="fas fa-eye"></i></button>
                    <button class="btn-icon delete-message" data-id="${message.id}"><i class="fas fa-trash"></i></button>
                </td>
            `;

            // 如果是未读留言，添加高亮样式
            if (message.status === 'unread') {
                tr.classList.add('unread-row');
            }

            // 添加到表格
            messagesTable.appendChild(tr);
        });
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 搜索按钮点击事件
        if (searchBtn) {
            searchBtn.addEventListener('click', searchMessages);
        }

        // 搜索框回车事件
        if (messageSearch) {
            messageSearch.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    searchMessages();
                }
            });
        }

        // 状态筛选事件
        if (statusFilter) {
            statusFilter.addEventListener('change', searchMessages);
        }

        // 表格行点击事件（使用事件委托）
        if (messagesTable) {
            messagesTable.addEventListener('click', function(e) {
                // 查看留言
                if (e.target.classList.contains('view-message') || e.target.closest('.view-message')) {
                    const button = e.target.classList.contains('view-message') ? e.target : e.target.closest('.view-message');
                    const messageId = parseInt(button.getAttribute('data-id'));
                    openMessageModal(messageId);
                }

                // 删除留言
                if (e.target.classList.contains('delete-message') || e.target.closest('.delete-message')) {
                    const button = e.target.classList.contains('delete-message') ? e.target : e.target.closest('.delete-message');
                    const messageId = parseInt(button.getAttribute('data-id'));
                    if (confirm('确定要删除这条留言吗？')) {
                        deleteMessage(messageId);
                    }
                }
            });
        }

        // 关闭模态框
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', function() {
                messageModal.style.display = 'none';
            });
        });

        // 点击模态框外部关闭
        window.addEventListener('click', function(e) {
            if (e.target === messageModal) {
                messageModal.style.display = 'none';
            }
        });

        // 标记为已读按钮
        if (markReadBtn) {
            markReadBtn.addEventListener('click', function() {
                updateMessageStatus(currentMessageId, 'read');
            });
        }

        // 标记为已回复按钮
        if (markRepliedBtn) {
            markRepliedBtn.addEventListener('click', function() {
                updateMessageStatus(currentMessageId, 'replied');
            });
        }

        // 删除留言按钮
        if (deleteMessageBtn) {
            deleteMessageBtn.addEventListener('click', function() {
                if (confirm('确定要删除这条留言吗？')) {
                    deleteMessage(currentMessageId);
                    messageModal.style.display = 'none';
                }
            });
        }
    }

    // 搜索留言
    function searchMessages() {
        const searchTerm = messageSearch.value.toLowerCase();
        const statusValue = statusFilter.value;

        // 过滤留言
        let filteredMessages = messages;

        // 按状态过滤
        if (statusValue !== 'all') {
            filteredMessages = filteredMessages.filter(message => message.status === statusValue);
        }

        // 按搜索词过滤
        if (searchTerm) {
            filteredMessages = filteredMessages.filter(message =>
                message.name.toLowerCase().includes(searchTerm) ||
                message.phone.includes(searchTerm) ||
                message.message.toLowerCase().includes(searchTerm)
            );
        }

        // 加载过滤后的留言
        loadMessages(filteredMessages);
    }

    // 打开留言详情模态框
    function openMessageModal(messageId) {
        // 查找留言
        const message = messages.find(m => m.id === messageId);

        if (message) {
            // 保存当前留言ID
            currentMessageId = messageId;

            // 格式化日期
            const messageDate = new Date(message.date);
            const formattedDate = `${messageDate.getFullYear()}-${(messageDate.getMonth() + 1).toString().padStart(2, '0')}-${messageDate.getDate().toString().padStart(2, '0')} ${messageDate.getHours().toString().padStart(2, '0')}:${messageDate.getMinutes().toString().padStart(2, '0')}`;

            // 设置模态框内容
            messageDetail.innerHTML = `
                <div class="message-info">
                    <p><strong>留言ID:</strong> ${message.id}</p>
                    <p><strong>姓名:</strong> ${message.name}</p>
                    <p><strong>电话:</strong> ${message.phone}</p>
                    <p><strong>提交时间:</strong> ${formattedDate}</p>
                    <p><strong>状态:</strong>
                        <span class="status ${message.status === 'unread' ? 'status-pending' : message.status === 'read' ? 'status-processing' : 'status-completed'}">
                            ${message.status === 'unread' ? '未读' : message.status === 'read' ? '已读' : '已回复'}
                        </span>
                    </p>
                </div>
                <div class="message-content">
                    <h3>留言内容:</h3>
                    <p>${message.message}</p>
                </div>
            `;

            // 根据留言状态显示/隐藏按钮
            if (message.status === 'unread') {
                markReadBtn.style.display = 'block';
                markRepliedBtn.style.display = 'block';
            } else if (message.status === 'read') {
                markReadBtn.style.display = 'none';
                markRepliedBtn.style.display = 'block';
            } else {
                markReadBtn.style.display = 'none';
                markRepliedBtn.style.display = 'none';
            }

            // 显示模态框
            messageModal.style.display = 'block';

            // 如果是未读留言，自动标记为已读
            if (message.status === 'unread') {
                updateMessageStatus(messageId, 'read', false);
            }
        }
    }

    // 更新留言状态
    async function updateMessageStatus(messageId, status, reload = true) {
        try {
            // 查找留言索引
            const messageIndex = messages.findIndex(m => m.id === messageId);

            if (messageIndex !== -1) {
                // 更新状态
                messages[messageIndex].status = status;

                // 这里应该有API调用来更新留言状态
                // 由于我们没有实现这个API端点，暂时只在前端更新
                // 在实际应用中，应该添加类似 await ApiClient.updateMessageStatus(messageId, status) 的调用

                // 如果需要，重新加载留言列表
                if (reload) {
                    loadMessages();
                    messageModal.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('更新留言状态失败:', error);
            alert('更新留言状态失败，请重试');
        }
    }

    // 删除留言
    async function deleteMessage(messageId) {
        try {
            // 这里应该有API调用来删除留言
            // 由于我们没有实现这个API端点，暂时只在前端删除
            // 在实际应用中，应该添加类似 await ApiClient.deleteMessage(messageId) 的调用

            // 过滤掉要删除的留言
            messages = messages.filter(m => m.id !== messageId);

            // 重新加载留言列表
            loadMessages();
        } catch (error) {
            console.error('删除留言失败:', error);
            alert('删除留言失败，请重试');
        }
    }
});
