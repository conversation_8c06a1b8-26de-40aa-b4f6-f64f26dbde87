import http.server
import socketserver
import os

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # 更新 MIME 类型映射
        self.extensions_map = {
            '.html': 'text/html',
            '.htm': 'text/html',
            '.js': 'application/javascript',
            '.mjs': 'application/javascript',
            '.css': 'text/css',
            '.json': 'application/json',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.ico': 'image/x-icon',
            '.svg': 'image/svg+xml',
            '': 'application/octet-stream',
        }
        super().__init__(*args, **kwargs)

    def end_headers(self):
        # 添加 CORS 头
        self.send_header('Access-Control-Allow-Origin', '*')
        # 添加正确的 MIME 类型
        if self.path.endswith('.js'):
            self.send_header('Content-Type', 'application/javascript')
        elif self.path.endswith('.png'):
            self.send_header('Content-Type', 'image/png')
        # 禁用缓存
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

    def do_GET(self):
        try:
            # 如果请求根路径，默认返回 index.html
            if self.path == '/':
                self.path = '/index.html'
            
            # 获取文件的完整路径
            file_path = os.path.join(os.getcwd(), self.path.lstrip('/'))
            
            if os.path.exists(file_path):
                print(f"Serving: {self.path}")
                print(f"MIME type: {self.guess_type(self.path)}")
                return super().do_GET()
            else:
                print(f"File not found: {file_path}")
                self.send_error(404, f"File not found: {self.path}")
                
        except Exception as e:
            print(f"Error serving {self.path}: {str(e)}")
            self.send_error(500, f"Internal server error: {str(e)}")

def run_server(host="*************", port=9091):
    handler = CustomHTTPRequestHandler
    with socketserver.TCPServer((host, port), handler) as httpd:
        print(f"Serving at http://{host}:{port}/")
        print(f"Root directory: {os.getcwd()}")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nShutting down server...")
            httpd.server_close()

if __name__ == "__main__":
    run_server() 