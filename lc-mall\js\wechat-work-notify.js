/**
 * 企业微信通知服务
 * 用于发送订单通知到企业微信群
 */

// 企业微信机器人配置
const WECHAT_WORK_CONFIG = {
    webhookUrl: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d5b6e5ec-d39e-4e17-9bba-3edaa5e97c1d'
};

// 企业微信通知服务
const WeChatWorkNotify = {
    /**
     * 发送新订单通知
     * @param {Object} orderData - 订单数据
     * @returns {Promise<boolean>} - 发送结果
     */
    async sendOrderNotification(orderData) {
        try {
            const result = await this.sendMessage(orderData);
            console.log('企业微信通知发送结果:', result);
            return result.success;
        } catch (error) {
            console.error('发送企业微信通知失败:', error);
            return false;
        }
    },

    /**
     * 格式化订单消息
     * @param {Object} orderData - 订单数据
     * @returns {Object} - 格式化后的消息
     */
    formatOrderMessage(orderData) {
        const customer = orderData.customer || {};
        const items = orderData.items || [];
        const total = orderData.total || 0;

        // 格式化商品列表
        const itemsText = items.map(item =>
            `• ${item.name} × ${item.quantity} = ¥${(item.price * item.quantity).toFixed(2)}`
        ).join('\n');

        // 格式化时间
        const orderTime = new Date(orderData.date).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        const content = `🎉 **新订单通知**

📋 **订单信息**
订单号：${orderData.id}
下单时间：${orderTime}
支付方式：微信支付
订单状态：等待支付确认

👤 **客户信息**
姓名：${customer.name || '未提供'}
电话：${customer.phone || '未提供'}
公司：${customer.company || '未提供'}
职位：${customer.position || '未提供'}
微信：${customer.wechat || '未提供'}
地址：${customer.address || '未提供'}

🛒 **商品清单**
${itemsText}

💰 **订单金额**
总计：¥${total.toFixed(2)}

⚠️ **处理提醒**
客户已确认完成微信支付，请及时确认收款并处理订单。

🔗 **管理链接**
[查看订单详情](${window.location.origin}/lc-mall/admin/orders.html)`;

        return {
            msgtype: 'markdown',
            markdown: {
                content: content
            }
        };
    },

    /**
     * 发送消息到企业微信（通过后端API）
     * @param {Object} orderData - 订单数据
     * @returns {Promise<Object>} - 发送结果
     */
    async sendMessage(orderData) {
        try {
            const response = await fetch('/api/wechat-work/notify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(orderData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                return { success: true, message: result.message };
            } else {
                throw new Error(`企业微信通知失败: ${result.error}`);
            }
        } catch (error) {
            console.error('发送企业微信消息失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    },

    /**
     * 发送简单文本消息
     * @param {string} text - 文本内容
     * @returns {Promise<boolean>} - 发送结果
     */
    async sendTextMessage(text) {
        const message = {
            msgtype: 'text',
            text: {
                content: text
            }
        };

        const result = await this.sendMessage(message);
        return result.success;
    },

    /**
     * 发送支付确认通知
     * @param {Object} orderData - 订单数据
     * @returns {Promise<boolean>} - 发送结果
     */
    async sendPaymentConfirmation(orderData) {
        try {
            const result = await this.sendMessage(orderData);
            console.log('企业微信支付确认通知发送结果:', result);
            return result.success;
        } catch (error) {
            console.error('发送企业微信支付确认通知失败:', error);
            return false;
        }
    },

    /**
     * 测试企业微信连接
     * @returns {Promise<boolean>} - 测试结果
     */
    async testConnection() {
        try {
            const response = await fetch('/api/wechat-work/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            return result.success;
        } catch (error) {
            console.error('测试企业微信连接失败:', error);
            return false;
        }
    }
};

// 导出服务
window.WeChatWorkNotify = WeChatWorkNotify;
