<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>龙驰新材料 - 管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="admin.css">
    <style>
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: #2c3e50;
            color: #fff;
            padding: 20px 0;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid #3d5166;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .sidebar-header img {
            max-width: 150px;
            margin-bottom: 10px;
        }
        
        .sidebar-header h3 {
            margin: 0;
            font-size: 18px;
            color: #ecf0f1;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: #b8c7ce;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: #1a2530;
            color: #fff;
        }
        
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f4f6f9;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 20px;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 20px;
        }
        
        .header h2 {
            margin: 0;
            color: #333;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-info span {
            margin-right: 15px;
        }
        
        .logout-btn {
            background-color: #e74c3c;
            color: #fff;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .logout-btn:hover {
            background-color: #c0392b;
        }
        
        .content-section {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .content-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
        }
        
        .stat-card i {
            font-size: 30px;
            margin-bottom: 10px;
            color: #3498db;
        }
        
        .stat-card h4 {
            margin: 0 0 5px;
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .stat-card .number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .content-area {
            display: none;
        }
        
        .content-area.active {
            display: block;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        table th, table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .action-btn {
            background-color: #3498db;
            color: #fff;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
            transition: background-color 0.3s;
        }
        
        .action-btn:hover {
            background-color: #2980b9;
        }
        
        .action-btn.delete {
            background-color: #e74c3c;
        }
        
        .action-btn.delete:hover {
            background-color: #c0392b;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.png" alt="龙驰新材料">
                <h3>管理后台</h3>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="#dashboard" class="nav-link active" data-target="dashboard-content"><i class="fas fa-tachometer-alt"></i> 控制面板</a></li>
                <li><a href="#products" class="nav-link" data-target="products-content"><i class="fas fa-box"></i> 产品管理</a></li>
                <li><a href="#orders" class="nav-link" data-target="orders-content"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                <li><a href="#users" class="nav-link" data-target="users-content"><i class="fas fa-users"></i> 用户管理</a></li>
                <li><a href="#messages" class="nav-link" data-target="messages-content"><i class="fas fa-envelope"></i> 留言管理</a></li>
                <li><a href="#product-intro" class="nav-link" data-target="product-intro-content"><i class="fas fa-info-circle"></i> 产品目录</a></li>
            </ul>
        </div>
        
        <div class="main-content">
            <div class="header">
                <h2>控制面板</h2>
                <div class="user-info">
                    <span>欢迎，<span id="admin-username">管理员</span></span>
                    <button id="logout-btn" class="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出</button>
                </div>
            </div>
            
            <!-- 控制面板内容 -->
            <div id="dashboard-content" class="content-area active">
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <i class="fas fa-box"></i>
                        <h4>产品总数</h4>
                        <div class="number" id="product-count">0</div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-shopping-cart"></i>
                        <h4>订单总数</h4>
                        <div class="number" id="order-count">0</div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-users"></i>
                        <h4>用户总数</h4>
                        <div class="number" id="user-count">0</div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-envelope"></i>
                        <h4>留言总数</h4>
                        <div class="number" id="message-count">0</div>
                    </div>
                </div>
                
                <div class="content-section">
                    <h3>最近订单</h3>
                    <table id="recent-orders">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>客户</th>
                                <th>日期</th>
                                <th>金额</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 最近订单数据将通过JavaScript加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 产品介绍内容 -->
            <div id="product-intro-content" class="content-area">
                <div class="content-section">
                    <h3>产品介绍管理</h3>
                    <p>在这里您可以编辑产品介绍页面的内容。</p>
                    
                    <div class="product-intro-tabs">
                        <button class="tab-btn active" data-target="category-tab">产品类别</button>
                        <button class="tab-btn" data-target="table-tab">产品表格</button>
                    </div>
                    
                    <div id="category-tab" class="tab-content active">
                        <h4>产品类别编辑</h4>
                        <div class="category-list">
                            <div class="category-item">
                                <h5>潜固化剂系列</h5>
                                <button class="action-btn edit-category" data-id="category-1">编辑</button>
                            </div>
                            <div class="category-item">
                                <h5>功能助剂系列</h5>
                                <button class="action-btn edit-category" data-id="category-2">编辑</button>
                            </div>
                            <div class="category-item">
                                <h5>生物基多元醇系列</h5>
                                <button class="action-btn edit-category" data-id="category-3">编辑</button>
                            </div>
                            <div class="category-item">
                                <h5>除水剂活化粉系列</h5>
                                <button class="action-btn edit-category" data-id="category-4">编辑</button>
                            </div>
                        </div>
                    </div>
                    
                    <div id="table-tab" class="tab-content">
                        <h4>产品表格编辑</h4>
                        <div class="table-list">
                            <div class="table-item">
                                <h5>潜固化剂系列产品</h5>
                                <button class="action-btn edit-table" data-id="product-details-1">编辑表格</button>
                            </div>
                            <div class="table-item">
                                <h5>功能助剂系列产品</h5>
                                <button class="action-btn edit-table" data-id="product-details-2">编辑表格</button>
                            </div>
                            <div class="table-item">
                                <h5>生物基多元醇系列产品</h5>
                                <button class="action-btn edit-table" data-id="product-details-3">编辑表格</button>
                            </div>
                            <div class="table-item">
                                <h5>除水剂活化粉系列产品</h5>
                                <button class="action-btn edit-table" data-id="product-details-4">编辑表格</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/api-client.js"></script>
    <script src="admin.js"></script>
    <script>
        // 检查管理员登录状态
        function checkAdminLogin() {
            const isLoggedIn = localStorage.getItem('adminLoggedIn');
            if (!isLoggedIn) {
                window.location.href = 'login.html';
            } else {
                const username = localStorage.getItem('adminUsername');
                document.getElementById('admin-username').textContent = username || '管理员';
            }
        }
        
        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAdminLogin();
            
            // 加载统计数据
            loadDashboardStats();
            
            // 导航菜单切换
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有活动状态
                    navLinks.forEach(item => item.classList.remove('active'));
                    document.querySelectorAll('.content-area').forEach(area => area.classList.remove('active'));
                    
                    // 添加当前活动状态
                    this.classList.add('active');
                    const targetId = this.getAttribute('data-target');
                    document.getElementById(targetId).classList.add('active');
                    
                    // 更新标题
                    document.querySelector('.header h2').textContent = this.textContent.trim();
                });
            });
            
            // 退出登录
            document.getElementById('logout-btn').addEventListener('click', function() {
                localStorage.removeItem('adminLoggedIn');
                localStorage.removeItem('adminUsername');
                window.location.href = 'login.html';
            });
            
            // 产品介绍标签页切换
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    tabBtns.forEach(item => item.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
                    
                    this.classList.add('active');
                    const targetId = this.getAttribute('data-target');
                    document.getElementById(targetId).classList.add('active');
                });
            });
            
            // 编辑产品类别
            document.querySelectorAll('.edit-category').forEach(btn => {
                btn.addEventListener('click', function() {
                    const categoryId = this.getAttribute('data-id');
                    window.location.href = '../product-intro.html?edit=category&id=' + categoryId;
                });
            });
            
            // 编辑产品表格
            document.querySelectorAll('.edit-table').forEach(btn => {
                btn.addEventListener('click', function() {
                    const tableId = this.getAttribute('data-id');
                    window.location.href = '../product-intro.html?edit=table&id=' + tableId;
                });
            });
        });
        
        // 加载控制面板统计数据
        function loadDashboardStats() {
            // 从API获取统计数据
            fetch('/api/admin/stats')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to load stats');
                    }
                    return response.json();
                })
                .then(data => {
                    // 更新统计数据
                    document.getElementById('product-count').textContent = data.productCount || 0;
                    document.getElementById('order-count').textContent = data.orderCount || 0;
                    document.getElementById('user-count').textContent = data.userCount || 0;
                    document.getElementById('message-count').textContent = data.messageCount || 0;
                    
                    // 加载最近订单
                    loadRecentOrders(data.recentOrders || []);
                })
                .catch(error => {
                    console.error('Error loading dashboard stats:', error);
                });
        }
        
        // 加载最近订单
        function loadRecentOrders(orders) {
            const tableBody = document.querySelector('#recent-orders tbody');
            tableBody.innerHTML = '';
            
            if (orders.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="5" style="text-align: center;">暂无订单数据</td>';
                tableBody.appendChild(row);
                return;
            }
            
            orders.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.orderNumber}</td>
                    <td>${order.customer}</td>
                    <td>${order.date}</td>
                    <td>¥${order.amount.toFixed(2)}</td>
                    <td>${getStatusBadge(order.status)}</td>
                `;
                tableBody.appendChild(row);
            });
        }
        
        // 获取状态标签
        function getStatusBadge(status) {
            let badgeClass = '';
            let statusText = '';
            
            switch (status) {
                case 'pending':
                    badgeClass = 'badge-warning';
                    statusText = '待处理';
                    break;
                case 'processing':
                    badgeClass = 'badge-info';
                    statusText = '处理中';
                    break;
                case 'completed':
                    badgeClass = 'badge-success';
                    statusText = '已完成';
                    break;
                case 'cancelled':
                    badgeClass = 'badge-danger';
                    statusText = '已取消';
                    break;
                default:
                    badgeClass = 'badge-secondary';
                    statusText = '未知';
            }
            
            return `<span class="badge ${badgeClass}">${statusText}</span>`;
        }
    </script>
</body>
</html>
