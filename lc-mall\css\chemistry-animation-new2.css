/* 化学符号 */
.chemical-symbols {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 4;
}

.symbol {
    position: absolute;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 0 10px currentColor;
    animation: symbol-float 15s infinite alternate;
}

.symbol-h2o {
    top: 15%;
    left: 15%;
    font-size: 30px;
    color: rgba(0, 200, 255, 0.8);
    animation-delay: 0s;
}

.symbol-co2 {
    top: 25%;
    right: 20%;
    font-size: 28px;
    color: rgba(255, 100, 100, 0.8);
    animation-delay: 2s;
}

.symbol-nh3 {
    bottom: 35%;
    left: 25%;
    font-size: 32px;
    color: rgba(150, 100, 255, 0.8);
    animation-delay: 4s;
}

.symbol-o2 {
    bottom: 25%;
    right: 15%;
    font-size: 34px;
    color: rgba(100, 255, 100, 0.8);
    animation-delay: 6s;
}

.symbol-formula {
    font-size: 24px;
    animation-delay: 8s;
}

.symbol-formula:nth-of-type(5) {
    top: 40%;
    left: 40%;
    color: rgba(255, 200, 0, 0.8);
}

.symbol-formula:nth-of-type(6) {
    bottom: 40%;
    right: 40%;
    color: rgba(255, 100, 200, 0.8);
}

/* 背景粒子 */
.particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
    animation: particle-float 25s infinite linear;
}

/* 创建多个粒子 */
.particle:nth-child(1) { left: 5%; top: 10%; animation-duration: 20s; animation-delay: 0s; }
.particle:nth-child(2) { left: 15%; top: 30%; animation-duration: 22s; animation-delay: 1s; }
.particle:nth-child(3) { left: 25%; top: 50%; animation-duration: 24s; animation-delay: 2s; }
.particle:nth-child(4) { left: 35%; top: 70%; animation-duration: 21s; animation-delay: 3s; }
.particle:nth-child(5) { left: 45%; top: 20%; animation-duration: 23s; animation-delay: 4s; }
.particle:nth-child(6) { left: 55%; top: 40%; animation-duration: 25s; animation-delay: 5s; }
.particle:nth-child(7) { left: 65%; top: 60%; animation-duration: 26s; animation-delay: 6s; }
.particle:nth-child(8) { left: 75%; top: 80%; animation-duration: 19s; animation-delay: 7s; }
.particle:nth-child(9) { left: 85%; top: 15%; animation-duration: 27s; animation-delay: 8s; }
.particle:nth-child(10) { left: 10%; top: 35%; animation-duration: 23s; animation-delay: 9s; }
.particle:nth-child(11) { left: 20%; top: 55%; animation-duration: 21s; animation-delay: 10s; }
.particle:nth-child(12) { left: 30%; top: 75%; animation-duration: 24s; animation-delay: 11s; }
.particle:nth-child(13) { left: 40%; top: 25%; animation-duration: 22s; animation-delay: 12s; }
.particle:nth-child(14) { left: 50%; top: 45%; animation-duration: 20s; animation-delay: 13s; }
.particle:nth-child(15) { left: 60%; top: 65%; animation-duration: 25s; animation-delay: 14s; }
.particle:nth-child(16) { left: 70%; top: 85%; animation-duration: 23s; animation-delay: 15s; }
.particle:nth-child(17) { left: 80%; top: 5%; animation-duration: 26s; animation-delay: 16s; }
.particle:nth-child(18) { left: 90%; top: 25%; animation-duration: 22s; animation-delay: 17s; }
.particle:nth-child(19) { left: 15%; top: 45%; animation-duration: 24s; animation-delay: 18s; }
.particle:nth-child(20) { left: 25%; top: 65%; animation-duration: 21s; animation-delay: 19s; }

/* 动画定义 */
@keyframes float {
    0% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-60px) scale(1.2);
    }
    100% {
        transform: translateY(0) scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: rotate(0deg) scale(1);
    }
    25% {
        transform: rotate(2deg) scale(1.02);
    }
    50% {
        transform: rotate(0deg) scale(1);
    }
    75% {
        transform: rotate(-2deg) scale(1.02);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: rotate(-10deg) scale(1);
    }
    50% {
        transform: rotate(-8deg) scale(1.05);
    }
}

@keyframes bubbles {
    0% {
        box-shadow: 0 0 0 rgba(255, 255, 255, 0.1) inset;
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.4) inset;
    }
    100% {
        box-shadow: 0 0 0 rgba(255, 255, 255, 0.1) inset;
    }
}

@keyframes colorChange {
    0% {
        background-color: rgba(0, 150, 255, 0.6);
    }
    33% {
        background-color: rgba(0, 100, 200, 0.6);
    }
    66% {
        background-color: rgba(50, 100, 255, 0.6);
    }
    100% {
        background-color: rgba(100, 150, 255, 0.6);
    }
}

@keyframes colorChange2 {
    0% {
        background-color: rgba(0, 200, 100, 0.6);
    }
    33% {
        background-color: rgba(100, 200, 50, 0.6);
    }
    66% {
        background-color: rgba(50, 150, 100, 0.6);
    }
    100% {
        background-color: rgba(100, 255, 150, 0.6);
    }
}

@keyframes colorShift {
    0% {
        background: linear-gradient(to bottom, rgba(255, 50, 150, 0.6), rgba(150, 50, 255, 0.8));
    }
    50% {
        background: linear-gradient(to bottom, rgba(150, 50, 255, 0.8), rgba(50, 150, 255, 0.6));
    }
    100% {
        background: linear-gradient(to bottom, rgba(50, 150, 255, 0.6), rgba(255, 50, 150, 0.6));
    }
}

@keyframes smoke {
    0% {
        transform: translateY(0) scale(1);
        opacity: 0.5;
    }
    100% {
        transform: translateY(-80px) scale(3);
        opacity: 0;
    }
}

@keyframes bubble-rise {
    0% {
        transform: translateY(0) scale(1);
        opacity: 0.7;
    }
    100% {
        transform: translateY(-100px) scale(1.5);
        opacity: 0;
    }
}

@keyframes rise {
    0% {
        transform: translateY(0);
        opacity: 0.7;
    }
    100% {
        transform: translateY(-150px);
        opacity: 0;
    }
}

@keyframes symbol-float {
    0% {
        transform: translate(0, 0) rotate(0deg);
        opacity: 0.3;
    }
    25% {
        transform: translate(10px, -10px) rotate(5deg);
        opacity: 0.8;
    }
    50% {
        transform: translate(0, -20px) rotate(0deg);
        opacity: 1;
    }
    75% {
        transform: translate(-10px, -10px) rotate(-5deg);
        opacity: 0.8;
    }
    100% {
        transform: translate(0, 0) rotate(0deg);
        opacity: 0.3;
    }
}

@keyframes particle-float {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(30px, 30px);
    }
    50% {
        transform: translate(0, 60px);
    }
    75% {
        transform: translate(-30px, 30px);
    }
    100% {
        transform: translate(0, 0);
    }
}

@keyframes glow-pulse {
    0%, 100% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.5);
    }
}

@keyframes lightning {
    0%, 100% {
        opacity: 0;
        transform: scaleY(0.5) translateY(-50%);
    }
    2%, 5% {
        opacity: 1;
        transform: scaleY(1) translateY(0);
    }
    7% {
        opacity: 0;
        transform: scaleY(0.5) translateY(-50%);
    }
}

@keyframes connection-pulse {
    0%, 100% {
        opacity: 0.3;
        height: 2px;
    }
    50% {
        opacity: 0.8;
        height: 3px;
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chemistry-animation {
        height: 500px;
    }
    
    .beaker {
        width: 100px;
        height: 130px;
    }
    
    .test-tube {
        width: 40px;
        height: 150px;
    }
    
    .symbol {
        font-size: 20px !important;
    }
}

@media (max-width: 480px) {
    .chemistry-animation {
        height: 400px;
    }
    
    .beaker {
        width: 80px;
        height: 100px;
    }
    
    .test-tube {
        width: 30px;
        height: 120px;
    }
}
