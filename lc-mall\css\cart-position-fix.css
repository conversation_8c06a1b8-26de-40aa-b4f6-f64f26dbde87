/* 购物车位置修复样式 */

/* 顶部导航栏购物车位置 */
.cart-icon {
    position: relative;
    margin-left: 15px !important; /* 减小与用户状态区域的间距 */
    margin-right: 0 !important;
    transition: transform 0.3s ease;
}

/* 确保购物车图标正确显示 */
.cart-icon a {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
    transition: all 0.2s ease !important;
}

.cart-icon a:hover {
    background-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
}

/* 购物车数量标签 */
#cart-count {
    position: absolute !important;
    top: -5px !important;
    right: -5px !important;
    background-color: var(--accent-color) !important;
    color: white !important;
    font-size: 0.7rem !important;
    font-weight: 600 !important;
    min-width: 20px !important;
    height: 20px !important;
    border-radius: 50% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    padding: 0 4px !important;
    z-index: 101 !important;
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .cart-icon {
        margin-left: 10px !important;
    }
    
    .cart-icon a {
        width: 36px !important;
        height: 36px !important;
    }
    
    #cart-count {
        min-width: 18px !important;
        height: 18px !important;
        font-size: 0.65rem !important;
    }
    
    /* 在移动设备上调整顺序 */
    .header-container {
        display: flex !important;
        flex-wrap: wrap !important;
        justify-content: space-between !important;
        align-items: center !important;
    }
    
    nav {
        order: 1 !important;
        flex: 1 0 100% !important;
    }
    
    .user-status {
        order: 2 !important;
    }
    
    .cart-icon {
        order: 3 !important;
    }
    
    .mobile-nav-toggle {
        order: 4 !important;
    }
}
