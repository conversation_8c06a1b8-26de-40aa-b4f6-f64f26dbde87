// Products management functionality
document.addEventListener('DOMContentLoaded', async function() {
    // DOM Elements
    const productTable = document.getElementById('product-table');
    const productModal = document.getElementById('product-modal');
    const deleteModal = document.getElementById('delete-modal');
    const productForm = document.getElementById('product-form');
    const addProductBtn = document.getElementById('add-product-btn');
    const cancelBtn = document.getElementById('cancel-btn');
    const deleteConfirmBtn = document.getElementById('confirm-delete-btn');
    const deleteCancelBtn = document.getElementById('delete-cancel-btn');
    const productSearch = document.getElementById('product-search');
    const searchBtn = document.getElementById('search-btn');

    // 初始化商品数据
    let products = [];

    // Variables
    let currentProductId = null;
    let productToDelete = null;
    let formModified = false; // 跟踪表单是否被修改
    const FORM_STORAGE_KEY = 'productFormData'; // localStorage的键名

    // 显示加载中提示
    const tbody = productTable.querySelector('tbody');
    tbody.innerHTML = '<tr><td colspan="8" class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在加载商品数据...</td></tr>';

    // 从API获取商品数据
    try {
        const apiProducts = await ApiClient.getProducts();
        console.log('Products loaded from API:', apiProducts);

        if (apiProducts && apiProducts.length > 0) {
            // 更新商品数据
            products = apiProducts;

            // 显示商品
            displayProducts(products);
            console.log('Products loaded from API');

            // 显示成功提示
            showNotification('商品数据已加载', 'success');
        } else {
            // 没有商品数据
            tbody.innerHTML = '<tr><td colspan="8" class="text-center">没有商品数据</td></tr>';
        }
    } catch (error) {
        console.error('加载商品数据失败:', error);
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">加载商品数据失败，请刷新页面重试</td></tr>';
        showNotification('加载商品数据失败，请刷新页面重试', 'error');
    }

    // 获取图片上传相关元素
    const uploadImageBtn = document.getElementById('upload-image-btn');
    const imageFileInput = document.getElementById('image-file');
    const usePlaceholderBtn = document.getElementById('use-placeholder-btn');
    const previewImg = document.getElementById('preview-img');
    const productImageInput = document.getElementById('product-image');

    // Event Listeners
    addProductBtn.addEventListener('click', openAddProductModal);
    cancelBtn.addEventListener('click', closeProductModal);
    productForm.addEventListener('submit', saveProduct);
    deleteConfirmBtn.addEventListener('click', confirmDeleteProduct);
    deleteCancelBtn.addEventListener('click', closeDeleteModal);
    searchBtn.addEventListener('click', searchProducts);
    productSearch.addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            searchProducts();
        }
    });

    // 图片上传相关事件监听
    uploadImageBtn.addEventListener('click', function() {
        imageFileInput.click();
    });

    // 压缩进度相关元素
    const compressionProgress = document.getElementById('compression-progress');
    const progressBarFill = document.getElementById('progress-bar-fill');
    const progressText = document.getElementById('progress-text');

    // 显示压缩进度
    function showCompressionProgress(progress) {
        compressionProgress.style.display = 'block';
        progressBarFill.style.width = `${progress}%`;
        progressText.textContent = `压缩中: ${Math.round(progress)}%`;
    }

    // 隐藏压缩进度
    function hideCompressionProgress() {
        setTimeout(() => {
            compressionProgress.style.display = 'none';
            progressBarFill.style.width = '0%';
        }, 1000);
    }

    imageFileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // 显示压缩进度条
            showCompressionProgress(0);

            // 使用图片压缩工具压缩图片
            ImageCompressor.compressFile(file, {
                maxWidth: 1200,
                maxHeight: 1200,
                quality: 0.8,
                maxSizeMB: 1,
                onProgress: showCompressionProgress
            }).then(compressedDataUrl => {
                // 更新预览图片和表单值
                previewImg.src = compressedDataUrl;
                productImageInput.value = compressedDataUrl;

                // 隐藏压缩进度条
                hideCompressionProgress();

                // 显示成功提示
                showNotification('图片已压缩并准备好上传', 'success');
            }).catch(error => {
                console.error('图片压缩失败:', error);

                // 隐藏压缩进度条
                hideCompressionProgress();

                // 显示错误提示
                showNotification('图片压缩失败，请重试', 'error');

                // 使用原始图片作为备选
                const reader = new FileReader();
                reader.onload = function(event) {
                    previewImg.src = event.target.result;
                    productImageInput.value = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        }
    });

    usePlaceholderBtn.addEventListener('click', function() {
        const placeholderUrl = '../images/placeholder/product-placeholder.svg';
        previewImg.src = placeholderUrl;
        productImageInput.value = placeholderUrl;
        formModified = true; // 标记表单已修改
    });

    // 图片URL输入框变化时更新预览
    productImageInput.addEventListener('input', function() {
        if (this.value) {
            previewImg.src = this.value;
        }
        formModified = true; // 标记表单已修改
    });

    // 监听所有表单输入，标记表单已修改并保存到localStorage
    productForm.addEventListener('input', function() {
        formModified = true;
        saveFormToLocalStorage();
    });

    // 监听图片文件上传，标记表单已修改并保存到localStorage
    imageFileInput.addEventListener('change', function() {
        formModified = true;
        saveFormToLocalStorage();
    });

    // 添加页面离开确认
    window.addEventListener('beforeunload', function(e) {
        if (formModified && productModal.style.display === 'block') {
            // 显示标准的浏览器确认对话框
            e.preventDefault();
            e.returnValue = '您有未保存的更改，确定要离开吗？';
            return e.returnValue;
        }
    });

    // 移除点击模态框背景关闭的功能，只能通过关闭按钮关闭

    // Functions

    // 保存表单数据到localStorage
    function saveFormToLocalStorage() {
        if (productModal.style.display !== 'block') return;

        try {
            const formData = {
                id: document.getElementById('product-id').value,
                name: document.getElementById('product-name').value,
                category: document.getElementById('product-category').value,
                price: document.getElementById('product-price').value,
                // 不保存base64图片数据到localStorage，避免超出配额
                image: document.getElementById('product-image').value.startsWith('data:') ?
                       '图片已上传但未保存到缓存' :
                       document.getElementById('product-image').value,
                // 产品描述字段已移除
                specifications: document.getElementById('product-specifications').value,
                sku: document.getElementById('product-sku').value,
                // 库存字段已移除
                featured: document.getElementById('product-featured').value,

                // 产品详细信息
                model: document.getElementById('product-model').value,
                appearance: document.getElementById('product-appearance').value,
                standard: document.getElementById('product-standard').value,
                usage: document.getElementById('product-usage').value,
                flashpoint: document.getElementById('product-flashpoint').value,
                smell: document.getElementById('product-smell').value,
                function: document.getElementById('product-function').value,
                amineValue: document.getElementById('product-amine-value').value,
                content: document.getElementById('product-content').value,
                imported: document.getElementById('product-imported').value,
                density: document.getElementById('product-density').value,
                color: document.getElementById('product-color').value,
                viscosity: document.getElementById('product-viscosity').value,

                // 产品说明书
                intro: document.getElementById('product-intro').value,
                usageList: document.getElementById('product-usage-list').value,
                dosageList: document.getElementById('product-dosage-list').value,

                // 保存时间戳
                timestamp: new Date().getTime(),

                // 不保存预览图片，可能会导致localStorage超出配额
                // 只记录是否使用了占位图
                isPlaceholder: document.getElementById('preview-img').src.includes('product-placeholder.svg'),

                // 保存模态框标题
                modalTitle: document.getElementById('modal-title').textContent,

                // 保存当前产品ID
                currentProductId: currentProductId
            };

            try {
                localStorage.setItem(FORM_STORAGE_KEY, JSON.stringify(formData));
            } catch (storageError) {
                // 如果存储失败，尝试移除图片相关数据后再次保存
                console.warn('localStorage存储失败，尝试移除图片数据后重试');
                delete formData.image;
                delete formData.isPlaceholder;
                localStorage.setItem(FORM_STORAGE_KEY, JSON.stringify(formData));
            }
        } catch (error) {
            console.error('保存表单数据到localStorage失败:', error);
        }
    }

    // 从localStorage加载表单数据
    function loadFormFromLocalStorage() {
        try {
            const savedData = localStorage.getItem(FORM_STORAGE_KEY);
            if (!savedData) return false;

            const formData = JSON.parse(savedData);

            // 检查数据是否过期（24小时）
            const now = new Date().getTime();
            const savedTime = formData.timestamp || 0;
            const hoursPassed = (now - savedTime) / (1000 * 60 * 60);

            if (hoursPassed > 24) {
                // 数据已过期，清除
                localStorage.removeItem(FORM_STORAGE_KEY);
                return false;
            }

            // 填充表单
            if (formData.id) document.getElementById('product-id').value = formData.id;
            if (formData.name) document.getElementById('product-name').value = formData.name;
            if (formData.category) document.getElementById('product-category').value = formData.category;
            if (formData.price) document.getElementById('product-price').value = formData.price;
            if (formData.image) document.getElementById('product-image').value = formData.image;
            // 产品描述字段已移除
            if (formData.specifications) document.getElementById('product-specifications').value = formData.specifications;
            if (formData.sku) document.getElementById('product-sku').value = formData.sku;
            // 库存字段已移除
            if (formData.featured) document.getElementById('product-featured').value = formData.featured;

            // 产品详细信息
            if (formData.model) document.getElementById('product-model').value = formData.model;
            if (formData.appearance) document.getElementById('product-appearance').value = formData.appearance;
            if (formData.standard) document.getElementById('product-standard').value = formData.standard;
            if (formData.usage) document.getElementById('product-usage').value = formData.usage;
            if (formData.flashpoint) document.getElementById('product-flashpoint').value = formData.flashpoint;
            if (formData.smell) document.getElementById('product-smell').value = formData.smell;
            if (formData.function) document.getElementById('product-function').value = formData.function;
            if (formData.amineValue) document.getElementById('product-amine-value').value = formData.amineValue;
            if (formData.content) document.getElementById('product-content').value = formData.content;
            if (formData.imported) document.getElementById('product-imported').value = formData.imported;
            if (formData.density) document.getElementById('product-density').value = formData.density;
            if (formData.color) document.getElementById('product-color').value = formData.color;
            if (formData.viscosity) document.getElementById('product-viscosity').value = formData.viscosity;

            // 产品说明书
            if (formData.intro) document.getElementById('product-intro').value = formData.intro;
            if (formData.specs) document.getElementById('product-specs').value = formData.specs;
            if (formData.usageList) document.getElementById('product-usage-list').value = formData.usageList;

            // 设置预览图片
            if (formData.isPlaceholder) {
                document.getElementById('preview-img').src = '../images/placeholder/product-placeholder.svg';
            } else if (formData.image && !formData.image.startsWith('data:')) {
                document.getElementById('preview-img').src = formData.image;
            }

            // 设置模态框标题
            if (formData.modalTitle) document.getElementById('modal-title').textContent = formData.modalTitle;

            // 设置当前产品ID
            currentProductId = formData.currentProductId;

            // 标记表单为已修改
            formModified = true;

            return true;
        } catch (error) {
            console.error('从localStorage加载表单数据失败:', error);
            return false;
        }
    }

    // 清除localStorage中的表单数据
    function clearFormLocalStorage() {
        localStorage.removeItem(FORM_STORAGE_KEY);
    }

    function displayProducts(productsToDisplay) {
        const tbody = productTable.querySelector('tbody');
        tbody.innerHTML = '';

        if (productsToDisplay.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="empty-table">暂无产品数据</td></tr>';
            return;
        }

        productsToDisplay.forEach(product => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td><img src="${product.image}" alt="${product.name}" class="product-image-small"></td>
                <td>${product.name}</td>
                <td>${product.category}</td>
                <td>${product.sku || '-'}</td>
                <td>¥${product.price.toFixed(2)}</td>
                <td>${product.featured ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'}</td>
                <td class="table-actions">
                    <button class="edit-btn" data-id="${product.id}" title="编辑"><i class="fas fa-edit"></i></button>
                    <button class="delete-btn" data-id="${product.id}" title="删除"><i class="fas fa-trash"></i></button>
                </td>
            `;
            tbody.appendChild(tr);
        });

        // Add event listeners to edit and delete buttons
        document.querySelectorAll('.edit-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = parseInt(this.getAttribute('data-id'));
                openEditProductModal(productId);
            });
        });

        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = parseInt(this.getAttribute('data-id'));
                openDeleteModal(productId);
            });
        });
    }

    function openAddProductModal() {
        // 检查是否有保存的表单数据
        const hasSavedData = loadFormFromLocalStorage();

        if (hasSavedData) {
            // 创建自定义确认对话框
            const confirmDialog = document.createElement('div');
            confirmDialog.className = 'confirm-dialog';
            confirmDialog.innerHTML = `
                <div class="confirm-dialog-content">
                    <p>发现您之前未保存的产品数据，是否要恢复？</p>
                    <div class="confirm-dialog-buttons">
                        <button class="confirm-btn">确定</button>
                        <button class="cancel-btn">取消</button>
                    </div>
                </div>
            `;
            document.body.appendChild(confirmDialog);

            // 添加按钮事件
            const confirmBtn = confirmDialog.querySelector('.confirm-btn');
            const cancelBtn = confirmDialog.querySelector('.cancel-btn');

            confirmBtn.addEventListener('click', function() {
                // 数据已经在loadFormFromLocalStorage中加载，无需额外操作
                document.body.removeChild(confirmDialog);

                // Show modal with animation
                productModal.style.display = 'block';
                setTimeout(() => {
                    productModal.classList.add('show');
                }, 10);
            });

            cancelBtn.addEventListener('click', function() {
                // 用户选择不恢复，清除保存的数据
                clearFormLocalStorage();
                document.body.removeChild(confirmDialog);

                // 继续打开空白表单
                resetAndOpenForm();
            });

            return;
        }

        // 如果没有保存的数据，直接打开空白表单
        resetAndOpenForm();
    }

    // 重置表单并打开
    function resetAndOpenForm() {
        // 重置表单
        productForm.reset();
        document.getElementById('product-id').value = '';
        document.getElementById('modal-title').textContent = '添加产品';
        document.getElementById('preview-img').src = '../images/placeholder/product-placeholder.svg';
        currentProductId = null;

        // 库存字段默认值设置已移除

        // Show modal with animation
        productModal.style.display = 'block';
        setTimeout(() => {
            productModal.classList.add('show');
        }, 10);

        // 标记表单为未修改状态
        formModified = false;
    }

    function openEditProductModal(productId) {
        const product = products.find(p => p.id === productId);

        if (product) {
            // Fill form with product data
            document.getElementById('product-id').value = product.id;
            document.getElementById('product-name').value = product.name;
            document.getElementById('product-category').value = product.category;
            document.getElementById('product-price').value = product.price;
            document.getElementById('product-image').value = product.image;
            // 产品描述字段已移除，编辑时将描述填充到产品简介
            document.getElementById('product-specifications').value = product.specifications;
            document.getElementById('product-featured').value = product.featured.toString();

            // 设置新增字段
            if (product.sku) {
                document.getElementById('product-sku').value = product.sku;
            }

            // 库存字段已移除

            // 填充产品详细信息
            if (product.details) {
                if (product.details.model) document.getElementById('product-model').value = product.details.model;
                if (product.details.appearance) document.getElementById('product-appearance').value = product.details.appearance;
                if (product.details.standard) document.getElementById('product-standard').value = product.details.standard;
                if (product.details.usage) document.getElementById('product-usage').value = product.details.usage;
                if (product.details.flashpoint) document.getElementById('product-flashpoint').value = product.details.flashpoint;
                if (product.details.smell) document.getElementById('product-smell').value = product.details.smell;
                if (product.details.function) document.getElementById('product-function').value = product.details.function;
                if (product.details.amineValue) document.getElementById('product-amine-value').value = product.details.amineValue;
                if (product.details.content) document.getElementById('product-content').value = product.details.content;
                if (product.details.imported) document.getElementById('product-imported').value = product.details.imported;
                if (product.details.density) document.getElementById('product-density').value = product.details.density;
                if (product.details.color) document.getElementById('product-color').value = product.details.color;
                if (product.details.viscosity) document.getElementById('product-viscosity').value = product.details.viscosity;
            }

            // 填充产品说明书
            if (product.manual) {
                // 如果有产品简介，优先使用产品简介
                if (product.manual.intro) {
                    document.getElementById('product-intro').value = product.manual.intro;
                } else if (product.description) {
                    // 如果没有产品简介但有产品描述，则使用产品描述
                    document.getElementById('product-intro').value = product.description;
                }


                if (product.manual.usageList && Array.isArray(product.manual.usageList)) {
                    document.getElementById('product-usage-list').value = product.manual.usageList.join('\n');
                }
                if (product.manual.dosageList && Array.isArray(product.manual.dosageList)) {
                    document.getElementById('product-dosage-list').value = product.manual.dosageList.join('\n');
                }
            } else if (product.description) {
                // 如果没有产品说明书但有产品描述，则使用产品描述作为产品简介
                document.getElementById('product-intro').value = product.description;
            }

            // 更新图片预览
            if (product.image && product.image.startsWith('http')) {
                // 如果是外部URL，尝试加载，如果失败则使用本地占位图
                const img = new Image();
                img.onload = function() {
                    document.getElementById('preview-img').src = product.image;
                };
                img.onerror = function() {
                    document.getElementById('preview-img').src = '../images/placeholder/product-placeholder.svg';
                };
                img.src = product.image;
            } else {
                document.getElementById('preview-img').src = product.image || '../images/placeholder/product-placeholder.svg';
            }

            document.getElementById('modal-title').textContent = '编辑产品';
            currentProductId = productId;

            // 标记表单为未修改状态
            formModified = false;

            // 显示模态框
            productModal.style.display = 'block';
            setTimeout(() => {
                productModal.classList.add('show');
            }, 10);
        }
    }

    function closeProductModal() {
        // 如果表单已修改，显示确认对话框
        if (formModified) {
            if (!confirm('您有未保存的更改，确定要关闭吗？')) {
                return; // 用户取消关闭
            }

            // 用户确认关闭，保存表单数据到localStorage
            saveFormToLocalStorage();

            // 显示提示
            showNotification('已自动保存您的编辑，下次打开时可以恢复', 'info');
        } else {
            // 如果表单未修改，清除localStorage中的数据
            clearFormLocalStorage();
        }

        productModal.classList.remove('show');
        setTimeout(() => {
            productModal.style.display = 'none';
            // 重置图片预览
            document.getElementById('preview-img').src = '../images/placeholder/product-placeholder.svg';
        }, 300);

        // 重置表单修改状态
        formModified = false;
    }

    async function saveProduct(e) {
        e.preventDefault();

        // 显示保存中提示
        const saveBtn = productForm.querySelector('.save-btn');
        const originalBtnText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        saveBtn.disabled = true;

        try {
            // 检查必填字段
            const requiredFields = ['product-name', 'product-category', 'product-price', 'product-intro'];
            let missingFields = [];

            for (const fieldId of requiredFields) {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    missingFields.push(field.previousElementSibling.textContent);
                    field.classList.add('error');
                } else {
                    field.classList.remove('error');
                }
            }

            if (missingFields.length > 0) {
                throw new Error(`请填写必填字段: ${missingFields.join(', ')}`);
            }

            // 处理图片URL
            let imageUrl = document.getElementById('product-image').value;

            // 创建产品数据对象
            const productData = {
                name: document.getElementById('product-name').value,
                category: document.getElementById('product-category').value,
                price: parseFloat(document.getElementById('product-price').value),
                image: imageUrl,
                description: document.getElementById('product-intro').value || '暂无产品描述', // 使用产品简介作为描述
                specifications: document.getElementById('product-specifications').value,
                sku: document.getElementById('product-sku').value,
                // 库存字段已移除
                featured: document.getElementById('product-featured').value === 'true',

                // 产品详细信息
                details: {
                    model: document.getElementById('product-model').value,
                    appearance: document.getElementById('product-appearance').value,
                    standard: document.getElementById('product-standard').value,
                    usage: document.getElementById('product-usage').value,
                    flashpoint: document.getElementById('product-flashpoint').value,
                    smell: document.getElementById('product-smell').value,
                    function: document.getElementById('product-function').value,
                    amineValue: document.getElementById('product-amine-value').value,
                    content: document.getElementById('product-content').value,
                    imported: document.getElementById('product-imported').value,
                    density: document.getElementById('product-density').value,
                    color: document.getElementById('product-color').value,
                    viscosity: document.getElementById('product-viscosity').value
                },

                // 产品说明书
                manual: {
                    intro: document.getElementById('product-intro').value,
                    usageList: document.getElementById('product-usage-list').value.split('\n').filter(line => line.trim() !== ''),
                    dosageList: document.getElementById('product-dosage-list').value.split('\n').filter(line => line.trim() !== '')
                }
            };

            // 显示压缩进度
            showCompressionProgress(0);
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 压缩数据中...';

            // 压缩产品数据
            try {
                // 检查数据大小
                const jsonData = JSON.stringify(productData);
                const dataSizeInMB = jsonData.length / (1024 * 1024);

                if (dataSizeInMB > 1) {
                    console.log(`数据大小: ${dataSizeInMB.toFixed(2)}MB，开始压缩...`);

                    // 压缩数据
                    const compressedData = await ImageCompressor.compressJsonData(productData, {
                        imageFields: ['image'],
                        maxSizeMB: 0.8,
                        onProgress: (progress) => {
                            showCompressionProgress(progress);
                            saveBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 压缩数据中 ${Math.round(progress)}%...`;
                        }
                    });

                    // 使用压缩后的数据
                    const compressedSize = JSON.stringify(compressedData).length / (1024 * 1024);
                    console.log(`压缩完成，压缩后大小: ${compressedSize.toFixed(2)}MB`);

                    // 替换原始数据
                    Object.assign(productData, compressedData);

                    // 显示压缩结果
                    showNotification(`数据已压缩: ${dataSizeInMB.toFixed(2)}MB → ${compressedSize.toFixed(2)}MB`, 'info');
                }
            } catch (compressionError) {
                console.error('数据压缩失败:', compressionError);
                // 继续使用原始数据
            }

            // 隐藏压缩进度
            hideCompressionProgress();
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';

            let result;

            try {
                if (currentProductId) {
                    // 更新现有商品
                    result = await ApiClient.updateProduct(currentProductId, productData);
                    if (!result) {
                        throw new Error('更新商品失败');
                    }
                } else {
                    // 添加新商品
                    result = await ApiClient.addProduct(productData);
                    if (!result) {
                        throw new Error('添加商品失败');
                    }
                }

                // 从API获取更新的商品数据
                const apiProducts = await ApiClient.getProducts();
                if (apiProducts && apiProducts.length > 0) {
                    products = apiProducts;
                    displayProducts(products);
                }

                // 重置表单修改状态
                formModified = false;

                // 清除localStorage中的表单数据 - 产品保存成功后不需要保留缓存
                clearFormLocalStorage();

                // 关闭模态框
                closeProductModal();

                // 显示成功提示
                showNotification(currentProductId ? '商品更新成功' : '商品添加成功', 'success');
            } catch (error) {
                console.error('保存商品失败:', error);
                showNotification('保存商品失败: ' + error.message, 'error');
            }
        } catch (error) {
            console.error('保存商品失败:', error);
            showNotification('保存商品失败，请重试', 'error');
        } finally {
            // 恢复按钮状态
            saveBtn.innerHTML = originalBtnText;
            saveBtn.disabled = false;
        }
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `admin-notification ${type}`;

        let icon = 'check-circle';
        if (type === 'error') {
            icon = 'exclamation-circle';
        } else if (type === 'info') {
            icon = 'info-circle';
        }

        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-${icon}"></i>
            </div>
            <div class="notification-message">${message}</div>
        `;

        document.body.appendChild(notification);

        // 显示通知
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // 3秒后隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    function openDeleteModal(productId) {
        const product = products.find(p => p.id === productId);
        if (product) {
            // 更新确认删除消息，显示产品名称
            const confirmMessage = document.querySelector('#delete-modal p');
            confirmMessage.innerHTML = `您确定要删除 <strong>${product.name}</strong> 吗？此操作无法撤销。`;

            productToDelete = productId;
            deleteModal.style.display = 'block';
            setTimeout(() => {
                deleteModal.classList.add('show');
            }, 10);
        }
    }

    function closeDeleteModal() {
        deleteModal.classList.remove('show');
        setTimeout(() => {
            deleteModal.style.display = 'none';
            productToDelete = null;
        }, 300);
    }

    async function confirmDeleteProduct() {
        if (productToDelete) {
            // 显示删除中提示
            const deleteBtn = deleteConfirmBtn;
            const originalBtnText = deleteBtn.innerHTML;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 删除中...';
            deleteBtn.disabled = true;

            try {
                try {
                    // 使用API删除商品
                    const success = await ApiClient.deleteProduct(productToDelete);

                    if (success) {
                        console.log('商品已从服务器删除');

                        // 从API获取更新的商品数据
                        const apiProducts = await ApiClient.getProducts();
                        products = apiProducts || [];

                        // 更新显示
                        displayProducts(products);

                        // 关闭模态框
                        closeDeleteModal();

                        // 显示成功提示
                        showNotification('商品已成功删除', 'success');
                    } else {
                        throw new Error('删除商品失败');
                    }
                } catch (error) {
                    console.error('删除商品失败:', error);
                    showNotification('删除商品失败: ' + error.message, 'error');

                    // 关闭模态框
                    closeDeleteModal();
                }
            } catch (error) {
                console.error('删除商品失败:', error);
                showNotification('删除商品失败，请重试', 'error');

                // 关闭模态框
                closeDeleteModal();
            } finally {
                // 恢复按钮状态
                deleteBtn.innerHTML = originalBtnText;
                deleteBtn.disabled = false;
            }
        }
    }

    function searchProducts() {
        const searchTerm = productSearch.value.toLowerCase().trim();

        if (searchTerm === '') {
            displayProducts(products);
            return;
        }

        const filteredProducts = products.filter(product =>
            product.name.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm) ||
            (product.description && product.description.toLowerCase().includes(searchTerm))
        );

        displayProducts(filteredProducts);
    }
});
