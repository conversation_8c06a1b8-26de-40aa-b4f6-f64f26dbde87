/* 球体消失动画专用CSS */

/* 球体消失动画 */
@keyframes ball-disappear {
    0% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 0 25px rgba(255, 255, 255, 0.9);
    }
    50% {
        transform: scale(0.6);
        opacity: 0.7;
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
    }
    100% {
        transform: scale(0.2);
        opacity: 0;
        box-shadow: 0 0 5px rgba(255, 255, 255, 0);
    }
}

/* 球体闪光效果 */
@keyframes ball-flash {
    0% {
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.6);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.9), inset 0 0 30px rgba(255, 255, 255, 0.9);
    }
    100% {
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.6);
    }
}

/* 应用消失动画的类 */
.disappearing-ball {
    animation: ball-disappear 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

/* 应用闪光效果的类 */
.flashing-ball {
    animation: ball-flash 0.5s ease-out;
}
