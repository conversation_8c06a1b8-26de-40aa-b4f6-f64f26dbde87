/**
 * 团队成员照片生成器
 * 当图片加载失败时，生成带有成员信息的默认图片
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有团队成员图片
    const teamImages = document.querySelectorAll('.team-member .member-photo img');
    
    // 为每个图片添加错误处理
    teamImages.forEach(img => {
        img.addEventListener('error', function() {
            // 获取团队成员类型
            const memberElement = this.closest('.team-member');
            const memberType = memberElement.classList.contains('ceo') ? 'ceo' : 
                              memberElement.classList.contains('tech') ? 'tech' : 
                              memberElement.classList.contains('sales') ? 'sales' : 'default';
            
            // 获取成员信息
            const memberName = memberElement.querySelector('h3').textContent;
            const memberPosition = memberElement.querySelector('.member-position').textContent;
            
            // 创建占位符元素
            const photoContainer = this.closest('.member-photo');
            
            // 设置背景渐变
            let gradient;
            switch(memberType) {
                case 'ceo':
                    gradient = 'linear-gradient(135deg, #ff9966, #ff5e62)';
                    break;
                case 'tech':
                    gradient = 'linear-gradient(135deg, #56ccf2, #2f80ed)';
                    break;
                case 'sales':
                    gradient = 'linear-gradient(135deg, #a1c4fd, #c2e9fb)';
                    break;
                default:
                    gradient = 'linear-gradient(135deg, #8e9eab, #eef2f3)';
            }
            
            // 设置背景
            photoContainer.style.background = gradient;
            
            // 添加占位符类
            photoContainer.classList.add('placeholder');
            
            // 清空内容
            photoContainer.innerHTML = '';
            
            // 添加图标
            const icon = document.createElement('i');
            switch(memberType) {
                case 'ceo':
                    icon.className = 'fas fa-user-tie';
                    break;
                case 'tech':
                    icon.className = 'fas fa-flask';
                    break;
                case 'sales':
                    icon.className = 'fas fa-chart-line';
                    break;
                default:
                    icon.className = 'fas fa-user';
            }
            photoContainer.appendChild(icon);
            
            // 添加名称
            const nameElement = document.createElement('h3');
            nameElement.textContent = memberName;
            photoContainer.appendChild(nameElement);
            
            // 添加职位
            const positionElement = document.createElement('p');
            positionElement.textContent = memberPosition;
            photoContainer.appendChild(positionElement);
            
            // 添加公司名称
            const companyElement = document.createElement('p');
            companyElement.textContent = '广州市龙驰新材料科技有限公司';
            companyElement.style.fontSize = '12px';
            companyElement.style.marginTop = '10px';
            companyElement.style.opacity = '0.7';
            photoContainer.appendChild(companyElement);
        });
    });
});
