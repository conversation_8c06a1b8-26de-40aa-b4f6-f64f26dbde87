/* 视频区域紧凑版样式 */
.video-section {
    padding: 30px 0;
    background-color: #f0f6ff;
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
}

/* 紧凑模式下的视频区域 */
.video-section.compact {
    max-height: 60px; /* 非常小的初始高度，只显示提示文字 */
    overflow: hidden;
    padding: 10px 0;
    cursor: pointer;
    position: relative;
    background-color: #f0f6ff;
}

/* 展开提示 */
.video-section.compact::after {
    content: '鼠标悬停查看产品视频介绍';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    text-align: center;
    transform: translateY(-50%);
    padding: 10px 0;
    color: #1a73e8;
    font-size: 14px;
    font-weight: 500;
    z-index: 10;
}



/* 紧凑模式下隐藏查看更多按钮 */
.video-section.compact .view-more-container {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* 紧凑模式下的视频列表 */
.video-section.compact .video-list-container {
    opacity: 0;
    transform: scale(0.95);
    transition: all 0.5s ease;
    max-height: 0; /* 完全隐藏视频列表 */
    overflow: hidden;
    visibility: hidden; /* 确保完全不可见 */
}

/* 鼠标悬停时展开 */
.video-section.compact:hover {
    max-height: 1500px; /* 足够大的高度以显示所有内容 */
    padding: 40px 0;
    transition: max-height 0.6s ease, padding 0.4s ease;
}

.video-section.compact:hover::after {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.video-section.compact:hover .video-list-container {
    opacity: 1;
    transform: scale(1);
    max-height: 1000px; /* 足够大的高度以显示内容 */
    overflow: visible;
    visibility: visible; /* 确保可见 */
    transition: all 0.5s ease 0.1s, visibility 0s; /* 添加延迟，使动画更流畅 */
}

.video-section.compact:hover .view-more-container {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease 0.2s, visibility 0.5s ease 0.2s; /* 添加延迟，使动画更流畅 */
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .video-section.compact {
        max-height: 50px; /* 移动设备上更小的初始高度 */
    }

    .video-section.compact:hover,
    .video-section.compact:active,
    .video-section.compact:focus {
        max-height: 1500px;
    }

    .video-section.compact::after {
        content: '点击查看产品视频介绍';
        font-size: 13px;
        padding: 8px 0;
    }

    /* 移动设备上完全隐藏视频列表 */
    .video-section.compact .video-list-container {
        max-height: 0;
        opacity: 0;
        visibility: hidden;
    }

    /* 移动设备上点击后显示视频列表 */
    .video-section.compact:active .video-list-container,
    .video-section.compact:focus .video-list-container {
        max-height: 1000px;
        opacity: 1;
        visibility: visible;
    }
}
