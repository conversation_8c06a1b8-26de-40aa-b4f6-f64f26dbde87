/**
 * 简单地图样式
 * Simple map styles for LongChi Mall - No API key required
 */

.map-container {
    position: relative;
    width: 100%;
    height: 450px;
    overflow: hidden;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

#baidu-map {
    width: 100%;
    height: 100%;
    background-color: #e8eef7;
}

/* 地址卡片样式 */
.address-card {
    transition: all 0.3s ease;
    z-index: 100 !important;
}

.address-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

/* 位置标记样式 */
.location-marker {
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 50 !important;
}

.location-marker:hover {
    transform: translate(-50%, -50%) scale(1.1);
}

/* 建筑物样式 */
.map-building {
    transition: all 0.2s ease;
    cursor: pointer;
}

.map-building:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 10 !important;
}

/* 建筑物标签样式 */
.building-label {
    transition: all 0.2s ease;
    pointer-events: none;
}

.map-building:hover + .building-label {
    font-weight: bold;
    background-color: rgba(255, 255, 255, 1);
    z-index: 11 !important;
}

/* 道路名称样式 */
.road-name {
    transition: all 0.2s ease;
    pointer-events: none;
}

.road-name:hover {
    font-weight: bold;
    background-color: rgba(255, 255, 255, 1);
    z-index: 11 !important;
}

/* 弹出窗口样式 */
.map-popup {
    animation: fadeIn 0.3s ease;
    z-index: 200 !important;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, -90%); }
    to { opacity: 1; transform: translate(-50%, -100%); }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .map-container {
        height: 350px;
    }

    .address-card {
        left: 10px;
        bottom: 10px;
        padding: 10px;
        max-width: calc(100% - 40px);
    }
}

@media (max-width: 480px) {
    .map-container {
        height: 300px;
    }

    .address-card {
        max-width: calc(100% - 20px);
        font-size: 12px;
    }
}
