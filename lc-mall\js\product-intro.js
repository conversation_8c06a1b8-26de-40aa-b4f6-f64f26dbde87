document.addEventListener('DOMContentLoaded', function() {
    // 检查是否是管理员登录状态
    const isAdmin = checkAdminStatus();

    // 加载产品数据
    loadProductData().then(() => {
        if (isAdmin) {
            // 添加编辑按钮到每个产品类别卡片
            addEditButtons();

            // 添加编辑按钮到产品详情表格
            addTableEditButtons();

            // 处理URL参数，自动打开编辑模态框
            handleUrlParams();
        }
    });
});

// 处理URL参数
function handleUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const editType = urlParams.get('edit');
    const id = urlParams.get('id');

    if (editType && id) {
        // 等待一小段时间确保页面完全加载
        setTimeout(() => {
            if (editType === 'category') {
                // 获取类别数据
                const card = document.getElementById(id);
                if (card) {
                    const title = card.querySelector('.category-header h3').textContent;
                    const content = card.querySelector('.category-content p').textContent;
                    openEditModal(id, title, content);
                }
            } else if (editType === 'table') {
                // 打开表格编辑模态框
                openTableEditModal(id);
            }
        }, 500);
    }
}

// 检查管理员登录状态
function checkAdminStatus() {
    // 检查URL参数是否包含编辑模式
    const urlParams = new URLSearchParams(window.location.search);
    const isEditMode = urlParams.has('edit');

    // 从sessionStorage获取管理员会话信息
    const adminSession = JSON.parse(sessionStorage.getItem('adminSession') || '{}');
    const isLoggedIn = adminSession.loggedIn === true;

    // 如果是编辑模式但未登录，重定向到管理员登录页面
    if (isEditMode && !isLoggedIn) {
        // 获取当前URL作为重定向目标
        const currentUrl = window.location.href;
        window.location.href = `/admin/login.html?redirect=${encodeURIComponent(currentUrl)}`;
        return false;
    }

    return isEditMode && isLoggedIn;
}

// 添加编辑按钮到产品类别卡片
function addEditButtons() {
    const categoryCards = document.querySelectorAll('.category-card');

    categoryCards.forEach(card => {
        const editButton = document.createElement('button');
        editButton.className = 'edit-button';
        editButton.innerHTML = '<i class="fas fa-edit"></i> 编辑';
        editButton.addEventListener('click', function() {
            const categoryId = card.id;
            const categoryHeader = card.querySelector('.category-header h3').textContent;
            const categoryContent = card.querySelector('.category-content p').textContent;

            openEditModal(categoryId, categoryHeader, categoryContent);
        });

        card.appendChild(editButton);
    });
}

// 添加编辑按钮到产品详情表格
function addTableEditButtons() {
    const productDetails = document.querySelectorAll('.product-details');

    productDetails.forEach(detail => {
        const editButton = document.createElement('button');
        editButton.className = 'table-edit-button';
        editButton.innerHTML = '<i class="fas fa-edit"></i> 编辑表格';
        editButton.addEventListener('click', function() {
            const detailId = detail.id;
            openTableEditModal(detailId);
        });

        detail.querySelector('h2').appendChild(editButton);
    });
}

// 打开编辑模态框
function openEditModal(categoryId, title, content) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'edit-modal';

    modal.innerHTML = `
        <div class="edit-modal-content">
            <span class="close-modal">&times;</span>
            <h2>编辑产品类别</h2>
            <form id="edit-category-form">
                <div class="form-group">
                    <label for="category-title">标题</label>
                    <input type="text" id="category-title" value="${title}" required>
                </div>
                <div class="form-group">
                    <label for="category-content">内容</label>
                    <textarea id="category-content" rows="6" required>${content}</textarea>
                </div>
                <div class="form-group">
                    <label for="category-image">上传图片</label>
                    <input type="file" id="category-image" accept="image/*">
                    <div id="image-preview"></div>
                </div>
                <div class="form-actions">
                    <button type="submit" class="save-btn">保存</button>
                    <button type="button" class="cancel-btn">取消</button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);

    // 关闭模态框
    const closeBtn = modal.querySelector('.close-modal');
    const cancelBtn = modal.querySelector('.cancel-btn');

    closeBtn.addEventListener('click', function() {
        document.body.removeChild(modal);
    });

    cancelBtn.addEventListener('click', function() {
        document.body.removeChild(modal);
    });

    // 处理图片上传预览
    const imageInput = document.getElementById('category-image');
    let imageUrl = '';

    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('image-preview');
                preview.innerHTML = `<img src="${e.target.result}" style="max-width: 100%; max-height: 200px; margin-top: 10px;">`;
            };
            reader.readAsDataURL(file);

            // 上传图片
            uploadImage(file, function(url) {
                imageUrl = url;
            });
        }
    });

    // 提交表单
    const form = modal.querySelector('#edit-category-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const newTitle = document.getElementById('category-title').value;
        const newContent = document.getElementById('category-content').value;

        // 更新UI
        const card = document.getElementById(categoryId);
        card.querySelector('.category-header h3').textContent = newTitle;
        card.querySelector('.category-content p').textContent = newContent;

        // 保存到后端
        const categoryData = {
            title: newTitle,
            content: newContent
        };

        if (imageUrl) {
            categoryData.imageUrl = imageUrl;
        }

        saveProductData(categoryId, categoryData);

        document.body.removeChild(modal);
    });
}

// 打开表格编辑模态框
function openTableEditModal(detailId) {
    // 获取当前表格数据
    const table = document.querySelector(`#${detailId} table`);
    const rows = table.querySelectorAll('tbody tr');

    let tableData = [];
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const rowData = {
            name: cells[0].textContent,
            model: cells[1].textContent,
            dosage: cells[2].textContent,
            features: cells[3].textContent
        };
        tableData.push(rowData);
    });

    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'edit-modal';

    let tableRows = '';
    tableData.forEach((row, index) => {
        tableRows += `
            <tr>
                <td><input type="text" class="product-name-input" value="${row.name}"></td>
                <td><input type="text" class="product-model-input" value="${row.model}"></td>
                <td><input type="text" class="product-dosage-input" value="${row.dosage || ''}"></td>
                <td><input type="text" class="product-features-input" value="${row.features}"></td>
                <td>
                    <button type="button" class="delete-row-btn" data-index="${index}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    modal.innerHTML = `
        <div class="edit-modal-content table-edit-modal">
            <span class="close-modal">&times;</span>
            <h2>编辑产品表格</h2>
            <form id="edit-table-form">
                <div class="table-container">
                    <table class="edit-table">
                        <thead>
                            <tr>
                                <th>产品名称</th>
                                <th>产品型号</th>
                                <th>建议添加量</th>
                                <th>产品特点</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="edit-table-body">
                            ${tableRows}
                        </tbody>
                    </table>
                </div>
                <button type="button" id="add-row-btn">添加行</button>
                <div class="form-actions">
                    <button type="submit" class="save-btn">保存</button>
                    <button type="button" class="cancel-btn">取消</button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);

    // 关闭模态框
    const closeBtn = modal.querySelector('.close-modal');
    const cancelBtn = modal.querySelector('.cancel-btn');

    closeBtn.addEventListener('click', function() {
        document.body.removeChild(modal);
    });

    cancelBtn.addEventListener('click', function() {
        document.body.removeChild(modal);
    });

    // 添加行
    const addRowBtn = modal.querySelector('#add-row-btn');
    addRowBtn.addEventListener('click', function() {
        const tableBody = modal.querySelector('#edit-table-body');
        const newRow = document.createElement('tr');
        const rowIndex = tableBody.querySelectorAll('tr').length;

        newRow.innerHTML = `
            <td><input type="text" class="product-name-input" value=""></td>
            <td><input type="text" class="product-model-input" value=""></td>
            <td><input type="text" class="product-dosage-input" value=""></td>
            <td><input type="text" class="product-features-input" value=""></td>
            <td>
                <button type="button" class="delete-row-btn" data-index="${rowIndex}">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(newRow);

        // 为新添加的删除按钮添加事件监听
        const deleteBtn = newRow.querySelector('.delete-row-btn');
        deleteBtn.addEventListener('click', function() {
            tableBody.removeChild(newRow);
        });
    });

    // 删除行
    const deleteButtons = modal.querySelectorAll('.delete-row-btn');
    deleteButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const row = this.closest('tr');
            row.parentNode.removeChild(row);
        });
    });

    // 提交表单
    const form = modal.querySelector('#edit-table-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // 获取编辑后的表格数据
        const rows = modal.querySelectorAll('#edit-table-body tr');
        let newTableData = [];

        rows.forEach(row => {
            const nameInput = row.querySelector('.product-name-input');
            const modelInput = row.querySelector('.product-model-input');
            const dosageInput = row.querySelector('.product-dosage-input');
            const featuresInput = row.querySelector('.product-features-input');

            newTableData.push({
                name: nameInput.value,
                model: modelInput.value,
                dosage: dosageInput.value,
                features: featuresInput.value
            });
        });

        // 更新UI
        updateTableUI(detailId, newTableData);

        // 保存到后端
        saveTableData(detailId, newTableData);

        document.body.removeChild(modal);
    });
}

// 更新表格UI
function updateTableUI(detailId, tableData) {
    const tableBody = document.querySelector(`#${detailId} table tbody`);
    tableBody.innerHTML = '';

    let currentName = '';
    let rowspan = 0;
    let firstRow = true;

    // 计算每个产品名称的行数
    const productCounts = {};
    tableData.forEach(row => {
        if (!productCounts[row.name]) {
            productCounts[row.name] = 0;
        }
        productCounts[row.name]++;
    });

    tableData.forEach((row, index) => {
        const tr = document.createElement('tr');

        // 如果是新产品名称，创建带有rowspan的单元格
        if (row.name !== currentName) {
            currentName = row.name;
            rowspan = productCounts[row.name];

            const nameTd = document.createElement('td');
            nameTd.textContent = row.name;
            nameTd.rowSpan = rowspan;
            nameTd.className = 'product-name';
            tr.appendChild(nameTd);

            firstRow = false;
        }

        // 添加型号、建议添加量和特点单元格
        const modelTd = document.createElement('td');
        modelTd.textContent = row.model;
        tr.appendChild(modelTd);

        const dosageTd = document.createElement('td');
        dosageTd.textContent = row.dosage || '';
        tr.appendChild(dosageTd);

        const featuresTd = document.createElement('td');
        featuresTd.textContent = row.features;
        tr.appendChild(featuresTd);

        tableBody.appendChild(tr);
    });
}

// 加载产品数据
function loadProductData() {
    // 返回Promise以便链式调用
    return new Promise((resolve, reject) => {
        // 从后端API加载数据
        fetch('/api/product-intro')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to load product data');
                }
                return response.json();
            })
            .catch(error => {
                console.error('Error loading product data:', error);
                // 如果API调用失败，尝试从本地JSON文件加载
                return fetch('/data/product-intro.json')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Failed to load local product data');
                        }
                        return response.json();
                    })
                    .catch(localError => {
                        console.error('Error loading local product data:', localError);
                        reject(localError);
                        return null;
                    });
            })
            .then(data => {
                if (data) {
                    // 使用加载的数据更新UI
                    updateProductUI(data);
                    resolve(data);
                } else {
                    reject(new Error('No data available'));
                }
            });
    });
}

// 保存产品数据
function saveProductData(categoryId, data) {
    // 发送数据到后端API
    fetch('/api/product-intro/category', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            categoryId: categoryId,
            data: data
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Failed to save category data');
        }
        return response.json();
    })
    .then(result => {
        if (result.success) {
            console.log('Category data saved successfully');
        }
    })
    .catch(error => {
        console.error('Error saving category data:', error);
    });
}

// 保存表格数据
function saveTableData(detailId, data) {
    // 发送数据到后端API
    fetch('/api/product-intro/table', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            detailId: detailId,
            data: data
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Failed to save table data');
        }
        return response.json();
    })
    .then(result => {
        if (result.success) {
            console.log('Table data saved successfully');
        }
    })
    .catch(error => {
        console.error('Error saving table data:', error);
    });
}

// 上传图片
function uploadImage(file, callback) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const imageData = e.target.result;

        fetch('/api/product-intro/upload-image', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                imageData: imageData,
                fileName: file.name
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to upload image');
            }
            return response.json();
        })
        .then(result => {
            if (result.success && callback) {
                callback(result.imageUrl);
            }
        })
        .catch(error => {
            console.error('Error uploading image:', error);
        });
    };
    reader.readAsDataURL(file);
}

// 更新产品UI
function updateProductUI(data) {
    // 更新类别卡片
    if (data.categories) {
        Object.keys(data.categories).forEach(categoryId => {
            const card = document.getElementById(categoryId);
            if (card) {
                const categoryData = data.categories[categoryId];
                card.querySelector('.category-header h3').textContent = categoryData.title;
                card.querySelector('.category-content p').textContent = categoryData.content;
            }
        });
    }

    // 更新表格
    if (data.tables) {
        Object.keys(data.tables).forEach(detailId => {
            updateTableUI(detailId, data.tables[detailId]);
        });
    }
}
