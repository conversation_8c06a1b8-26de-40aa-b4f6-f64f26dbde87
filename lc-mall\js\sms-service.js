// 短信通知服务模拟
console.log('SMS Service loaded');

// 管理员手机号
const ADMIN_PHONE = '18938845280';

// 短信类型
const SMS_TYPES = {
    ORDER: 'order',
    MESSAGE: 'message'
};

// 发送短信通知
function sendSmsNotification(type, data) {
    // 创建短信内容
    let content = '';
    let title = '';

    switch(type) {
        case SMS_TYPES.ORDER:
            title = '新订单通知';
            content = `您有一个新订单 #${data.id}，客户：${data.customer.name}，金额：¥${data.total.toFixed(2)}，请及时处理。`;
            break;
        case SMS_TYPES.MESSAGE:
            title = '新留言通知';
            content = `您有一条新留言，来自：${data.name}，电话：${data.phone}，请及时查看。`;
            break;
        default:
            console.error('未知的短信类型');
            return false;
    }

    // 创建短信记录
    const smsRecord = {
        id: Date.now(),
        type: type,
        recipient: ADMIN_PHONE,
        title: title,
        content: content,
        data: data,
        sentTime: new Date().toISOString(),
        status: 'sent'
    };

    // 保存短信记录到服务器
    try {
        ApiClient.addSmsRecord(smsRecord)
            .then(result => {
                if (result) {
                    console.log('短信记录已保存到服务器:', result);
                } else {
                    console.error('保存短信记录失败');
                }
            })
            .catch(error => {
                console.error('保存短信记录错误:', error);
            });
    } catch (error) {
        console.error('保存短信记录异常:', error);
    }

    // 模拟发送短信
    console.log(`短信已发送到 ${ADMIN_PHONE}：${title} - ${content}`);

    return true;
}

// 导出函数和常量
window.smsService = {
    sendSmsNotification,
    SMS_TYPES
};
