// 短视频页面功能
document.addEventListener('DOMContentLoaded', function() {
    // 视频数据
    const videos = [
        {
            id: 1,
            title: '潜固化剂WL-101产品介绍',
            thumbnail: 'https://via.placeholder.com/400x225?text=视频缩略图',
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // 示例URL
            date: '2023-05-15',
            views: 1245
        },
        {
            id: 2,
            title: '生物基树脂应用技术',
            thumbnail: 'https://via.placeholder.com/400x225?text=视频缩略图',
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // 示例URL
            date: '2023-04-20',
            views: 986
        },
        {
            id: 3,
            title: '功能助剂使用指南',
            thumbnail: 'https://via.placeholder.com/400x225?text=视频缩略图',
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // 示例URL
            date: '2023-03-10',
            views: 1532
        },
        {
            id: 4,
            title: '粉体助剂产品展示',
            thumbnail: 'https://via.placeholder.com/400x225?text=视频缩略图',
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // 示例URL
            date: '2023-02-25',
            views: 876
        },
        {
            id: 5,
            title: '企业宣传片',
            thumbnail: 'https://via.placeholder.com/400x225?text=视频缩略图',
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // 示例URL
            date: '2023-01-15',
            views: 2345
        },
        {
            id: 6,
            title: '技术研发团队介绍',
            thumbnail: 'https://via.placeholder.com/400x225?text=视频缩略图',
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // 示例URL
            date: '2022-12-20',
            views: 1123
        }
    ];
    
    // DOM Elements
    const videoItems = document.querySelectorAll('.video-item');
    const videoModal = document.getElementById('video-modal');
    const videoTitle = document.getElementById('video-title');
    const videoFrame = document.getElementById('video-frame');
    const closeButtons = document.querySelectorAll('.close');
    
    // 初始化视频点击事件
    if (videoItems.length > 0) {
        videoItems.forEach((item, index) => {
            const playButton = item.querySelector('.play-button');
            if (playButton) {
                playButton.addEventListener('click', () => {
                    openVideoModal(videos[index]);
                });
            }
            
            const thumbnail = item.querySelector('.video-thumbnail');
            if (thumbnail) {
                thumbnail.addEventListener('click', () => {
                    openVideoModal(videos[index]);
                });
            }
        });
    }
    
    // 打开视频模态框
    function openVideoModal(video) {
        if (videoModal && videoTitle && videoFrame) {
            videoTitle.textContent = video.title;
            videoFrame.src = video.videoUrl;
            videoModal.style.display = 'block';
            
            // 记录观看次数（在实际应用中，这应该是通过API保存到服务器）
            video.views++;
        }
    }
    
    // 关闭模态框
    if (closeButtons.length > 0) {
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                if (videoModal) {
                    videoModal.style.display = 'none';
                    if (videoFrame) {
                        videoFrame.src = '';
                    }
                }
            });
        });
    }
    
    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
        if (e.target === videoModal) {
            videoModal.style.display = 'none';
            if (videoFrame) {
                videoFrame.src = '';
            }
        }
    });
});
