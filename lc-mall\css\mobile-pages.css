/* 移动端页面专用样式 */
/* 这个文件专门用于处理移动设备上的页面样式问题 */

@media only screen and (max-width: 767px) {
    /* 通用页面样式 */
    .page-banner {
        padding: 30px 0;
    }

    .page-banner h1 {
        font-size: 24px;
    }

    .breadcrumb {
        font-size: 12px;
    }

    /* 关于我们页面 */
    .about-section {
        padding: 20px 0;
    }

    .about-content {
        flex-direction: column;
    }

    .about-image {
        width: 100%;
        margin-bottom: 20px;
    }

    .about-text {
        width: 100%;
        padding: 0;
    }

    .about-text h3 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .about-text p {
        font-size: 14px;
        margin-bottom: 15px;
    }

    .company-features {
        margin-left: 20px;
    }

    .company-features li {
        margin-bottom: 10px;
        font-size: 14px;
    }

    .about-features {
        flex-direction: column;
        margin-top: 30px;
    }

    .feature-item {
        width: 100%;
        margin-bottom: 20px;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }

    .feature-content h3 {
        font-size: 16px;
    }

    .feature-content p {
        font-size: 14px;
    }

    /* 位置信息部分 */
    .location-section {
        padding: 20px 0;
    }

    .location-info {
        flex-direction: column;
    }

    .location-info > div {
        width: 100%;
        margin-bottom: 20px;
    }

    .map-container {
        height: 300px;
    }

    .map-container iframe {
        height: 300px;
    }

    /* 企业风貌页面 */
    .company-profile {
        padding: 20px 0;
    }

    .company-gallery {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        gap: 15px;
    }

    .gallery-item {
        margin-bottom: 15px;
    }

    .gallery-item img {
        height: 200px;
    }

    .gallery-caption {
        font-size: 14px;
        padding: 8px;
    }

    .company-description {
        padding: 15px;
    }

    .company-description h3 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .company-description p {
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 15px;
    }

    /* 产品介绍页面 */
    .product-intro-section {
        padding: 20px 0;
    }

    .product-intro-content {
        flex-direction: column;
    }

    .product-intro-image {
        width: 100%;
        margin-bottom: 20px;
    }

    .product-intro-text {
        width: 100%;
        padding: 0;
    }

    .product-intro-text h3 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .product-intro-text p {
        font-size: 14px;
        margin-bottom: 15px;
    }

    /* 修复导航栏在移动设备上的显示问题 */
    .mobile-nav-toggle {
        display: block !important;
        position: absolute;
        left: 10px;
        top: 15px;
        z-index: 1001;
        background: transparent;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
    }

    .nav-container {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100vh;
        background-color: var(--primary-color);
        z-index: 1000;
        transition: left 0.3s ease;
        overflow-y: auto;
        padding-top: 60px;
    }

    .nav-container.active {
        left: 0;
    }

    nav ul {
        flex-direction: column !important;
        padding: 0 20px !important;
        width: 100% !important;
    }

    nav ul li {
        margin: 10px 0 !important;
        width: 100% !important;
    }

    nav ul li a {
        display: block !important;
        padding: 10px 0 !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        width: 100% !important;
    }

    /* 移动导航遮罩层 */
    .mobile-nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
    }

    /* 用户状态区域 */
    #user-status {
        position: absolute !important;
        top: 10px !important;
        right: 60px !important;
        display: flex !important;
        flex-direction: row !important;
    }

    #user-status button {
        padding: 5px 10px !important;
        font-size: 12px !important;
        margin-left: 5px !important;
    }

    /* 购物车图标 */
    .cart-icon {
        position: absolute !important;
        top: 10px !important;
        right: 10px !important;
        margin-left: 0 !important;
    }

    .cart-icon a {
        width: 30px !important;
        height: 30px !important;
    }

    #cart-count {
        min-width: 16px !important;
        height: 16px !important;
        font-size: 10px !important;
    }

    /* 移动版Logo */
    .mobile-logo {
        display: block !important;
        text-align: center !important;
        margin: 0 auto !important;
    }

    .mobile-logo img {
        height: 40px !important;
        margin: 5px auto !important;
    }
}
