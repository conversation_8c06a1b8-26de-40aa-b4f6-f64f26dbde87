/**
 * 视频页面专用脚本
 * 用于处理视频页面的特定功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 等待页面完全加载后再初始化视频页面
    window.addEventListener('load', initVideosPage);
});

// 当前页码
let currentPage = 1;
// 每页显示的视频数量
const videosPerPage = 8;
// 当前筛选类别
let currentCategory = 'all';
// 当前排序方式
let currentSort = 'date';
// 当前排序顺序
let currentOrder = 'desc';

/**
 * 初始化视频页面
 */
function initVideosPage() {
    // 加载视频数据
    loadVideos();

    // 初始化视频点击事件
    initVideoClickEvents();

    // 初始化视频模态框
    initVideoModal();

    // 初始化查看更多按钮
    initViewMoreButton();

    // 初始化筛选和排序功能
    initFilterAndSort();
}

/**
 * 加载视频数据
 * @param {boolean} append - 是否追加模式（用于加载更多）
 */
function loadVideos(append = false) {
    // 获取视频列表容器
    const videoListContainer = document.getElementById('video-list-container');
    if (!videoListContainer) return;

    // 获取筛选后的视频数据
    let filteredVideos = getVideosByCategory(currentCategory);

    // 排序视频
    if (currentSort === 'date') {
        filteredVideos = sortVideosByDate(filteredVideos, currentOrder);
    } else if (currentSort === 'views') {
        filteredVideos = sortVideosByViews(filteredVideos, currentOrder);
    }

    // 计算分页
    const startIndex = (currentPage - 1) * videosPerPage;
    const endIndex = startIndex + videosPerPage;
    const paginatedVideos = filteredVideos.slice(startIndex, endIndex);

    // 如果不是追加模式，清空列表内容
    if (!append) {
        videoListContainer.innerHTML = '';
    }

    // 添加视频项目
    paginatedVideos.forEach((video, index) => {
        const videoItem = document.createElement('div');
        videoItem.className = 'video-item';
        videoItem.dataset.id = video.id;

        // 格式化日期
        const formattedDate = formatDate(video.date);

        // 格式化观看次数
        const formattedViews = formatViews(video.views);

        videoItem.innerHTML = `
            <div class="video-placeholder" data-video-src="${video.src}">
                <img src="${video.thumbnail}" alt="${video.title}">
                <div class="play-button">
                    <i class="fas fa-play"></i>
                </div>
                <div class="video-duration">${video.duration}</div>
                <div class="loading-indicator">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <div class="video-info">
                <h3>${video.title}</h3>
                <p>${video.description}</p>
                <div class="video-meta">
                    <div class="date">
                        <i class="far fa-calendar-alt"></i>
                        <span>${formattedDate}</span>
                    </div>
                    <div class="views">
                        <i class="far fa-eye"></i>
                        <span>${formattedViews}次观看</span>
                    </div>
                </div>
            </div>
        `;

        videoListContainer.appendChild(videoItem);
    });

    // 添加视频加载动画效果
    animateVideoItems();

    // 更新查看更多按钮状态
    updateViewMoreButton(filteredVideos.length > endIndex);
}

/**
 * 初始化筛选和排序功能
 */
function initFilterAndSort() {
    // 这里可以添加筛选和排序功能的实现
    // 例如添加类别筛选、日期排序、观看次数排序等

    // 示例：添加筛选按钮点击事件
    const filterButtons = document.querySelectorAll('.filter-btn');
    if (filterButtons.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const category = this.getAttribute('data-category');
                currentCategory = category;
                currentPage = 1;
                loadVideos(false);

                // 更新按钮状态
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
        });
    }

    // 示例：添加排序按钮点击事件
    const sortButtons = document.querySelectorAll('.sort-btn');
    if (sortButtons.length > 0) {
        sortButtons.forEach(button => {
            button.addEventListener('click', function() {
                const sort = this.getAttribute('data-sort');
                const order = this.getAttribute('data-order');

                currentSort = sort;
                currentOrder = order;
                currentPage = 1;
                loadVideos(false);

                // 更新按钮状态
                sortButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
        });
    }
}

/**
 * 加载更多视频
 * 供video-lazy-load.js调用
 */
function loadMoreVideos() {
    // 获取当前显示的视频数量
    const videoListContainer = document.getElementById('video-list-container');
    if (!videoListContainer) return;

    const currentVideoCount = videoListContainer.children.length;
    const totalVideos = getAllVideos().length;

    // 如果已经显示了所有视频，不再加载
    if (currentVideoCount >= totalVideos) {
        updateViewMoreButtonVisibility(false);
        return;
    }

    // 计算需要加载的视频
    const nextVideos = getAllVideos().slice(currentVideoCount, currentVideoCount + 4);

    // 添加视频项目
    nextVideos.forEach((video, index) => {
        const videoItem = document.createElement('div');
        videoItem.className = 'video-item';
        videoItem.dataset.id = video.id;

        // 格式化日期
        const formattedDate = formatDate(video.date);

        // 格式化观看次数
        const formattedViews = formatViews(video.views);

        videoItem.innerHTML = `
            <div class="video-placeholder" data-video-src="${video.src}">
                <img src="${video.thumbnail}" alt="${video.title}">
                <div class="play-button">
                    <i class="fas fa-play"></i>
                </div>
                <div class="video-duration">${video.duration}</div>
                <div class="loading-indicator">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <div class="video-info">
                <h3>${video.title}</h3>
                <p>${video.description}</p>
                <div class="video-meta">
                    <div class="date">
                        <i class="far fa-calendar-alt"></i>
                        <span>${formattedDate}</span>
                    </div>
                    <div class="views">
                        <i class="far fa-eye"></i>
                        <span>${formattedViews}次观看</span>
                    </div>
                </div>
            </div>
        `;

        // 设置初始状态
        videoItem.style.opacity = '0';
        videoItem.style.transform = 'translateY(20px)';

        videoListContainer.appendChild(videoItem);

        // 添加动画
        setTimeout(() => {
            videoItem.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            videoItem.style.opacity = '1';
            videoItem.style.transform = 'translateY(0)';
        }, 100 * index);
    });

    // 更新查看更多按钮状态
    updateViewMoreButtonVisibility(false);

    // 重新绑定视频点击事件
    initVideoClickEvents();
}

/**
 * 更新查看更多按钮状态
 * @param {boolean} hasMore - 是否还有更多视频
 */
function updateViewMoreButton(hasMore) {
    const viewMoreBtn = document.getElementById('view-more-btn');
    if (!viewMoreBtn) return;

    if (hasMore) {
        viewMoreBtn.style.display = 'inline-block';
    } else {
        viewMoreBtn.style.display = 'none';
    }
}

/**
 * 添加视频项目加载动画
 */
function animateVideoItems() {
    const videoItems = document.querySelectorAll('.video-item');

    videoItems.forEach((item, index) => {
        // 只为新添加的项目设置初始状态
        if (item.style.opacity !== '1') {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';

            // 添加动画
            setTimeout(() => {
                item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, 100 * (index % videosPerPage)); // 错开时间，创造级联效果
        }
    });
}

/**
 * 初始化视频点击事件
 */
function initVideoClickEvents() {
    // 使用事件委托，监听容器的点击事件
    const videoListContainer = document.getElementById('video-list-container');
    if (!videoListContainer) return;

    videoListContainer.addEventListener('click', function(event) {
        // 查找最近的视频占位符
        const placeholder = event.target.closest('.video-placeholder');
        if (!placeholder) return;

        const videoSrc = placeholder.getAttribute('data-video-src');
        const videoItem = placeholder.closest('.video-item');
        const videoTitle = videoItem.querySelector('.video-info h3').textContent;
        const videoDescription = videoItem.querySelector('.video-info p').textContent;

        // 打开视频模态框
        openVideoModal(videoSrc, videoTitle, videoDescription);
    });
}
