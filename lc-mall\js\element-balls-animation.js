/**
 * 元素球体动画 - 从顶部掉落并吸入化学瓶
 * 实现元素周期表中的元素以球体形式从顶部掉落，相互碰撞，然后被吸入化学瓶
 */

// 等待页面完全加载后再初始化元素球体动画
window.addEventListener('load', function() {
    initElementBallsAnimation();
});

// 元素周期表中的元素及其符号 - 只保留前四个周期
const periodicElements = [
    // 第1周期
    { symbol: 'H', name: 'Hydrogen', class: 'hydrogen' },
    { symbol: 'He', name: 'Helium', class: 'helium' },

    // 第2周期
    { symbol: 'Li', name: 'Lithium', class: 'lithium' },
    { symbol: 'Be', name: 'Beryllium', class: 'beryllium' },
    { symbol: 'B', name: 'Boron', class: 'boron' },
    { symbol: 'C', name: 'Carbon', class: 'carbon' },
    { symbol: 'N', name: 'Nitrogen', class: 'nitrogen' },
    { symbol: 'O', name: 'Oxygen', class: 'oxygen' },
    { symbol: 'F', name: 'Fluorine', class: 'fluorine' },
    { symbol: 'Ne', name: 'Neon', class: 'neon' },

    // 第3周期
    { symbol: 'Na', name: 'Sodium', class: 'sodium' },
    { symbol: 'Mg', name: 'Magnesium', class: 'magnesium' },
    { symbol: 'Al', name: 'Aluminum', class: 'aluminum' },
    { symbol: 'Si', name: 'Silicon', class: 'silicon' },
    { symbol: 'P', name: 'Phosphorus', class: 'phosphorus' },
    { symbol: 'S', name: 'Sulfur', class: 'sulfur' },
    { symbol: 'Cl', name: 'Chlorine', class: 'chlorine' },
    { symbol: 'Ar', name: 'Argon', class: 'argon' },

    // 第4周期
    { symbol: 'K', name: 'Potassium', class: 'potassium' },
    { symbol: 'Ca', name: 'Calcium', class: 'calcium' },
    { symbol: 'Sc', name: 'Scandium', class: 'scandium' },
    { symbol: 'Ti', name: 'Titanium', class: 'titanium' },
    { symbol: 'V', name: 'Vanadium', class: 'vanadium' },
    { symbol: 'Cr', name: 'Chromium', class: 'chromium' },
    { symbol: 'Mn', name: 'Manganese', class: 'manganese' },
    { symbol: 'Fe', name: 'Iron', class: 'iron' },
    { symbol: 'Co', name: 'Cobalt', class: 'cobalt' },
    { symbol: 'Ni', name: 'Nickel', class: 'nickel' },
    { symbol: 'Cu', name: 'Copper', class: 'copper' },
    { symbol: 'Zn', name: 'Zinc', class: 'zinc' },
    { symbol: 'Ga', name: 'Gallium', class: 'gallium' },
    { symbol: 'Ge', name: 'Germanium', class: 'germanium' },
    { symbol: 'As', name: 'Arsenic', class: 'arsenic' },
    { symbol: 'Se', name: 'Selenium', class: 'selenium' },
    { symbol: 'Br', name: 'Bromine', class: 'bromine' },
    { symbol: 'Kr', name: 'Krypton', class: 'krypton' }
];

// 球体物理属性
const balls = [];
const gravity = 0.2;
const friction = 0.99;
const elasticity = 0.7;
let animationId = null;
let suckInTimeout = null;

/**
 * 初始化元素球体动画
 */
function initElementBallsAnimation() {
    const container = document.getElementById('element-balls-container');
    if (!container) return;



    // 清除现有的动画和元素
    if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
    }

    if (suckInTimeout) {
        clearTimeout(suckInTimeout);
        suckInTimeout = null;
    }

    container.innerHTML = '';
    balls.length = 0;

    // 不再需要创建化学瓶中的气泡

    // 创建元素球体
    createElementBalls();

    // 开始动画
    animate();

    // 不再使用定时器自动重新开始
    // 现在会在所有球体完成三次掉落后自动重新开始
}

/**
 * 创建化学瓶中的气泡 - 此函数已不再使用，已移除化学瓶
 */
function createFlaskBubbles() {
    // 不再需要创建化学瓶中的气泡
}

/**
 * 创建元素球体
 */
function createElementBalls() {
    const container = document.getElementById('element-balls-container');
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const containerWidth = containerRect.width;

    // 计算中心区域的范围（容器宽度的中间60%区域）
    const centerAreaStart = containerWidth * 0.2;
    const centerAreaWidth = containerWidth * 0.6;

    // 检测是否为移动设备
    const isMobile = window.innerWidth < 768;

    // 根据设备类型调整元素数量和大小
    // 移动设备：更小的球体，更适合小屏幕
    // 桌面设备：更大的球体，更加明显
    const minSize = isMobile ? 25 : 35;
    const sizeRange = isMobile ? 15 : 25;

    // 创建一个元素索引数组
    const elementIndices = [];

    // 添加所有元素索引
    for (let i = 0; i < periodicElements.length; i++) {
        elementIndices.push(i);
    }

    // 打乱数组顺序，使元素出现顺序随机
    for (let i = elementIndices.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [elementIndices[i], elementIndices[j]] = [elementIndices[j], elementIndices[i]];
    }

    // 根据设备类型限制元素数量
    // 移动设备：只显示3个元素
    // 桌面设备：显示所有元素
    const maxElements = isMobile ? 3 : periodicElements.length;

    // 创建元素球体
    for (let i = 0; i < Math.min(maxElements, elementIndices.length); i++) {
        const element = periodicElements[elementIndices[i]];

        // 创建球体元素
        const ball = document.createElement('div');
        ball.className = `element-ball ${element.class}`;
        ball.textContent = element.symbol;

        // 随机大小
        // 移动端：30-50px
        // 桌面端：35-60px（更大，更加明显）
        const size = Math.random() * sizeRange + minSize;
        ball.style.width = `${size}px`;
        ball.style.height = `${size}px`;
        ball.style.fontSize = `${size * 0.6}px`; // 增大字体比例，使元素符号更加明显

        // 根据设备类型选择不同的水平分布范围
        const x = isMobile
            ? containerWidth * 0.3 + Math.random() * (containerWidth * 0.4) // 移动设备使用更窄的中心区域
            : centerAreaStart + Math.random() * centerAreaWidth; // 桌面设备使用更宽的中心区域

        // 移动设备使用更低的初始高度，桌面设备使用更高的初始高度
        const heightRange = isMobile ? 200 : 500;
        const y = -size - Math.random() * heightRange;

        // 使用transform而不是left/top，提高性能
        ball.style.transform = `translate(${x}px, ${y}px)`;
        // 设置初始位置为0,0，实际位置由transform控制
        ball.style.left = '0';
        ball.style.top = '0';

        // 添加到容器
        container.appendChild(ball);

        // 添加到球体数组，包含物理属性
        balls.push({
            element: ball,
            x: x,
            y: y,
            size: size,
            vx: isMobile ? (Math.random() * 0.5 - 0.25) : (Math.random() * 0.8 - 0.4), // 进一步降低水平速度范围
            vy: isMobile ? (Math.random() * 0.7 + 0.2) : (Math.random() * 1 + 0.3), // 进一步降低垂直速度，使下落更自然
            mass: size / 6, // 进一步增加质量，使球体下落更快
            suckIn: false, // 是否处于吸入状态
            dropCount: 0, // 掉落次数计数
            maxDrops: 1, // 只需要一次掉落就消失
            isDropping: false, // 是否正在掉落（用于防止连续计数）
            shouldRemove: false // 是否应该移除
        });
    }
}

/**
 * 动画循环
 */
function animate() {
    updateBalls();

    // 检查碰撞
    checkCollisions();

    // 请求下一帧动画
    animationId = requestAnimationFrame(animate);
}

/**
 * 更新球体位置
 */
function updateBalls() {
    const container = document.getElementById('element-balls-container');
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    // 使用transform而不是left/top，提高性能
    // 创建一个批量更新队列，减少DOM重排
    let updateQueue = [];

    balls.forEach(ball => {
        if (ball.suckIn) return; // 如果球体正在被吸入，跳过物理更新

        // 应用重力
        ball.vy += gravity;

        // 应用摩擦力
        ball.vx *= friction;
        ball.vy *= friction;

        // 更新位置
        ball.x += ball.vx;
        ball.y += ball.vy;

        // 边界碰撞检测
        // 左右边界
        if (ball.x < 0) {
            ball.x = 0;
            ball.vx = -ball.vx * elasticity;
        } else if (ball.x + ball.size > containerWidth) {
            ball.x = containerWidth - ball.size;
            ball.vx = -ball.vx * elasticity;
        }

        // 底部边界
        if (ball.y + ball.size > containerHeight) {
            ball.y = containerHeight - ball.size;
            ball.vy = -ball.vy * elasticity;

            // 如果垂直速度足够大，且球体正在下落（不是弹起），认为是一次有效的掉落
            if (Math.abs(ball.vy) > 1 && ball.vy > 0) {
                // 检查是否是新的掉落（防止连续多次计数）
                if (!ball.isDropping) {
                    ball.isDropping = true;
                    ball.dropCount++; // 增加掉落次数

                    console.log(`Ball dropped ${ball.dropCount} times`);

                    // 添加掉落次数的视觉反馈
                    ball.element.style.boxShadow = `0 0 20px rgba(255, 255, 255, 0.8)`;
                    setTimeout(() => {
                        if (!ball.shouldRemove) {
                            ball.element.style.boxShadow = `0 0 15px rgba(0, 0, 0, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.6)`;
                        }
                    }, 300);

                    // 在第三次掉落后，标记为需要移除并添加淡出效果
                    if (ball.dropCount >= ball.maxDrops) {
                        ball.shouldRemove = true;

                        // 添加淡出效果
                        ball.element.style.transition = "opacity 0.5s ease-out";
                        ball.element.style.opacity = "0";
                    }
                }
            } else if (ball.vy < 0) {
                // 球体正在弹起，重置掉落状态
                ball.isDropping = false;
            }
        }

        // 将更新添加到队列
        updateQueue.push({
            element: ball.element,
            x: ball.x,
            y: ball.y
        });
    });

    // 使用requestAnimationFrame批量更新DOM，减少重排
    requestAnimationFrame(() => {
        updateQueue.forEach(update => {
            // 使用transform而不是left/top，减少重排并提高性能
            update.element.style.transform = `translate(${update.x}px, ${update.y}px)`;
        });
    });

    // 检查是否所有球体都已完成三次掉落
    const allBallsCompleted = balls.every(ball => ball.shouldRemove);

    // 如果所有球体都已完成三次掉落，重新开始动画
    if (allBallsCompleted && balls.length > 0) {
        // 清除现有的定时器
        if (suckInTimeout) {
            clearTimeout(suckInTimeout);
            suckInTimeout = null;
        }

        // 设置短暂延迟后重新开始
        setTimeout(() => {
            initElementBallsAnimation();
        }, 500);
    }
}

/**
 * 检查球体之间的碰撞
 */
function checkCollisions() {
    // 检测是否为移动设备
    const isMobile = window.innerWidth < 768;

    // 桌面端使用空间分区优化
    if (!isMobile && balls.length > 100) {
        // 使用空间分区优化碰撞检测
        const gridSize = 50; // 网格大小
        const grid = {}; // 空间网格

        // 将球体放入网格
        for (let i = 0; i < balls.length; i++) {
            const ball = balls[i];
            if (ball.suckIn) continue; // 如果球体正在被吸入，跳过

            // 计算球体所在的网格坐标
            const gridX = Math.floor((ball.x + ball.size/2) / gridSize);
            const gridY = Math.floor((ball.y + ball.size/2) / gridSize);
            const gridKey = `${gridX},${gridY}`;

            // 将球体添加到网格
            if (!grid[gridKey]) {
                grid[gridKey] = [];
            }
            grid[gridKey].push(i);
        }

        // 检查每个网格中的球体碰撞
        for (const gridKey in grid) {
            const cellBalls = grid[gridKey];

            // 检查同一网格内的球体碰撞
            for (let i = 0; i < cellBalls.length; i++) {
                for (let j = i + 1; j < cellBalls.length; j++) {
                    checkBallCollision(balls[cellBalls[i]], balls[cellBalls[j]]);
                }
            }

            // 检查相邻网格的球体碰撞
            const [gx, gy] = gridKey.split(',').map(Number);
            for (let nx = gx - 1; nx <= gx + 1; nx++) {
                for (let ny = gy - 1; ny <= gy + 1; ny++) {
                    if (nx === gx && ny === gy) continue; // 跳过当前网格

                    const neighborKey = `${nx},${ny}`;
                    const neighborBalls = grid[neighborKey];

                    if (neighborBalls) {
                        for (let i = 0; i < cellBalls.length; i++) {
                            for (let j = 0; j < neighborBalls.length; j++) {
                                checkBallCollision(balls[cellBalls[i]], balls[neighborBalls[j]]);
                            }
                        }
                    }
                }
            }
        }
    } else {
        // 移动端或球体数量较少时使用简单的O(n²)算法
        for (let i = 0; i < balls.length; i++) {
            // 限制检测次数，提高性能
            const checkLimit = isMobile ? balls.length : Math.min(balls.length, i + 100);
            for (let j = i + 1; j < checkLimit; j++) {
                checkBallCollision(balls[i], balls[j]);
            }
        }
    }
}

/**
 * 检查两个球体之间的碰撞
 */
function checkBallCollision(ball1, ball2) {
    if (ball1.suckIn || ball2.suckIn) return; // 如果任一球体正在被吸入，跳过碰撞检测

    // 计算球体中心点之间的距离
    const dx = ball2.x + ball2.size/2 - (ball1.x + ball1.size/2);
    const dy = ball2.y + ball2.size/2 - (ball1.y + ball1.size/2);

    // 快速距离检查（避免开方运算）
    const distanceSquared = dx * dx + dy * dy;
    const minDistance = (ball1.size + ball2.size) / 2;
    const minDistanceSquared = minDistance * minDistance;

    if (distanceSquared < minDistanceSquared) {
        // 计算实际距离（只在可能碰撞时计算）
        const distance = Math.sqrt(distanceSquared);

        // 不再创建碰撞效果（移除落地烟雾）

        // 计算碰撞角度
        const angle = Math.atan2(dy, dx);

        // 计算碰撞前的速度分量
        const v1 = Math.sqrt(ball1.vx * ball1.vx + ball1.vy * ball1.vy);
        const v2 = Math.sqrt(ball2.vx * ball2.vx + ball2.vy * ball2.vy);

        // 计算碰撞前的角度
        const a1 = Math.atan2(ball1.vy, ball1.vx);
        const a2 = Math.atan2(ball2.vy, ball2.vx);

        // 计算碰撞后的速度分量（弹性碰撞）
        const m1 = ball1.mass;
        const m2 = ball2.mass;

        // 应用动量守恒和能量守恒
        const v1x = ((m1 - m2) * v1 * Math.cos(a1 - angle) + 2 * m2 * v2 * Math.cos(a2 - angle)) / (m1 + m2) * Math.cos(angle) + v1 * Math.sin(a1 - angle) * Math.cos(angle + Math.PI/2);
        const v1y = ((m1 - m2) * v1 * Math.cos(a1 - angle) + 2 * m2 * v2 * Math.cos(a2 - angle)) / (m1 + m2) * Math.sin(angle) + v1 * Math.sin(a1 - angle) * Math.sin(angle + Math.PI/2);
        const v2x = ((m2 - m1) * v2 * Math.cos(a2 - angle) + 2 * m1 * v1 * Math.cos(a1 - angle)) / (m1 + m2) * Math.cos(angle) + v2 * Math.sin(a2 - angle) * Math.cos(angle + Math.PI/2);
        const v2y = ((m2 - m1) * v2 * Math.cos(a2 - angle) + 2 * m1 * v1 * Math.cos(a1 - angle)) / (m1 + m2) * Math.sin(angle) + v2 * Math.sin(a2 - angle) * Math.sin(angle + Math.PI/2);

        // 更新速度
        ball1.vx = v1x * elasticity;
        ball1.vy = v1y * elasticity;
        ball2.vx = v2x * elasticity;
        ball2.vy = v2y * elasticity;

        // 分离球体，防止粘连
        const overlap = minDistance - distance;
        const separationX = overlap * Math.cos(angle);
        const separationY = overlap * Math.sin(angle);

        ball1.x -= separationX * 0.5;
        ball1.y -= separationY * 0.5;
        ball2.x += separationX * 0.5;
        ball2.y += separationY * 0.5;
    }
}

/**
 * 创建碰撞效果 - 此函数已不再使用，保留为空函数以避免潜在的引用错误
 */
function createCollisionEffect(/* x, y, size */) {
    // 不再创建碰撞效果（移除落地烟雾）
    // 参数被注释掉以避免未使用的参数警告
}

/**
 * 这个函数已不再使用，保留为空函数以避免潜在的引用错误
 */
function startSuckInAnimation() {
    // 不再执行吸入动画
    // 所有动画逻辑已移至initElementBallsAnimation函数中
}
