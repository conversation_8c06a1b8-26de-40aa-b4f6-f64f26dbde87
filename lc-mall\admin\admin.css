/* Admin Styles */
.admin-body {
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.admin-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Login Form */
.login-form {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    max-width: 400px;
    margin: 0 auto;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header img {
    height: 60px;
    margin-bottom: 1rem;
}

.login-header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
}

.login-form .form-group {
    margin-bottom: 1.5rem;
}

.login-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.login-form label i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.login-form input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--medium-gray);
    border-radius: 4px;
    font-family: inherit;
    transition: border-color 0.3s;
}

.login-form input:focus {
    border-color: var(--primary-color);
    outline: none;
}

.btn-login {
    width: 100%;
    background-color: var(--primary-color);
    margin-top: 1rem;
}

.btn-login:hover {
    background-color: var(--secondary-color);
}

.login-footer {
    text-align: center;
    margin-top: 1.5rem;
}

.login-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.login-footer a:hover {
    text-decoration: underline;
}

/* Admin Dashboard */
.admin-header {
    background-color: var(--primary-color);
    color: #fff;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Stats Table */
.stats-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.stats-table th,
.stats-table td {
    padding: 15px;
    text-align: center;
    border: 1px solid var(--medium-gray);
}

.stats-table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
}

.stats-table td {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stats-table i {
    margin-right: 8px;
}

.admin-header h1 {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
}

.admin-header h1 img {
    height: 30px;
    margin-right: 10px;
}

.admin-nav {
    display: flex;
    align-items: center;
}

.admin-nav a {
    color: #fff;
    margin-left: 1.5rem;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.admin-nav a:hover {
    opacity: 1;
}

.admin-content {
    padding: 2rem;
}

.admin-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.admin-card h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--medium-gray);
}

/* Product Management */
.product-actions {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-search {
    display: flex;
    align-items: center;
    flex: 1;
    max-width: 400px;
}

.product-search input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--medium-gray);
    border-radius: 4px 0 0 4px;
}

.product-search button {
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    padding: 8px 12px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.add-product-btn {
    background-color: var(--accent-color);
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.add-product-btn i {
    margin-right: 5px;
}

.product-table {
    width: 100%;
    border-collapse: collapse;
}

.product-table th,
.product-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.product-table th {
    background-color: var(--light-gray);
    font-weight: 600;
    color: var(--primary-color);
}

.product-table tr:hover {
    background-color: var(--light-gray);
}

.product-image-small,
.user-avatar-small {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

/* User avatar in modal */
.user-avatar {
    text-align: center;
    margin-bottom: 1.5rem;
}

.user-avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

/* User details layout */
.user-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.info-group {
    margin-bottom: 0.75rem;
}

.info-group label {
    font-weight: 600;
    color: var(--primary-color);
    margin-right: 0.5rem;
}

/* Filter options */
.filter-options {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-options select {
    padding: 8px 12px;
    border: 1px solid var(--medium-gray);
    border-radius: 4px;
    background-color: white;
}

/* 用户统计 */
.stats-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 15px 0;
    padding: 15px;
    background-color: var(--light-gray);
    border-radius: 4px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-item i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.stat-item strong {
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* 导出按钮 */
#export-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
}

#export-btn:hover {
    background-color: var(--accent-dark);
}

.table-actions {
    display: flex;
    gap: 10px;
}

.table-actions button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.edit-btn {
    color: var(--primary-color);
}

.delete-btn {
    color: #e74c3c;
}

/* Product Form */
.product-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
    border-color: #e53935;
    box-shadow: 0 0 0 1px #e53935;
}

.form-group input.error:focus,
.form-group textarea.error:focus,
.form-group select.error:focus {
    border-color: #e53935;
    box-shadow: 0 0 0 2px rgba(229, 57, 53, 0.3);
}

.form-group-full {
    grid-column: 1 / -1;
}

.form-section-title {
    grid-column: 1 / -1;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--primary-color);
    font-size: 1.2rem;
}

.form-actions {
    grid-column: 1 / -1;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1rem;
}

.cancel-btn {
    background-color: var(--medium-gray);
    color: var(--text-color);
}

.save-btn {
    background-color: var(--primary-color);
    color: #fff;
}

/* Image Upload */
.image-upload-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.image-preview {
    width: 100%;
    height: 200px;
    border: 1px dashed var(--medium-gray);
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--light-gray);
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.image-upload-controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.upload-options {
    display: flex;
    gap: 0.5rem;
}

/* 压缩进度条样式 */
.compression-progress {
    margin-top: 10px;
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background-color: #f0f0f0;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-bar-fill {
    height: 100%;
    background-color: var(--primary-color);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #666;
    text-align: center;
}

.upload-options .btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 0.9rem;
}

#upload-image-btn {
    background-color: var(--primary-color);
}

#use-placeholder-btn {
    background-color: var(--medium-gray);
}

/* Status Indicators */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-in-stock {
    background-color: #e3f9e5;
    color: #0d6832;
}

.status-low-stock {
    background-color: #fff8e6;
    color: #b25e09;
}

.status-out-of-stock {
    background-color: #fee7e7;
    color: #b42318;
}

.text-success {
    color: #0d6832;
}

.text-danger {
    color: #b42318;
}

/* Admin Notifications */
.admin-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1000;
    transform: translateY(-20px);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
    max-width: 400px;
}

.admin-notification.show {
    transform: translateY(0);
    opacity: 1;
}

.admin-notification .notification-icon {
    font-size: 1.5rem;
}

.admin-notification.success {
    border-left: 4px solid #0d6832;
}

.admin-notification.success .notification-icon {
    color: #0d6832;
}

.admin-notification.error {
    border-left: 4px solid #b42318;
}

.admin-notification.error .notification-icon {
    color: #b42318;
}

.admin-notification.info {
    border-left: 4px solid #2196f3;
}

.admin-notification.info .notification-icon {
    color: #2196f3;
}

.admin-notification .notification-message {
    font-size: 1rem;
    color: var(--text-color);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s;
    overflow-y: auto; /* 允许内容滚动 */
    padding: 20px; /* 添加内边距，防止内容贴边 */
}

.modal.show {
    opacity: 1;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    margin: 30px auto;
    padding: 20px;
    position: relative;
    max-width: 800px;
    transition: transform 0.3s;
    transform: translateY(20px);
}

.modal.show .modal-content {
    transform: translateY(0);
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: var(--primary-color);
}

/* Responsive Admin */
@media (max-width: 768px) {
    .product-form {
        grid-template-columns: 1fr;
    }

    .product-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .product-search {
        max-width: 100%;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .admin-notification {
        left: 20px;
        right: 20px;
        max-width: calc(100% - 40px);
    }

    .modal-content {
        margin: 10px auto;
        padding: 15px;
        max-width: calc(100% - 30px);
    }
}

/* Custom Confirm Dialog */
.confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1200;
}

.confirm-dialog-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 400px;
    width: 100%;
    text-align: center;
}

.confirm-dialog p {
    margin-bottom: 20px;
    font-size: 16px;
}

.confirm-dialog-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.confirm-dialog button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
}

.confirm-dialog .confirm-btn {
    background-color: var(--primary-color);
    color: white;
}

.confirm-dialog .confirm-btn:hover {
    background-color: var(--primary-dark);
}

.confirm-dialog .cancel-btn {
    background-color: #f0f0f0;
    color: #333;
}

.confirm-dialog .cancel-btn:hover {
    background-color: #e0e0e0;
}

/* 管理操作按钮样式 */
.admin-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.admin-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-actions .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.admin-actions .btn-primary {
    background-color: #007bff;
    color: white;
}

.admin-actions .btn-primary:hover {
    background-color: #0056b3;
}

.admin-actions .btn-secondary {
    background-color: #6c757d;
    color: white;
}

.admin-actions .btn-secondary:hover {
    background-color: #545b62;
}

/* 支付状态样式 */
.status-payment-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* 响应式管理按钮 */
@media (max-width: 768px) {
    .admin-actions {
        flex-direction: column;
        gap: 0.25rem;
        width: 100%;
    }

    .admin-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
