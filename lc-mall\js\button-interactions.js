/**
 * 按钮交互效果增强
 * 为所有按钮添加点击反馈、触觉反馈和声音反馈
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化按钮交互效果
    initButtonInteractions();
});

/**
 * 初始化所有按钮的交互效果
 */
function initButtonInteractions() {
    // 获取所有需要增强交互效果的元素
    const interactiveElements = document.querySelectorAll(`
        .btn,
        .category-link,
        .product-card .btn,
        .add-to-cart,
        .view-product,
        .pagination-link,
        .close,
        #checkout-btn,
        nav ul li a,
        .footer-nav a,
        .mobile-nav-toggle,
        .product-card,
        .floating-cart
    `);

    // 为每个元素添加点击事件监听器
    interactiveElements.forEach(element => {
        element.addEventListener('click', handleElementClick);
    });

    // 监听动态添加的元素
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // 元素节点
                        const newElements = node.querySelectorAll(`
                            .btn,
                            .category-link,
                            .product-card .btn,
                            .add-to-cart,
                            .view-product,
                            .pagination-link,
                            .close,
                            #checkout-btn,
                            nav ul li a,
                            .footer-nav a,
                            .mobile-nav-toggle,
                            .product-card,
                            .floating-cart
                        `);
                        newElements.forEach(element => {
                            element.addEventListener('click', handleElementClick);
                        });
                    }
                });
            }
        });
    });

    // 开始观察文档变化
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * 处理元素点击事件
 * @param {Event} e - 点击事件对象
 */
function handleElementClick(e) {
    const element = e.currentTarget;

    // 添加波纹效果
    addRippleEffect(element);

    // 添加触觉反馈
    addHapticFeedback();

    // 添加声音反馈
    playButtonClickSound(element);

    // 添加视觉反馈
    addVisualFeedback(element);
}

/**
 * 添加波纹效果
 * @param {HTMLElement} element - 被点击的元素
 */
function addRippleEffect(element) {
    // 添加点击类，触发波纹动画
    element.classList.add('clicked');

    // 动画结束后移除类
    setTimeout(() => {
        element.classList.remove('clicked');
    }, 600);
}

/**
 * 添加触觉反馈（如果浏览器支持）
 */
function addHapticFeedback() {
    if (navigator.vibrate) {
        navigator.vibrate(30); // 30毫秒的短振动
    }
}

/**
 * 播放按钮点击声音
 * @param {HTMLElement} element - 被点击的元素
 */
function playButtonClickSound(element) {
    try {
        // 创建音频上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // 根据元素类型选择不同的声音
        let oscillatorType = 'sine';
        let frequency = 800;
        let duration = 0.1;

        if (element.classList.contains('btn') || element.classList.contains('add-to-cart')) {
            // 主要按钮使用更明显的声音
            oscillatorType = 'sine';
            frequency = 800;
            duration = 0.15;
        } else if (element.classList.contains('category-link') || element.classList.contains('view-product')) {
            // 导航类按钮使用较轻的声音
            oscillatorType = 'sine';
            frequency = 600;
            duration = 0.1;
        } else if (element.classList.contains('close')) {
            // 关闭按钮使用较低的声音
            oscillatorType = 'triangle';
            frequency = 400;
            duration = 0.1;
        } else if (element.classList.contains('product-card')) {
            // 产品卡片使用较柔和的声音
            oscillatorType = 'sine';
            frequency = 500;
            duration = 0.08;
        }

        // 创建振荡器
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        // 连接节点
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // 设置音频参数
        oscillator.type = oscillatorType;
        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);

        // 设置音量渐变
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.01);
        gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + duration);

        // 播放音频
        oscillator.start();
        oscillator.stop(audioContext.currentTime + duration);
    } catch (e) {
        console.log('Audio context not supported or user interaction required');
    }
}

/**
 * 添加视觉反馈
 * @param {HTMLElement} element - 被点击的元素
 */
function addVisualFeedback(element) {
    // 对于按钮类元素，增强下沉效果
    if (element.classList.contains('btn') ||
        element.classList.contains('add-to-cart') ||
        element.classList.contains('view-product') ||
        element.classList.contains('pagination-link') ||
        element.classList.contains('checkout-btn')) {

        // 保存原始样式
        const originalTransform = element.style.transform;
        const originalBoxShadow = element.style.boxShadow;
        const originalBorderBottom = element.style.borderBottom;

        // 应用下沉效果
        element.style.transform = 'translateY(3px)';
        element.style.boxShadow = '0 0 0 rgba(0, 0, 0, 0.1)';
        element.style.borderBottom = '1px solid rgba(0, 0, 0, 0.2)';

        // 恢复原始样式
        setTimeout(() => {
            element.style.transform = originalTransform;
            element.style.boxShadow = originalBoxShadow;
            element.style.borderBottom = originalBorderBottom;
        }, 200);
    }

    // 对于特定类型的按钮添加特殊效果
    if (element.classList.contains('btn-accent') || element.classList.contains('add-to-cart')) {
        // 添加震动效果
        element.classList.add('btn-shake');
        setTimeout(() => {
            element.classList.remove('btn-shake');
        }, 400);
    }

    // 对于产品卡片，添加特殊高亮效果，但不添加位移
    if (element.classList.contains('product-card')) {
        const originalBoxShadow = element.style.boxShadow;
        element.style.boxShadow = '0 0 20px rgba(249, 115, 22, 0.5)';
        // 添加边框高亮而不是位移
        element.style.border = '1px solid rgba(249, 115, 22, 0.5)';
        setTimeout(() => {
            element.style.boxShadow = originalBoxShadow;
            element.style.border = '';
        }, 300);
    }
}
