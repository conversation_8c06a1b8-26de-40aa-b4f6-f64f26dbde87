/**
 * 团队成员图片加载失败时的备用方案
 */
document.addEventListener('DOMContentLoaded', function() {
    // 团队成员图片
    const teamImages = document.querySelectorAll('.team-member .member-photo img');
    
    // 备用图片URL
    const fallbackImages = {
        'ceo': 'images/team/ceo.jpg',
        'tech': 'images/team/tech.jpg',
        'sales': 'images/team/sales.jpg'
    };
    
    // 为每个图片添加错误处理
    teamImages.forEach(img => {
        img.addEventListener('error', function() {
            // 获取团队成员类型
            const memberType = this.closest('.team-member').classList.contains('ceo') ? 'ceo' : 
                              this.closest('.team-member').classList.contains('tech') ? 'tech' : 
                              this.closest('.team-member').classList.contains('sales') ? 'sales' : null;
            
            // 如果有对应的备用图片，则使用备用图片
            if (memberType && fallbackImages[memberType]) {
                this.src = fallbackImages[memberType];
            } else {
                // 否则使用通用备用图片
                this.src = 'images/team/default.jpg';
            }
        });
    });
});
