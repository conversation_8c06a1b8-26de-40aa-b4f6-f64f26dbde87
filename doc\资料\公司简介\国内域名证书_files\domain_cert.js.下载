function toguoji(){

    var domainName = $("#enDomainName1").val();
    if(domainName === ""){
        alert("请输入域名！");
        return;
    }
    var suffix = domainName.substring(domainName.lastIndexOf("."), domainName.length).toLowerCase();

    if(suffix === ".cn" || suffix === ".中国" || suffix === ".中國"){
        alert("请输入正确的国际域名！");
        return;
    }
    location.href="https://dcp.xinnet.com/dcp/domain_cert_guoji.html?domainName="+domainName
}

function toguonei(){

    var domainName = $("#cnDomainName1").val();
    if(domainName === ""){
        alert("请输入域名！");
        return;
    }
    var suffix = domainName.substring(domainName.lastIndexOf("."), domainName.length).toLowerCase();

    if(suffix !== ".cn" && suffix !== ".中国" && suffix !== ".中國"){
        alert("请输入正确的国内域名！");
        return;
    }
    location.href="https://dcp.xinnet.com/dcp/domain_cert_guonei.html?domainName="+domainName
}

function getKeyValue(url) {
	var result = {};
	var reg = new RegExp('([\?|&])(.+?)=([^&?]*)', 'ig');
	var arr = reg.exec(url);

	while (arr) {
		result[arr[2]] = arr[3];

		arr = reg.exec(url);
	}
	return result;
}
function queryDomainCertList(){

    var domainName =  $("#domainName").val();
    if(domainName !== ""){
        $.ajax({
            type : "POST",
            url : "https://dcp.xinnet.com/domainCert/listCert",
            contentType : "application/x-www-form-urlencoded; charset=UTF-8",
            data : {
                "domainName" : domainName
            },
            dataType : "json",
            cache : false,
            async : true,
            beforeSend: function () {
                //页面置灰
            },
            success : function(data) {
                //申请主体信息
                var code= data.data.code;
                if(code === "1000"){
                    $(".domainC").text(data.data.detail.domainName);
                    $(".domainE").text(data.data.detail.domainName);
                    $(".RegistrantC").text(data.data.detail.registrarCn);
                    $(".RegistrantE").text(data.data.detail.registrarEn);
                    $(".createC").text(data.data.detail.registrarDate);
                    $(".createE").text(data.data.detail.registrarDate);
                    $(".expireC").text(data.data.detail.closeDate);
                    $(".expireE").text(data.data.detail.closeDate);
                    $(".addressC").text(data.data.detail.addressCn);
                    $(".addressE").text(data.data.detail.addressEn);
                }else{

                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                if (XMLHttpRequest.status == 511) {
                    window.location.href = "https://dcp.xinnet.com"
                } else {
                    showDialog("操作失败,请稍后重试");
                }
            }
        });
    }
}

function queryDomainCertInfo(){

    $.ajax({
        type : "POST",
        url : "https://dcp.xinnet.com/domainCert/certInfo",
        contentType : "application/x-www-form-urlencoded; charset=UTF-8",
        data : {
        },
        dataType : "json",
        cache : false,
        async : true,
        beforeSend: function () {
            //页面置灰
        },
        success : function(data) {
            //申请主体信息
            var code= data.data.code;
            if(code === "1000"){

                domainName = data.data.detail.domainName;
                regOrignUserCn = data.data.detail.registrarCn;
                registrarDate = data.data.detail.registrarDate;
                closeDate = data.data.detail.closeDate;
                addressCn = data.data.detail.addressCn;

                div = data.data.flag;
                sureDownloadFlag = false;
                surePrintFlag = false;
                if(div === "int"){

                    regOrignUserEn = data.data.detail.registrarEn;
                    addressEn = data.data.detail.addressEn;

                    $("#abroad").css("display","block");
                    $("#domestic").css("display","none");

                    $(".domainCA").text(domainName);
                    $(".domainEA").text(domainName);
                    $(".RegistrantCA").text(regOrignUserCn);
                    $(".RegistrantEA").text(regOrignUserEn);
                    $(".createCA").text(registrarDate);
                    $(".createEA").text(registrarDate);
                    $(".expireCA").text(closeDate);
                    $(".expireEA").text(closeDate);
                    //$(".addressCA").text(addressCn);
                    //$(".addressEA").text(addressEn);
                    $("#ddA").text(domainName);
                    $("#ddrA").text(domainName);
                    $("#registrarCnSpanA").text(regOrignUserCn);
                }else {
                    $("#abroad").css("display","none");
                    $("#domestic").css("display","block");

                    $(".domainCD").text(domainName);
                    $(".RegistrantCD").text(regOrignUserCn);
                    $(".createCD").text(registrarDate);
                    $(".expireCD").text(closeDate);
                    //$(".addressCD").text(addressCn);
                    $("#ddD").text(domainName);
                    $("#registrarCnSpanD").text(regOrignUserCn);
                }

            }else{

            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            if (XMLHttpRequest.status == 511) {
                window.location.href = "https://dcp.xinnet.com"
            } else {
                showDialog("操作失败,请稍后重试");
            }
        }
    });
}