/* 产品介绍页面编辑模式样式 */

/* 编辑按钮 */
.edit-button,
.table-edit-button {
    background-color: #0088ff;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
    margin-top: 10px;
    display: inline-flex;
    align-items: center;
    transition: background-color 0.3s;
}

.edit-button:hover,
.table-edit-button:hover {
    background-color: #0066cc;
}

.edit-button i,
.table-edit-button i {
    margin-right: 5px;
}

.table-edit-button {
    margin-left: 15px;
    font-size: 14px;
    font-weight: normal;
}

/* 编辑模态框 */
.edit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.edit-modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 30px;
    position: relative;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #777;
}

.edit-modal h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 16px;
}

.form-group textarea {
    min-height: 150px;
    resize: vertical;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.save-btn,
.cancel-btn {
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
}

.save-btn {
    background-color: #0088ff;
    color: #fff;
    border: none;
}

.cancel-btn {
    background-color: #f1f1f1;
    color: #333;
    border: 1px solid #ddd;
}

/* 表格编辑 */
.table-edit-modal {
    width: 95%;
    max-width: 1000px;
}

.table-container {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.edit-table {
    width: 100%;
    border-collapse: collapse;
}

.edit-table th,
.edit-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.edit-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.edit-table input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

#add-row-btn {
    background-color: #2ecc71;
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 20px;
    display: inline-flex;
    align-items: center;
}

#add-row-btn:hover {
    background-color: #27ae60;
}

.delete-row-btn {
    background-color: #e74c3c;
    color: #fff;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.delete-row-btn:hover {
    background-color: #c0392b;
}

/* 图片预览 */
#image-preview {
    margin-top: 10px;
    border: 1px dashed #ddd;
    padding: 10px;
    text-align: center;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#image-preview img {
    max-width: 100%;
    max-height: 200px;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .edit-modal-content {
        width: 95%;
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .save-btn,
    .cancel-btn {
        width: 100%;
        margin-bottom: 10px;
    }
}
