/* 页面通用样式 */
.page-banner {
    background-color: #f5f5f5;
    padding: 30px 0;
    margin-bottom: 40px;
}

.page-banner h1 {
    font-size: 28px;
    color: #333;
    margin: 0 0 10px;
}

.breadcrumb {
    font-size: 14px;
    color: #666;
}

.breadcrumb a {
    color: #0066cc;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.section-title {
    text-align: center;
    margin-bottom: 40px;
}

.section-title h2 {
    font-size: 28px;
    color: #333;
    margin: 0 0 10px;
    position: relative;
    display: inline-block;
}

.section-title h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #0066cc;
    margin: 15px auto 0;
}

.section-title p {
    font-size: 16px;
    color: #666;
    margin: 0;
}

/* 企业风貌页面样式 */
.company-gallery {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 40px;
}

/* 移动设备响应式布局 */
@media (max-width: 992px) {
    .company-gallery {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .company-gallery {
        grid-template-columns: 1fr;
    }
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 250px; /* 固定高度，确保所有图片显示一致 */
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover; /* 确保图片填充整个容器并保持比例 */
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 12px;
    text-align: center;
    font-size: 15px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transform: translateY(0);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.gallery-item:hover .gallery-caption {
    background-color: rgba(0, 102, 204, 0.8);
    transform: translateY(0);
}

.company-description {
    margin-bottom: 40px;
}

.company-description h3 {
    font-size: 22px;
    color: #333;
    margin: 0 0 20px;
    position: relative;
    padding-left: 15px;
}

.company-description h3:before {
    content: '';
    position: absolute;
    left: 0;
    top: 5px;
    bottom: 5px;
    width: 4px;
    background-color: #0066cc;
}

.company-description p {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* 短视频页面样式 */
.videos-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 40px;
}

.video-item {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.video-item:hover {
    transform: translateY(-5px);
}

.video-thumbnail {
    position: relative;
    cursor: pointer;
}

.video-thumbnail img {
    width: 100%;
    height: auto;
    display: block;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 24px;
    opacity: 0.8;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.video-thumbnail:hover .play-button {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

.video-info {
    padding: 15px;
}

.video-info h3 {
    font-size: 18px;
    color: #333;
    margin: 0 0 10px;
}

.video-info p {
    font-size: 14px;
    color: #666;
    margin: 5px 0;
}

.video-modal-content {
    width: 80%;
    max-width: 800px;
}

.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 比例 */
    height: 0;
    overflow: hidden;
    margin-top: 20px;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* 新闻中心页面样式 */
.news-container {
    display: flex;
    gap: 30px;
    margin-bottom: 40px;
}

.news-sidebar {
    width: 25%;
}

.news-list {
    width: 75%;
}

.news-categories, .recent-news {
    background-color: #f5f5f5;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
}

.news-categories h3, .recent-news h3 {
    font-size: 18px;
    color: #333;
    margin: 0 0 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.news-categories ul, .recent-news ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.news-categories li, .recent-news li {
    margin-bottom: 10px;
}

.news-categories a, .recent-news a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    display: block;
    padding: 5px 0;
    transition: color 0.3s ease;
}

.news-categories a:hover, .recent-news a:hover {
    color: #0066cc;
}

.news-categories a.active {
    color: #0066cc;
    font-weight: bold;
}

.news-item {
    display: flex;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.news-image {
    width: 30%;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.news-content {
    width: 70%;
    padding: 20px;
}

.news-content h3 {
    font-size: 20px;
    margin: 0 0 10px;
}

.news-content h3 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.news-content h3 a:hover {
    color: #0066cc;
}

.news-meta {
    font-size: 14px;
    color: #999;
    margin-bottom: 10px;
}

.news-meta span {
    margin-right: 15px;
}

.news-content p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.read-more {
    font-size: 14px;
    color: #0066cc;
    text-decoration: none;
    display: inline-block;
}

.read-more:hover {
    text-decoration: underline;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.pagination a {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 5px;
    background-color: #f5f5f5;
    color: #666;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.pagination a:hover, .pagination a.active {
    background-color: #0066cc;
    color: #fff;
}

.pagination a.next {
    padding: 8px 15px;
}

/* 友情链接页面样式 */
.links-category {
    margin-bottom: 40px;
}

.links-category h3 {
    font-size: 22px;
    color: #333;
    margin: 0 0 20px;
    position: relative;
    padding-left: 15px;
}

.links-category h3:before {
    content: '';
    position: absolute;
    left: 0;
    top: 5px;
    bottom: 5px;
    width: 4px;
    background-color: #0066cc;
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.link-item {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
    text-align: center;
}

.link-item:hover {
    transform: translateY(-5px);
}

.link-item a {
    display: block;
    padding: 20px;
    text-decoration: none;
}

.link-item img {
    width: 100%;
    max-width: 200px;
    height: auto;
    margin-bottom: 10px;
}

.link-item h4 {
    font-size: 16px;
    color: #333;
    margin: 0;
}

/* 在线留言页面样式 */
.message-container {
    display: flex;
    gap: 30px;
    margin-bottom: 40px;
}

.message-info {
    width: 30%;
}

.message-form-container {
    width: 70%;
}

.info-card {
    display: flex;
    background-color: #f5f5f5;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    align-items: center;
}

.info-icon {
    width: 50px;
    height: 50px;
    background-color: #0066cc;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 20px;
    margin-right: 15px;
}

.info-content h3 {
    font-size: 18px;
    color: #333;
    margin: 0 0 5px;
}

.info-content p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.message-form {
    background-color: #f5f5f5;
    border-radius: 5px;
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
    width: 48%;
    display: inline-block;
    vertical-align: top;
}

.form-group.full-width {
    width: 100%;
}

.form-group label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
}

.form-group .required {
    color: #f00;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
}

.captcha-container {
    display: flex;
    align-items: center;
}

.captcha-container input {
    width: 120px;
    margin-right: 10px;
}

.captcha-image {
    display: flex;
    align-items: center;
}

.captcha-image img {
    height: 40px;
    margin-right: 10px;
}

.captcha-image button {
    background: none;
    border: none;
    color: #0066cc;
    cursor: pointer;
    font-size: 14px;
}

.form-actions {
    text-align: center;
    margin-top: 20px;
}

.form-actions button {
    margin: 0 10px;
}

.map-container {
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .company-gallery, .videos-grid, .links-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .news-container, .message-container {
        flex-direction: column;
    }

    .news-sidebar, .news-list, .message-info, .message-form-container {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .company-gallery, .videos-grid, .links-grid {
        grid-template-columns: 1fr;
    }

    .news-item {
        flex-direction: column;
    }

    .news-image, .news-content {
        width: 100%;
    }

    .form-group {
        width: 100%;
    }
}
