#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
微信支付处理模块
"""

import os
import json
import time
import uuid
import hashlib
import base64
import requests
import logging
from datetime import datetime
from cryptography.hazmat.primitives.asymmetric.rsa import RSAPrivateKey
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.serialization import load_pem_private_key

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('wechat_pay')

# 微信支付API地址
WECHAT_PAY_API = {
    'unified_order': 'https://api.mch.weixin.qq.com/v3/pay/transactions/native',
    'query_order': 'https://api.mch.weixin.qq.com/v3/pay/transactions/id/{transaction_id}',
    'close_order': 'https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/{out_trade_no}/close',
    'refund': 'https://api.mch.weixin.qq.com/v3/refund/domestic/refunds'
}

# 配置文件路径
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'wechat_pay_config.json')

class WeChatPay:
    """微信支付处理类"""

    def __init__(self):
        """初始化微信支付"""
        self.config = self._load_config()
        self.private_key = self._load_private_key()

    def _load_config(self):
        """加载微信支付配置"""
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            logger.error(f"加载微信支付配置失败: {str(e)}")
            return {}

    def _load_private_key(self):
        """加载商户私钥"""
        try:
            with open(self.config.get('key_path'), 'rb') as f:
                private_key_data = f.read()
            private_key = load_pem_private_key(private_key_data, password=None)
            return private_key
        except Exception as e:
            logger.error(f"加载商户私钥失败: {str(e)}")
            return None

    def _generate_sign(self, method, url_path, body):
        """生成签名"""
        timestamp = str(int(time.time()))
        nonce_str = str(uuid.uuid4()).replace('-', '')
        
        # 构造签名串
        sign_str = method + "\n"
        sign_str += url_path + "\n"
        sign_str += timestamp + "\n"
        sign_str += nonce_str + "\n"
        sign_str += body + "\n"
        
        # 使用私钥签名
        signature = self.private_key.sign(
            sign_str.encode('utf-8'),
            padding.PKCS1v15(),
            hashes.SHA256()
        )
        
        # Base64编码
        signature_base64 = base64.b64encode(signature).decode('utf-8')
        
        # 构造认证头
        auth = f'WECHATPAY2-SHA256-RSA2048 mchid="{self.config["mchid"]}",'
        auth += f'nonce_str="{nonce_str}",'
        auth += f'signature="{signature_base64}",'
        auth += f'timestamp="{timestamp}",'
        auth += f'serial_no="{self.config["serial_no"]}"'
        
        return auth

    def create_order(self, order_data):
        """创建微信支付订单"""
        try:
            # 构造请求数据
            out_trade_no = order_data.get('id')
            total_amount = int(float(order_data.get('total', 0)) * 100)  # 转换为分
            description = f"龙驰新材料订单-{out_trade_no}"
            
            request_data = {
                "appid": self.config["appid"],
                "mchid": self.config["mchid"],
                "description": description,
                "out_trade_no": out_trade_no,
                "notify_url": self.config["notify_url"],
                "amount": {
                    "total": total_amount,
                    "currency": "CNY"
                }
            }
            
            # 将请求数据转换为JSON字符串
            request_body = json.dumps(request_data)
            
            # 生成签名
            url_path = "/v3/pay/transactions/native"
            authorization = self._generate_sign("POST", url_path, request_body)
            
            # 发送请求
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Authorization": authorization
            }
            
            response = requests.post(
                WECHAT_PAY_API['unified_order'],
                data=request_body,
                headers=headers
            )
            
            # 解析响应
            if response.status_code == 200:
                result = response.json()
                logger.info(f"创建微信支付订单成功: {result}")
                return {
                    "success": True,
                    "code_url": result.get("code_url"),
                    "out_trade_no": out_trade_no
                }
            else:
                logger.error(f"创建微信支付订单失败: {response.text}")
                return {
                    "success": False,
                    "error": response.text
                }
        except Exception as e:
            logger.error(f"创建微信支付订单异常: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def query_order(self, transaction_id=None, out_trade_no=None):
        """查询微信支付订单"""
        try:
            if transaction_id:
                # 使用微信支付订单号查询
                url_path = f"/v3/pay/transactions/id/{transaction_id}"
                url = WECHAT_PAY_API['query_order'].format(transaction_id=transaction_id)
                params = {"mchid": self.config["mchid"]}
            elif out_trade_no:
                # 使用商户订单号查询
                url_path = f"/v3/pay/transactions/out-trade-no/{out_trade_no}"
                url = f"https://api.mch.weixin.qq.com{url_path}"
                params = {"mchid": self.config["mchid"]}
            else:
                return {
                    "success": False,
                    "error": "缺少订单号参数"
                }
            
            # 生成签名
            authorization = self._generate_sign("GET", url_path, "")
            
            # 发送请求
            headers = {
                "Accept": "application/json",
                "Authorization": authorization
            }
            
            response = requests.get(url, params=params, headers=headers)
            
            # 解析响应
            if response.status_code == 200:
                result = response.json()
                logger.info(f"查询微信支付订单成功: {result}")
                return {
                    "success": True,
                    "data": result
                }
            else:
                logger.error(f"查询微信支付订单失败: {response.text}")
                return {
                    "success": False,
                    "error": response.text
                }
        except Exception as e:
            logger.error(f"查询微信支付订单异常: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def verify_notify(self, headers, body):
        """验证微信支付通知"""
        # 实际项目中需要实现通知验签逻辑
        # 这里简化处理，直接返回成功
        return True

# 创建微信支付实例
wechat_pay = WeChatPay()
