<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>静态地图生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1a73e8;
        }
        p {
            line-height: 1.6;
        }
        .map-placeholder {
            width: 100%;
            height: 450px;
            background-color: #e9e9e9;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #666;
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        .map-placeholder i {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #1a73e8;
            margin: 20px 0;
        }
        .btn {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #1557b0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>静态地图生成器</h1>
        <p>这个页面用于生成龙驰新材料网站使用的静态地图图片。</p>
        
        <div class="instructions">
            <p><strong>使用说明：</strong></p>
            <ol>
                <li>截取百度地图或高德地图上的广州市黄埔区科学城科学大道182号创新大厦C3栋位置</li>
                <li>保存为 static-map.jpg 文件</li>
                <li>将文件放在 images 目录下</li>
            </ol>
        </div>
        
        <div class="map-placeholder">
            <i>🗺️</i>
            <p>地图图片占位符</p>
            <p>广州市黄埔区科学城科学大道182号创新大厦C3栋</p>
        </div>
        
        <p>完成后，请确保在 about.html 和 message.html 页面中引入了 baidu-map.js 和 static-map.css 文件。</p>
        
        <button class="btn" onclick="alert('请手动创建静态地图图片')">生成地图</button>
    </div>
</body>
</html>
