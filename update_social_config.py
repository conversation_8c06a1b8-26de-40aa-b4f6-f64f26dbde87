#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
社交登录配置更新工具
用于更新微信和QQ的AppID和AppSecret
"""

import os
import json
import argparse

# 配置文件路径
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "lc-mall", "data")
CONFIG_FILE = os.path.join(DATA_DIR, "social_config.json")

def load_config():
    """加载配置文件"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"读取配置文件失败: {str(e)}")
            return get_default_config()
    else:
        print(f"配置文件不存在，将创建新文件: {CONFIG_FILE}")
        return get_default_config()

def get_default_config():
    """获取默认配置"""
    return {
        "wechat": {
            "enabled": True,
            "app_id": "wx_your_app_id_here",
            "app_secret": "wx_your_app_secret_here",
            "redirect_uri": "http://your-domain.com/auth/wechat/callback"
        },
        "qq": {
            "enabled": True,
            "app_id": "qq_your_app_id_here",
            "app_secret": "qq_your_app_secret_here",
            "redirect_uri": "http://your-domain.com/auth/qq/callback"
        }
    }

def save_config(config):
    """保存配置文件"""
    # 确保目录存在
    os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
    
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"配置已保存到: {CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"保存配置文件失败: {str(e)}")
        return False

def update_wechat_config(app_id=None, app_secret=None, redirect_uri=None, enabled=None):
    """更新微信配置"""
    config = load_config()
    
    if app_id:
        config["wechat"]["app_id"] = app_id
    if app_secret:
        config["wechat"]["app_secret"] = app_secret
    if redirect_uri:
        config["wechat"]["redirect_uri"] = redirect_uri
    if enabled is not None:
        config["wechat"]["enabled"] = enabled
    
    return save_config(config)

def update_qq_config(app_id=None, app_secret=None, redirect_uri=None, enabled=None):
    """更新QQ配置"""
    config = load_config()
    
    if app_id:
        config["qq"]["app_id"] = app_id
    if app_secret:
        config["qq"]["app_secret"] = app_secret
    if redirect_uri:
        config["qq"]["redirect_uri"] = redirect_uri
    if enabled is not None:
        config["qq"]["enabled"] = enabled
    
    return save_config(config)

def show_config():
    """显示当前配置"""
    config = load_config()
    print("\n当前社交登录配置:")
    print("-" * 50)
    print("微信配置:")
    print(f"  启用状态: {'启用' if config['wechat']['enabled'] else '禁用'}")
    print(f"  AppID: {config['wechat']['app_id']}")
    print(f"  AppSecret: {'*' * len(config['wechat'].get('app_secret', ''))}")
    print(f"  回调地址: {config['wechat']['redirect_uri']}")
    print("\nQQ配置:")
    print(f"  启用状态: {'启用' if config['qq']['enabled'] else '禁用'}")
    print(f"  AppID: {config['qq']['app_id']}")
    print(f"  AppSecret: {'*' * len(config['qq'].get('app_secret', ''))}")
    print(f"  回调地址: {config['qq']['redirect_uri']}")
    print("-" * 50)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="社交登录配置更新工具")
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 显示配置命令
    show_parser = subparsers.add_parser("show", help="显示当前配置")
    
    # 更新微信配置命令
    wechat_parser = subparsers.add_parser("wechat", help="更新微信配置")
    wechat_parser.add_argument("--app-id", help="微信AppID")
    wechat_parser.add_argument("--app-secret", help="微信AppSecret")
    wechat_parser.add_argument("--redirect-uri", help="微信回调地址")
    wechat_parser.add_argument("--enable", action="store_true", help="启用微信登录")
    wechat_parser.add_argument("--disable", action="store_true", help="禁用微信登录")
    
    # 更新QQ配置命令
    qq_parser = subparsers.add_parser("qq", help="更新QQ配置")
    qq_parser.add_argument("--app-id", help="QQ AppID")
    qq_parser.add_argument("--app-secret", help="QQ AppSecret")
    qq_parser.add_argument("--redirect-uri", help="QQ回调地址")
    qq_parser.add_argument("--enable", action="store_true", help="启用QQ登录")
    qq_parser.add_argument("--disable", action="store_true", help="禁用QQ登录")
    
    args = parser.parse_args()
    
    if args.command == "show" or not args.command:
        show_config()
    elif args.command == "wechat":
        enabled = None
        if args.enable:
            enabled = True
        elif args.disable:
            enabled = False
        
        update_wechat_config(
            app_id=args.app_id,
            app_secret=args.app_secret,
            redirect_uri=args.redirect_uri,
            enabled=enabled
        )
        show_config()
    elif args.command == "qq":
        enabled = None
        if args.enable:
            enabled = True
        elif args.disable:
            enabled = False
        
        update_qq_config(
            app_id=args.app_id,
            app_secret=args.app_secret,
            redirect_uri=args.redirect_uri,
            enabled=enabled
        )
        show_config()

if __name__ == "__main__":
    main()
