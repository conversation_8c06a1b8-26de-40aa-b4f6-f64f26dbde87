<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>留言管理 - 龙驰新材料</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header class="admin-header">
        <h1><img src="../images/logo.png" alt="龙驰新材料"> 龙驰新材料管理后台</h1>
        <nav class="admin-nav">
            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> 控制面板</a>
            <a href="products.html"><i class="fas fa-box"></i> 产品管理</a>
            <a href="orders.html"><i class="fas fa-shopping-cart"></i> 订单管理</a>
            <a href="users.html"><i class="fas fa-users"></i> 用户管理</a>
            <a href="messages.html" class="active"><i class="fas fa-comments"></i> 留言管理</a>
            <a href="sms.html"><i class="fas fa-sms"></i> 短信记录</a>
            <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出</a>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-content">
            <div class="admin-card">
                <h2>留言管理</h2>

                <div class="product-actions">
                    <div class="product-search">
                        <input type="text" id="message-search" placeholder="搜索留言...">
                        <button id="search-btn"><i class="fas fa-search"></i></button>
                    </div>
                    <div class="filter-options">
                        <select id="status-filter">
                            <option value="all">全部状态</option>
                            <option value="unread">未读</option>
                            <option value="read">已读</option>
                            <option value="replied">已回复</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="product-table" id="message-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>姓名</th>
                                <th>电话</th>
                                <th>留言内容</th>
                                <th>提交时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="messages-table">
                            <!-- 留言数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                <div class="empty-table" id="empty-messages" style="display: none;">
                    <i class="fas fa-comments"></i>
                    <p>暂无留言数据</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 留言详情模态框 -->
    <div id="message-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>留言详情</h2>
            <div id="message-detail">
                <!-- 留言详情将通过JavaScript动态加载 -->
            </div>
            <div class="modal-actions">
                <button id="mark-read-btn" class="btn btn-primary">标记为已读</button>
                <button id="mark-replied-btn" class="btn btn-success">标记为已回复</button>
                <button id="delete-message-btn" class="btn btn-danger">删除留言</button>
            </div>
        </div>
    </div>

    <!-- 引入API客户端 -->
    <script src="../js/api-client.js"></script>
    <!-- 引入管理后台脚本 -->
    <script src="admin.js"></script>
    <script src="messages.js"></script>
</body>
</html>
