/* Base Styles */
:root {
    /* Modern Color Palette */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #60a5fa;
    --secondary-color: #0f172a;
    --accent-color: #f97316;
    --accent-hover: #ea580c;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-color: #1e293b;
    --text-light: #64748b;
    --text-lighter: #94a3b8;
    --bg-color: #ffffff;
    --bg-light: #f8fafc;
    --bg-dark: #f1f5f9;
    --border-color: #e2e8f0;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-full: 9999px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-light);
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 600;
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    text-align: center;
}

.btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn:active {
    transform: translateY(0);
}

.btn-accent {
    background-color: var(--accent-color);
}

.btn-accent:hover {
    background-color: var(--accent-hover);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--secondary-color);
    position: relative;
    padding-bottom: 1rem;
    font-weight: 700;
    font-size: 2rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-full);
}

/* Top Bar */
.top-bar {
    background-color: var(--bg-color);
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.top-bar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-company-info {
    text-align: center;
    flex-grow: 1;
    margin: 0 auto;
}

.header-company-info h2 {
    font-size: 1.6rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 0.5rem;
    text-shadow: none;
    letter-spacing: 1px;
}

.header-company-info p {
    font-size: 1rem;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    white-space: normal;
    overflow: visible;
}

.hotline {
    font-size: 0.9rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.hotline::before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23f97316"><path d="M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1z"/></svg>');
    background-size: contain;
    margin-right: 8px;
}

.hotline span {
    color: var(--accent-color);
    font-weight: 700;
    font-size: 1.1rem;
}

/* Header */
header {
    background-color: var(--primary-color);
    box-shadow: var(--shadow);
    padding: 0.5rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    height: 70px;
    transition: transform var(--transition-fast);
}

.logo:hover {
    /* 移除鼠标滑过效果 */
}

header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-container {
    display: flex;
    align-items: center;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 2rem;
}

nav ul li a {
    color: white;
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: color var(--transition-fast);
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    transition: width var(--transition);
    border-radius: var(--radius-full);
}

nav ul li a:hover {
    color: rgba(255, 255, 255, 0.8);
}

nav ul li a:hover::after,
nav ul li a.active::after {
    width: 100%;
}

.mobile-nav-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: white;
    background: transparent;
    border: none;
    transition: color var(--transition-fast);
}

.mobile-nav-toggle:hover {
    color: rgba(255, 255, 255, 0.8);
}

.cart-icon {
    position: relative;
    margin-left: 2rem;
    transition: transform 0.3s ease;
}

.cart-icon a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background-color: rgba(255, 255, 255, 0.2);
    transition: all var(--transition-fast);
}

.cart-icon a:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* 购物车图标脉冲动画 */
@keyframes cartPulse {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.4) rotate(-15deg); }
    50% { transform: scale(1.2) rotate(10deg); }
    75% { transform: scale(1.4) rotate(-5deg); }
    100% { transform: scale(1) rotate(0deg); }
}

.cart-icon-pulse {
    animation: cartPulse 0.6s ease;
}

.cart-icon-pulse a {
    box-shadow: 0 0 20px rgba(255, 71, 87, 0.8);
}

.cart-icon-pulse i {
    color: var(--accent-color) !important;
}

/* 粒子效果 */
.particle {
    position: fixed;
    z-index: 9997;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    pointer-events: none;
    opacity: 0.8;
    animation: particleFade 1s forwards;
}

@keyframes particleFade {
    0% { transform: scale(0.8); opacity: 0.8; }
    100% { transform: scale(0); opacity: 0; }
}

.cart-icon i {
    font-size: 1.2rem;
    color: white;
}

#cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--accent-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    min-width: 20px;
    height: 20px;
    border-radius: var(--radius-full);
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: var(--shadow-sm);
    padding: 0 4px; /* 添加水平内边距 */
    z-index: 101; /* 确保在最上层 */
}

/* Hero Section */
.hero {
    position: relative;
    background-image: linear-gradient(to right, rgba(15, 23, 42, 0.9), rgba(15, 23, 42, 0.7)), url('https://via.placeholder.com/1920x800?text=龙驰新材料');
    background-size: cover;
    background-position: center;
    color: white;
    text-align: center;
    padding: 8rem 1rem;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(37, 99, 235, 0.1), transparent 50%);
    z-index: 1;
}

.hero-content {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    z-index: 2;
}

.hero h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    background: linear-gradient(to right, #ffffff, #60a5fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero .btn {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    box-shadow: var(--shadow-md);
    background-color: var(--accent-color);
    transition: all var(--transition);
}

.hero .btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    background-color: var(--accent-hover);
}

/* Categories Section */
.categories {
    padding: 6rem 2rem;
    background-color: var(--bg-color);
}

.category-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 1280px;
    margin: 0 auto;
}

.category-card {
    background-color: var(--bg-color);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all var(--transition);
    text-align: center;
    position: relative;
    border: 1px solid var(--border-color);
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-light);
}

.category-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform var(--transition);
}

.category-card:hover img {
    transform: scale(1.05);
}

.category-card h3 {
    padding: 1.25rem 1rem 0.75rem;
    color: var(--secondary-color);
    font-weight: 600;
    font-size: 1.25rem;
}

.category-card .btn-small {
    margin: 0 auto 1.5rem;
    background-color: var(--primary-color);
    display: inline-flex;
    transition: all var(--transition);
}

.category-card .btn-small:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Products Section */
.products {
    padding: 3rem 2rem;
    background-color: var(--bg-light);
    position: relative;
}

.products::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at bottom left, rgba(37, 99, 235, 0.05), transparent 50%);
    z-index: 0;
}

/* Product Search */
.product-search-container {
    position: relative;
    z-index: 1;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.search-box {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    overflow: hidden;
    background-color: var(--bg-color);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.search-box:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-box input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    outline: none;
    font-family: inherit;
    font-size: 0.95rem;
    background-color: transparent;
}

.search-box button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0 1.25rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-box button:hover {
    background-color: var(--primary-dark);
}

/* Product Layout */
.product-layout {
    position: relative;
    z-index: 1;
    display: flex;
    gap: 2rem;
    margin-top: 0;
}

/* Category Sidebar */
.category-sidebar {
    width: 250px;
    flex-shrink: 0;
}

.category-sidebar h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.category-nav {
    margin-bottom: 2rem;
}

.category-nav li {
    margin-bottom: 0.5rem;
}

.category-link {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    border-radius: var(--radius);
    transition: all var(--transition-fast);
    font-weight: 500;
    position: relative;
}

.category-link:hover {
    background-color: var(--bg-dark);
    color: var(--primary-color);
    transform: translateX(5px);
}

.category-link.active {
    background-color: var(--primary-color);
    color: white;
}

.category-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 70%;
    background-color: var(--accent-color);
    border-radius: 0 var(--radius) var(--radius) 0;
}

.filter-section {
    margin-bottom: 2rem;
}

.filter-section select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    background-color: var(--bg-color);
    font-family: inherit;
    font-size: 0.95rem;
    color: var(--text-color);
    cursor: pointer;
    outline: none;
    transition: all var(--transition-fast);
}

.filter-section select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    display: inline-block;
    padding: 0.5rem 0.75rem;
    background-color: var(--bg-dark);
    color: var(--text-color);
    border-radius: var(--radius);
    font-size: 0.85rem;
    transition: all var(--transition-fast);
}

.tag:hover {
    background-color: var(--primary-light);
    color: white;
}

.tag.active {
    background-color: var(--primary-color);
    color: white;
}

/* Product Content */
.product-content {
    flex: 1;
    min-width: 0; /* Prevent flex item from overflowing */
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.product-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--secondary-color);
}

.product-results {
    position: relative;
    z-index: 1;
}

.product-count {
    color: var(--text-light);
    font-size: 0.95rem;
}

.product-pagination {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: center;
    margin-top: 3rem;
    gap: 0.5rem;
}

.pagination-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius);
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    font-weight: 500;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.pagination-btn:hover {
    background-color: var(--primary-light);
    color: white;
    border-color: var(--primary-light);
}

.pagination-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.product-container {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.product-card {
    background-color: var(--bg-color);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all var(--transition);
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid var(--border-color);
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-light);
}

.product-card-header {
    position: relative;
    overflow: hidden;
}

.product-card img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform var(--transition);
}

.product-card:hover img {
    transform: scale(1.05);
}

.product-badges {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 2;
}

.product-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-new {
    background-color: var(--primary-color);
    color: white;
}

.badge-sale {
    background-color: var(--accent-color);
    color: white;
}

.badge-featured {
    background-color: var(--success-color);
    color: white;
}

.badge-out-of-stock {
    background-color: var(--text-lighter);
    color: white;
}

.product-category-tag {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    z-index: 2;
}

.product-info {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.product-info h3 {
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 1.25rem;
    line-height: 1.4;
}

.product-meta {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.product-sku {
    font-size: 0.8rem;
    color: var(--text-lighter);
}

.product-rating {
    margin-left: auto;
    display: flex;
    align-items: center;
    color: var(--warning-color);
    font-size: 0.9rem;
}

.product-rating span {
    margin-left: 4px;
    color: var(--text-light);
    font-size: 0.8rem;
}

.product-info p {
    color: var(--text-light);
    margin-bottom: 1rem;
    font-size: 0.95rem;
    line-height: 1.6;
    flex-grow: 1;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-stock {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.85rem;
}

.stock-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.in-stock .stock-indicator {
    background-color: var(--success-color);
}

.low-stock .stock-indicator {
    background-color: var(--warning-color);
}

.out-of-stock .stock-indicator {
    background-color: var(--danger-color);
}

.in-stock {
    color: var(--success-color);
}

.low-stock {
    color: var(--warning-color);
}

.out-of-stock {
    color: var(--danger-color);
}

.product-info .price {
    color: var(--accent-color);
    font-weight: 700;
    font-size: 1.5rem;
    margin: 0.75rem 0;
    display: flex;
    align-items: center;
}

.product-info .price::before {
    content: '¥';
    font-size: 1rem;
    margin-right: 0.25rem;
    font-weight: 400;
    color: var(--text-lighter);
}

.product-info .original-price {
    color: var(--text-lighter);
    text-decoration: line-through;
    font-size: 1rem;
    margin-left: 0.75rem;
}

.product-actions {
    display: flex;
    justify-content: space-between;
    margin-top: auto;
    gap: 0.75rem;
}

.product-actions .btn-small {
    flex: 1;
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    border-radius: var(--radius);
    transition: all var(--transition-fast);
}

.product-actions .view-product {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.product-actions .view-product:hover {
    background-color: var(--primary-color);
    color: white;
}

.product-actions .add-to-cart {
    background-color: var(--accent-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-actions .add-to-cart i {
    margin-left: 6px;
    font-size: 0.8rem;
}

.product-actions .add-to-cart:hover {
    background-color: var(--accent-hover);
    transform: translateY(-2px);
}

.product-actions .add-to-cart[disabled] {
    background-color: var(--text-lighter);
    cursor: not-allowed;
    transform: none;
}

/* No Products Found */
.no-products {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    background-color: var(--bg-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.no-products p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.no-products .btn {
    background-color: var(--primary-color);
}

/* About Section */
.about {
    padding: 6rem 2rem;
    background-color: var(--bg-color);
    position: relative;
    overflow: hidden;
}

.about::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(37, 99, 235, 0.03), transparent 70%);
    z-index: 0;
    border-radius: 50%;
}

.about-content {
    position: relative;
    z-index: 1;
    display: flex;
    flex-wrap: wrap;
    max-width: 1280px;
    margin: 0 auto;
    gap: 3rem;
    align-items: center;
}

.about-image {
    flex: 1;
    min-width: 300px;
}

.about-image img {
    width: 100%;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition), box-shadow var(--transition);
}

.about-image img:hover {
    transform: scale(1.02) rotate(1deg);
    box-shadow: var(--shadow-lg);
}

.about-text {
    flex: 2;
    min-width: 300px;
}

.about-text p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
    color: var(--text-color);
    font-size: 1.05rem;
}

.about-text p:last-child {
    margin-bottom: 0;
}

/* Contact Section */
.contact {
    padding: 6rem 2rem;
    background-color: var(--bg-light);
    position: relative;
}

.contact::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--border-color), transparent);
}

.contact-container {
    display: flex;
    flex-wrap: wrap;
    max-width: 1280px;
    margin: 0 auto;
    gap: 3rem;
}

.contact-info {
    flex: 1;
    min-width: 300px;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    transition: transform var(--transition-fast);
}

.contact-item:hover {
    transform: translateX(5px);
}

.contact-item i {
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-right: 1rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-dark);
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

.contact-item:hover i {
    background-color: var(--primary-color);
    color: white;
    transform: rotate(10deg);
}

.contact-item p {
    color: var(--text-color);
    font-size: 1.05rem;
}

.contact-form {
    flex: 2;
    min-width: 300px;
    background-color: var(--bg-color);
    padding: 2.5rem;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: box-shadow var(--transition), transform var(--transition);
}

.contact-form:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-5px);
}

.contact-form h3 {
    margin-bottom: 0.5rem;
    color: var(--secondary-color);
    font-weight: 700;
    font-size: 1.5rem;
    position: relative;
}

.form-title-underline {
    width: 50px;
    height: 3px;
    background: linear-gradient(to right, #4169e1, #4169e1);
    border-radius: var(--radius-full);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.75rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-family: inherit;
    background-color: var(--bg-light);
    color: var(--text-color);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-form .btn {
    width: 100%;
    padding: 0.875rem;
    font-size: 1rem;
    margin-top: 1rem;
}

.btn-submit {
    background-color: #f97316;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.btn-submit:hover {
    background-color: #ea580c;
}

/* Footer */
footer {
    background-color: var(--secondary-color);
    color: white;
    padding: 5rem 2rem 2rem;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1280px;
    margin: 0 auto;
    gap: 3rem;
}

.footer-logo {
    flex: 1;
    min-width: 250px;
}

.footer-logo h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 1rem;
    background: linear-gradient(to right, #ffffff, var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-small {
    height: 35px;
    margin-right: 0.75rem;
    transition: transform var(--transition-fast);
}

.logo-small:hover {
    transform: scale(1.1);
}

.footer-links {
    flex: 1;
    min-width: 180px;
}

.footer-links h4,
.footer-contact h4 {
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
}

.footer-links h4::after,
.footer-contact h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-full);
}

.footer-links ul li {
    margin-bottom: 0.75rem;
}

.footer-links ul li a {
    color: rgba(255, 255, 255, 0.8);
    transition: all var(--transition-fast);
    display: inline-block;
    padding: 0.25rem 0;
}

.footer-links ul li a:hover {
    color: white;
    transform: translateX(5px);
}

.footer-contact {
    flex: 1;
    min-width: 250px;
}

.footer-contact p {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    transition: color var(--transition-fast);
}

.footer-contact p:hover {
    color: white;
}

.footer-contact i {
    margin-right: 1rem;
    width: 20px;
    color: var(--primary-light);
}

.footer-bottom {
    text-align: center;
    padding-top: 3rem;
    margin-top: 3rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(15, 23, 42, 0.75);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity var(--transition);
}

.modal.show {
    opacity: 1;
}

.modal-content {
    background-color: var(--bg-color);
    margin: 5% auto;
    padding: 2.5rem;
    border-radius: var(--radius-md);
    max-width: 800px;
    position: relative;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    transform: translateY(-20px);
    opacity: 0;
    transition: all var(--transition);
}

/* 产品详情模态框特殊样式 */
#product-modal .modal-content {
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal.show .modal-content {
    transform: translateY(0);
    opacity: 1;
}

.close {
    position: absolute;
    top: 1.25rem;
    right: 1.25rem;
    font-size: 1.5rem;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-full);
    background-color: var(--bg-dark);
    color: var(--text-color);
    transition: all var(--transition-fast);
}

.close:hover {
    background-color: var(--danger-color);
    color: white;
    transform: rotate(90deg);
}

/* Cart Modal Specific Styles */
#cart-modal .modal-content {
    max-width: 600px;
}

#cart-modal h2 {
    color: var(--secondary-color);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: var(--radius);
    overflow: hidden;
    margin-right: 1rem;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-details {
    flex: 1;
}

.cart-item-details h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 0.25rem;
}

.cart-item-details p {
    color: var(--text-light);
    font-size: 0.9rem;
}

.cart-item-total {
    color: var(--accent-color) !important;
    font-weight: 600;
    font-size: 1rem !important;
}

.cart-item-actions {
    display: flex;
    align-items: center;
    margin-left: 1rem;
}

.cart-item-actions button {
    width: 30px;
    height: 30px;
    border-radius: var(--radius-full);
    border: none;
    background-color: var(--bg-dark);
    color: var(--text-color);
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.cart-item-actions button:hover {
    background-color: var(--primary-color);
    color: white;
}

.cart-item-actions .cart-item-remove:hover {
    background-color: var(--danger-color);
}

.cart-item-quantity {
    margin: 0 0.5rem;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.cart-total {
    display: flex;
    justify-content: flex-end;
    padding: 1.5rem 0;
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--secondary-color);
}

.empty-cart {
    text-align: center;
    padding: 2rem 0;
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Product Modal Specific Styles */
.product-modal-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 1000px;
    margin: 0 auto;
}

.product-modal-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.product-detail-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

/* 注册气泡提示样式 */
.registration-bubble {
    position: relative;
    margin-bottom: 1rem;
}

.bubble-content {
    display: inline-block;
    background-color: #ff6b6b;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    box-shadow: 0 4px 10px rgba(255, 107, 107, 0.3);
    animation: bubble-pulse 2s infinite;
    position: relative;
}

.bubble-content i {
    margin-right: 0.5rem;
}

.bubble-arrow {
    position: absolute;
    bottom: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #ff6b6b;
}

@keyframes bubble-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 顶部标签页样式 */
.product-detail-tabs-top {
    margin-top: 1rem;
}

.tab-header-top {
    display: flex;
    background-color: #f0f0f0;
    border-radius: var(--radius);
    overflow: hidden;
}

.tab-item-top {
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    background-color: #f0f0f0;
    transition: all var(--transition-fast);
    text-align: center;
    flex: 1;
    border-right: 1px solid #ddd;
    font-weight: 600;
}

.tab-item-top:last-child {
    border-right: none;
}

.tab-item-top:hover {
    background-color: #e0e0e0;
}

.tab-item-top.active {
    background-color: var(--primary-color);
    color: white;
}

.tab-pane-top {
    display: none;
}

.tab-pane-top.active {
    display: flex;
}

.product-detail-main {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
}

.product-modal-image {
    flex: 1;
    min-width: 300px;
    max-width: 400px;
}

.product-modal-image img {
    width: 100%;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
}

.product-modal-info {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
}

.product-detail-basic-info {
    margin-bottom: 1.5rem;
}

.product-detail-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid var(--border-color);
}

.product-detail-table td {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    font-size: 0.95rem;
}

.table-label {
    background-color: var(--bg-light);
    font-weight: 600;
    color: var(--text-color);
    width: 20%;
}

.product-detail-actions {
    margin-top: auto;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.product-price-stock {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.product-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.product-category, .product-sku {
    display: inline-block;
    background-color: var(--bg-dark);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
}

.product-sku {
    background-color: var(--bg-light);
    color: var(--text-light);
    border: 1px solid var(--border-color);
}

.product-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
    display: flex;
    align-items: center;
}

.product-price::before {
    content: '¥';
    font-size: 1.25rem;
    margin-right: 0.25rem;
    font-weight: 400;
    color: var(--text-lighter);
}

.product-stock .status-badge {
    display: inline-block;
    padding: 0.35rem 0.75rem;
    border-radius: var(--radius);
    font-size: 0.875rem;
    font-weight: 600;
}

.status-in-stock {
    background-color: #e3f9e5;
    color: #0d6832;
}

.status-low-stock {
    background-color: #fff8e6;
    color: #b25e09;
}

.status-out-of-stock {
    background-color: #fee7e7;
    color: #b42318;
}

.add-to-cart-modal[disabled] {
    background-color: var(--text-lighter);
    cursor: not-allowed;
}

.add-to-cart-modal {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    background-color: var(--accent-color);
    border: none;
    border-radius: var(--radius);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.add-to-cart-modal:hover:not([disabled]) {
    background-color: var(--accent-hover);
}

/* 产品详情标签页 */
.product-detail-tabs {
    margin-top: 2rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    overflow: hidden;
}

.tab-header {
    display: flex;
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
}

.tab-item {
    padding: 1rem 1.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 3px solid transparent;
}

.tab-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.tab-item.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: white;
}

.tab-content {
    padding: 1.5rem;
    background-color: white;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

/* 移除所有标签页内容的标题样式，因为标签页本身已经有标题 */

.product-intro {
    padding: 1rem 0;
}

.intro-content {
    padding: 1rem 0;
}

.intro-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: var(--bg-light);
    border-radius: var(--radius);
    border-left: 4px solid var(--primary-color);
}

.intro-icon {
    color: var(--primary-color);
    font-weight: bold;
    margin-right: 0.75rem;
    flex-shrink: 0;
    font-size: 1.1rem;
}

.intro-item span:last-child {
    line-height: 1.6;
    color: var(--text-color);
}

.product-intro div {
    color: var(--text-color);
    line-height: 1.7;
}

.product-intro p {
    color: var(--text-color);
    line-height: 1.7;
    margin-bottom: 1rem;
}

/* 规格表格 */
.specs-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
}

.specs-table th,
.specs-table td {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    text-align: center;
}

.specs-table th {
    background-color: var(--bg-light);
    font-weight: 600;
}

.specs-table tr:nth-child(even) {
    background-color: var(--bg-light);
}

/* 推荐用途列表 */
.usage-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.usage-list li {
    position: relative;
    padding: 0.5rem 0 0.5rem 1.5rem;
    border-bottom: 1px dashed var(--border-color);
}

.usage-list li:last-child {
    border-bottom: none;
}

.usage-list li::before {
    content: '◆';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-size: 0.8rem;
}

/* 推荐用量样式 */
.dosage-content {
    padding: 1rem 0;
}

.dosage-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: var(--bg-light);
    border-radius: var(--radius);
    border-left: 4px solid var(--primary-color);
}

.dosage-icon {
    color: var(--primary-color);
    font-weight: bold;
    margin-right: 0.75rem;
    flex-shrink: 0;
    font-size: 1.1rem;
}

.dosage-item span:last-child {
    line-height: 1.6;
    color: var(--text-color);
}

.dosage-note {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--radius);
    color: #856404;
    font-weight: 500;
    text-align: center;
}

/* 价格说明和联系我们部分 */
.price-explanation,
.contact-info,
.price-explanation-section,
.contact-info-section {
    padding: 2rem;
    background-color: #f9f9f9;
    border-radius: var(--radius);
    width: 100%;
    margin-top: 2rem;
}

.price-explanation h3,
.contact-info h3,
.price-explanation-section h3,
.contact-info-section h3 {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.75rem;
}

.price-explanation p,
.contact-info p,
.price-explanation-section p,
.contact-info-section p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.contact-info a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-info a:hover {
    text-decoration: underline;
}

/* 产品说明书部分 */
.product-detail-document {
    margin-top: 2rem;
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
}

.product-detail-document h3 {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    text-align: center;
    font-weight: 600;
}

.add-to-cart-modal:hover {
    background-color: var(--accent-hover);
}

/* Notification */
.notification {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: var(--bg-color);
    color: var(--secondary-color);
    padding: 1rem 1.5rem;
    border-radius: var(--radius);
    box-shadow: var(--shadow-md);
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    max-width: 350px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Flying Cart Animation */
.cart-item-fly {
    position: fixed;
    z-index: 9999;
    background-color: var(--accent-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: 0 0 15px rgba(255, 71, 87, 0.7);
    pointer-events: none;
    opacity: 0;
    transition: transform 1s cubic-bezier(0.18, 0.89, 0.32, 1.28), opacity 1s;
    animation: spin 1s infinite linear, pulse-shadow 1s infinite alternate;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse-shadow {
    0% { box-shadow: 0 0 15px rgba(255, 71, 87, 0.7); }
    100% { box-shadow: 0 0 25px rgba(255, 71, 87, 0.9); }
}

/* 产品图片飞入效果 */
.product-image-fly {
    position: fixed;
    z-index: 9998;
    width: 70px;
    height: 70px;
    border-radius: 10px;
    object-fit: cover;
    box-shadow: var(--shadow-lg);
    pointer-events: none;
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    transform-origin: center;
    filter: drop-shadow(0 0 8px rgba(37, 99, 235, 0.5));
}

.notification-icon {
    font-size: 1.5rem;
}

.notification-message {
    flex: 1;
}

.notification.fade-out {
    animation: slideOut 0.5s ease-in forwards;
}

/* 不同类型的通知样式 */
.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-success .notification-icon {
    color: var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--danger-color);
}

.notification-error .notification-icon {
    color: var(--danger-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-warning .notification-icon {
    color: var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--primary-color);
}

.notification-info .notification-icon {
    color: var(--primary-color);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(50px);
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero h2 {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 1.75rem;
    }
}

@media (max-width: 1024px) {
    .container {
        padding: 0 1.5rem;
    }

    .product-container {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }

    .category-container {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .top-bar-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;
    }

    .company-info {
        text-align: center;
    }

    .hotline {
        justify-content: center;
    }

    .header-container {
        padding: 0 1rem;
    }

    .nav-container {
        position: fixed;
        top: 0;
        right: -100%;
        width: 80%;
        height: 100vh;
        background-color: var(--primary-color);
        box-shadow: var(--shadow-lg);
        transition: right var(--transition);
        z-index: 1000;
        flex-direction: column;
        align-items: flex-start;
        padding: 5rem 2rem 2rem;
    }

    .nav-container.active {
        right: 0;
    }

    nav ul {
        flex-direction: column;
        width: 100%;
        margin-right: 0;
        margin-bottom: 2rem;
    }

    nav ul li {
        margin: 0.75rem 0;
        width: 100%;
    }

    nav ul li a {
        display: block;
        padding: 0.75rem 0;
        font-size: 1.1rem;
    }

    .mobile-nav-toggle {
        display: block;
        z-index: 1001;
    }

    .hero {
        padding: 6rem 1rem;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .categories,
    .products,
    .about,
    .contact {
        padding: 4rem 1rem;
    }

    /* Product layout for mobile */
    .product-layout {
        flex-direction: column;
    }

    .category-sidebar {
        width: 100%;
        margin-bottom: 2rem;
    }

    /* Mobile category navigation */
    .category-sidebar {
        position: relative;
    }

    .category-nav {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        padding: 0.5rem;
        background-color: var(--bg-color);
    }

    .category-link {
        padding: 0.5rem 0.75rem;
    }

    /* Mobile filter sections */
    .filter-section {
        margin-bottom: 1.5rem;
    }

    .tag-cloud {
        justify-content: flex-start;
    }

    .modal-content {
        margin: 10% auto;
        width: 90%;
        padding: 1.5rem;
    }

    /* 产品详情模态框移动端样式 */
    .product-modal-content {
        flex-direction: column;
        padding: 0.5rem;
    }

    .product-detail-title {
        font-size: 1.5rem;
    }

    .bubble-content {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .product-detail-main {
        flex-direction: column;
        gap: 1rem;
    }

    .product-modal-image,
    .product-modal-info {
        width: 100%;
        max-width: 100%;
    }

    .product-detail-table td {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    /* 顶部标签页移动端样式 */
    .tab-header-top {
        flex-wrap: wrap;
    }

    .tab-item-top {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        flex-grow: 1;
        text-align: center;
    }

    .price-explanation,
    .contact-info {
        padding: 1rem;
    }

    .product-detail-document h3 {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .tab-header {
        flex-wrap: wrap;
    }

    .tab-item {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        flex-grow: 1;
        text-align: center;
    }

    .tab-content {
        padding: 1rem;
    }

    .specs-table th,
    .specs-table td {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    .about-content,
    .contact-container {
        gap: 2rem;
    }

    .product-container {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    /* Mobile product header */
    .product-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .product-header h3 {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .product-container {
        grid-template-columns: 1fr;
    }

    .category-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .product-filter-container {
        flex-direction: column;
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.25rem;
    }

    .logo {
        height: 30px;
    }

    .hero h2 {
        font-size: 1.75rem;
    }

    .hero p {
        font-size: 0.95rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .product-card {
        min-width: 100%;
    }

    .product-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .cart-item {
        flex-wrap: wrap;
    }

    .cart-item-image {
        margin-bottom: 0.5rem;
    }

    .cart-item-actions {
        width: 100%;
        justify-content: flex-end;
        margin-top: 0.5rem;
        margin-left: 0;
    }

    .footer-content {
        flex-direction: column;
        gap: 2rem;
    }

    .footer-logo,
    .footer-links,
    .footer-contact {
        min-width: 100%;
    }

    /* Improved mobile experience */
    .product-meta {
        flex-direction: column;
        align-items: flex-start;
    }

    .product-rating {
        margin-left: 0;
        margin-top: 0.5rem;
    }

    .product-pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .notification {
        max-width: 90%;
        left: 5%;
        right: 5%;
    }
}

/* 无数据提示样式 */
.no-data {
    padding: 2rem;
    text-align: center;
    color: var(--text-light);
    font-style: italic;
    background-color: var(--bg-light);
    border-radius: var(--radius);
    border: 1px dashed var(--border-color);
}

/* 错误信息样式 */
.error-message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;
    max-width: 90%;
    min-width: 300px;
    animation: slideDown 0.3s ease-out;
}

.error-message.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.error-message.warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.error-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-radius: var(--radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.error-icon {
    margin-right: 10px;
    font-size: 1.2rem;
}

.error-text {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.4;
}

.error-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    margin-left: 10px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.error-close:hover {
    opacity: 1;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 微信支付模态框样式 */
.wechat-pay-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.wechat-pay-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.wechat-pay-content .close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
    transition: color 0.2s;
}

.wechat-pay-content .close:hover {
    color: #333;
}

.wechat-pay-content h2 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.order-info {
    background-color: var(--bg-light);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    text-align: left;
}

.order-info p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.payment-instruction {
    font-size: 1rem;
    color: var(--text-color);
    margin-bottom: 1rem;
    font-weight: 500;
}

.wechat-pay-qrcode {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.payment-qrcode-img {
    max-width: 200px;
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.payment-notice {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.notice-text {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    color: #856404;
    text-align: left;
}

.payment-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn-payment-confirm,
.btn-payment-cancel {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.btn-payment-confirm {
    background-color: #07c160;
    color: white;
}

.btn-payment-confirm:hover {
    background-color: #06ad56;
    transform: translateY(-1px);
}

.btn-payment-cancel {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.btn-payment-cancel:hover {
    background-color: #e9ecef;
    transform: translateY(-1px);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .wechat-pay-content {
        padding: 1.5rem;
        margin: 1rem;
    }

    .payment-qrcode-img {
        max-width: 180px;
    }

    .payment-actions {
        flex-direction: column;
    }

    .btn-payment-confirm,
    .btn-payment-cancel {
        width: 100%;
    }
}

/* 订单状态样式 */
.status-payment-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}
