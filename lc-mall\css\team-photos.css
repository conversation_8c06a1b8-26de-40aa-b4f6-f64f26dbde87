/* 团队成员照片样式 */

/* 团队成员照片容器 */
.team-member .member-photo {
    position: relative;
    height: 300px;
    overflow: hidden;
    border-radius: 10px 10px 0 0;
}

/* 团队成员照片 */
.team-member .member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

/* 照片悬停效果 */
.team-member:hover .member-photo img {
    transform: scale(1.05);
}

/* 照片覆盖层 */
.member-photo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.5));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.team-member:hover .member-photo::before {
    opacity: 1;
}

/* 职位图标 */
.team-member::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    z-index: 2;
    opacity: 0.9;
    transition: transform 0.3s ease;
}

.team-member.ceo::after {
    content: '\f521'; /* FontAwesome 用户领带图标 */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    background-color: #e74c3c;
}

.team-member.tech::after {
    content: '\f0c3'; /* FontAwesome 烧杯图标 */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    background-color: #3498db;
}

.team-member.sales::after {
    content: '\f201'; /* FontAwesome 图表图标 */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    background-color: #2ecc71;
}

/* 默认背景渐变 */
.team-member.ceo .member-photo {
    background: linear-gradient(135deg, #ff9966, #ff5e62);
}

.team-member.tech .member-photo {
    background: linear-gradient(135deg, #56ccf2, #2f80ed);
}

.team-member.sales .member-photo {
    background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
}

/* 团队成员照片占位符样式 */
.team-member .member-photo.placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 20px;
    text-align: center;
}

.team-member .member-photo.placeholder i {
    font-size: 60px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
}

.team-member .member-photo.placeholder h3 {
    font-size: 24px;
    color: white;
    margin: 0 0 10px;
}

.team-member .member-photo.placeholder p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .team-member .member-photo {
        height: 250px;
    }
}
