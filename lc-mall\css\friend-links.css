/* 友情链接样式 - 侧边栏版本 */
.sidebar-section {
    margin-top: 30px;
}

.sidebar-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.friend-links-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.friend-links-nav li {
    margin-bottom: 10px;
}

.friend-links-nav a {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

/* 官网链接特殊样式 */
.friend-links-nav li:first-child a {
    background-color: #e6f0ff;
    color: #2563eb;
    font-weight: 600;
    border-left: 3px solid #2563eb;
}

.friend-links-nav a:hover {
    background-color: #2563eb;
    color: white;
    transform: translateX(5px);
}

/* 官网链接悬停样式 */
.friend-links-nav li:first-child a:hover {
    background-color: #2563eb;
    color: white;
    border-left-color: #1d4ed8;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .sidebar-section {
        margin-top: 20px;
    }

    .friend-links-nav a {
        padding: 8px 12px;
        font-size: 13px;
    }
}
