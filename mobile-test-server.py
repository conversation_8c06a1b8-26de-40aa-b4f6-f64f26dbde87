"""
移动端优化测试服务器
使用Python的http.server模块创建一个简单的HTTP服务器
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from urllib.parse import urlparse, parse_qs

# 配置服务器
PORT = 8000
DIRECTORY = "lc-mall"  # 网站根目录

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        # 设置网站根目录
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def log_message(self, format, *args):
        """自定义日志输出格式"""
        sys.stdout.write(f"[{self.log_date_time_string()}] {self.address_string()} - {format % args}\n")
        sys.stdout.flush()
    
    def do_GET(self):
        """处理GET请求"""
        # 解析URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        
        # 处理根路径请求
        if path == "/":
            self.path = "/index.html"
        
        # 处理移动端测试页面请求
        if path == "/mobile-test":
            self.path = "/mobile-test.html"
        
        # 调用父类方法处理请求
        return super().do_GET()

def run_server():
    """运行HTTP服务器"""
    handler = CustomHTTPRequestHandler
    
    # 创建服务器
    with socketserver.TCPServer(("", PORT), handler) as httpd:
        print(f"服务器启动在 http://localhost:{PORT}")
        print(f"移动端测试页面: http://localhost:{PORT}/mobile-test")
        print("按Ctrl+C停止服务器")
        
        # 自动打开浏览器
        webbrowser.open(f"http://localhost:{PORT}/mobile-test")
        
        # 启动服务器
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n服务器已停止")

if __name__ == "__main__":
    # 检查目录是否存在
    if not os.path.isdir(DIRECTORY):
        print(f"错误: 目录 '{DIRECTORY}' 不存在")
        sys.exit(1)
    
    # 运行服务器
    run_server()
