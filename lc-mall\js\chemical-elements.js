// 化学元素动画脚本
document.addEventListener('DOMContentLoaded', function() {
    // 化学元素和化学公式列表
    const elements = [
        'H', 'C', 'N', 'O', 'Na', 'Mg', 'Al', 'Si', 'P', 'S', 'Cl', 'K', 'Ca',
        'Fe', 'Cu', 'Zn', 'Br', 'I', 'Li', 'B', 'F', 'Ne', 'Ar'
    ];

    // 化学方程式列表 (参考维基学院的化学反应方程式列表)
    const formulas = [
        // 燃烧反应
        'CH₄ + 2O₂ → CO₂ + 2H₂O',
        'C₂H₅OH + 3O₂ → 2CO₂ + 3H₂O',
        '2C₈H₁₈ + 25O₂ → 16CO₂ + 18H₂O',

        // 酸碱反应
        'HCl + NaOH → NaCl + H₂O',
        'H₂SO₄ + 2NaOH → Na₂SO₄ + 2H₂O',
        'H₃PO₄ + 3KOH → K₃PO₄ + 3H₂O',

        // 置换反应
        'Zn + 2HCl → ZnCl₂ + H₂',
        'Fe + CuSO₄ → FeSO₄ + Cu',
        '2Na + 2H₂O → 2NaOH + H₂',

        // 分解反应
        '2H₂O → 2H₂ + O₂',
        'CaCO₃ → CaO + CO₂',
        '2KClO₃ → 2KCl + 3O₂',

        // 聚氨酯相关反应 (与公司业务相关)
        'R-NCO + R-OH → R-NH-COO-R',
        'R-NCO + H₂O → R-NH₂ + CO₂',
        'MDI + HO-R-OH → -[MDI-O-R-O]ₙ-',
        'TDI + HO-R-OH → -[TDI-O-R-O]ₙ-'
    ];

    // 获取头部动画容器
    const headerSection = document.querySelector('.element-balls-animation');

    if (!headerSection) return;

    // 创建化学元素容器
    const chemicalContainer = document.createElement('div');
    chemicalContainer.className = 'chemical-elements-container';
    headerSection.appendChild(chemicalContainer);

    // 用于跟踪已占用的位置
    const occupiedPositions = [];

    // 检查位置是否与已有元素重叠
    function isOverlapping(x, y, size, minDistance = 50) { // 减小默认最小距离
        for (const pos of occupiedPositions) {
            const dx = Math.abs(x - pos.x);
            const dy = Math.abs(y - pos.y);
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance < minDistance) {
                return true; // 重叠
            }
        }
        return false; // 不重叠
    }

    // 生成随机位置，确保不重叠
    function getRandomPosition(containerWidth, containerHeight, elementSize) {
        let x, y;
        let attempts = 0;
        const maxAttempts = 50; // 最大尝试次数

        do {
            x = Math.floor(Math.random() * (containerWidth - elementSize));
            y = Math.floor(Math.random() * (containerHeight - elementSize));
            attempts++;

            // 如果尝试次数过多，减小最小距离要求
            if (attempts > maxAttempts / 2) {
                if (!isOverlapping(x, y, elementSize, 35)) break;
            }

            // 如果尝试次数实在太多，接受较小的距离
            if (attempts > maxAttempts) {
                if (!isOverlapping(x, y, elementSize, 15)) break;
            }
        } while (isOverlapping(x, y, elementSize) && attempts < maxAttempts + 10);

        // 记录这个位置
        occupiedPositions.push({ x, y });

        return { x, y };
    }

    // 生成随机大小类别
    function getRandomSize() {
        const sizes = ['small', '', 'large'];
        const randomIndex = Math.floor(Math.random() * sizes.length);
        return sizes[randomIndex];
    }

    // 生成随机动画延迟
    function getRandomDelay() {
        return Math.random() * 10; // 0-10秒的随机延迟
    }

    // 生成随机动画持续时间
    function getRandomDuration() {
        return 15 + Math.random() * 20; // 15-35秒的随机持续时间
    }

    // 创建化学元素和公式
    function createChemicalElement() {
        const containerWidth = headerSection.offsetWidth;
        const containerHeight = headerSection.offsetHeight || 300; // 确保高度至少为300px

        // 限制每次显示8个公式
        const elementCount = 8;

        // 清空现有元素
        chemicalContainer.innerHTML = '';

        // 清空已占用位置数组
        occupiedPositions.length = 0;

        // 随机选择8个不同的公式索引
        const selectedFormulaIndices = [];
        while (selectedFormulaIndices.length < elementCount) {
            const randomIndex = Math.floor(Math.random() * formulas.length);
            if (!selectedFormulaIndices.includes(randomIndex)) {
                selectedFormulaIndices.push(randomIndex);
            }
        }

        // 创建元素
        for (let i = 0; i < elementCount; i++) {
            // 使用预先选择的公式索引
            const formulaIndex = selectedFormulaIndices[i];
            const selectedFormula = formulas[formulaIndex];

            // 创建公式DOM
            const formulaDiv = document.createElement('div');
            formulaDiv.className = `chemical-element formula ${getRandomSize()}`;
            formulaDiv.textContent = selectedFormula;

            // 设置随机位置，确保不超出屏幕
            const elementWidth = 250; // 为长公式预留足够宽度
            const elementHeight = 40; // 公式高度

            // 确保公式完全在可视区域内
            const position = getRandomPosition(containerWidth - elementWidth, containerHeight - elementHeight, elementHeight);

            // 设置位置，添加内边距确保不会太靠近边缘
            const padding = 20;
            formulaDiv.style.left = `${position.x + padding}px`;
            formulaDiv.style.top = `${position.y + padding}px`;

            // 设置随机动画延迟和持续时间
            formulaDiv.style.animationDelay = `${getRandomDelay()}s`;
            formulaDiv.style.animationDuration = `${getRandomDuration()}s`;

            // 为公式使用不同的动画
            const animations = ['float', 'float2', 'float3'];
            const randomAnimation = animations[Math.floor(Math.random() * animations.length)];
            formulaDiv.style.animationName = randomAnimation;

            // 添加到容器
            chemicalContainer.appendChild(formulaDiv);
        }
    }

    // 初始化
    createChemicalElement();

    // 窗口大小改变时重新创建元素
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // 清空容器
            chemicalContainer.innerHTML = '';
            // 重新创建元素
            createChemicalElement();
        }, 300);
    });
});
