/**
 * mobile-optimizations.js
 * 专门针对移动设备的优化脚本，特别是iPhone设备
 * 独立于主要的mobile.js文件，专注于解决移动设备上的特定问题
 */

console.log('Mobile optimizations script loaded');

// 设备检测
const deviceInfo = {
    isMobile: /iPhone|iPad|iPod|Android/i.test(navigator.userAgent),
    isIOS: /iPhone|iPad|iPod/i.test(navigator.userAgent),
    isIPhone: /iPhone/i.test(navigator.userAgent),
    isAndroid: /Android/i.test(navigator.userAgent),
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight,
    pixelRatio: window.devicePixelRatio || 1
};

// 只在移动设备上执行
if (deviceInfo.isMobile) {
    console.log('检测到移动设备，启用专门的移动设备优化');
    console.log('设备信息:', deviceInfo);

    // 在DOM加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化移动设备优化
        initMobileOptimizations();
    });
}

/**
 * 初始化移动设备优化
 */
function initMobileOptimizations() {
    // 应用设备特定优化
    applyDeviceSpecificOptimizations();

    // 优化产品图片加载
    optimizeProductImages();

    // 优化产品卡片布局
    optimizeProductCardLayout();

    // 添加触摸友好的交互
    enhanceTouchInteractions();

    // 优化性能
    optimizePerformance();

    // 优化化学公式显示
    optimizeChemicalFormulas();

    // 记录优化已应用
    console.log('移动设备优化已应用');
}

/**
 * 应用设备特定优化
 */
function applyDeviceSpecificOptimizations() {
    // 添加设备特定的类到body元素
    const body = document.body;

    if (deviceInfo.isIOS) {
        body.classList.add('ios-device');
    }

    if (deviceInfo.isIPhone) {
        body.classList.add('iphone-device');

        // iPhone特定优化
        applyIPhoneOptimizations();
    }

    if (deviceInfo.isAndroid) {
        body.classList.add('android-device');
    }

    // 根据屏幕尺寸添加类
    if (deviceInfo.screenWidth <= 375) {
        body.classList.add('small-mobile');
    } else if (deviceInfo.screenWidth <= 414) {
        body.classList.add('medium-mobile');
    } else {
        body.classList.add('large-mobile');
    }

    console.log('已应用设备特定优化');
}

/**
 * 应用iPhone特定优化
 */
function applyIPhoneOptimizations() {
    console.log('应用iPhone特定优化');

    // 修复iPhone上的Safari浏览器高度问题
    fixIOSViewportHeight();

    // 修复iPhone上的产品卡片显示问题
    fixIPhoneProductCards();

    // 优化iPhone上的触摸响应
    enhanceIOSTouchResponse();
}

/**
 * 修复iOS设备上的视口高度问题
 */
function fixIOSViewportHeight() {
    // 在iOS上，100vh可能包含地址栏，导致内容被截断
    // 使用JavaScript计算实际可见高度
    function setViewportHeight() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    // 初始设置
    setViewportHeight();

    // 在窗口大小改变时重新计算
    window.addEventListener('resize', setViewportHeight);

    // 在滚动时重新计算（处理iOS上的地址栏隐藏/显示）
    window.addEventListener('scroll', setViewportHeight);
}

/**
 * 修复iPhone上的产品卡片显示问题
 */
function fixIPhoneProductCards() {
    // 获取产品列表容器
    const productList = document.getElementById('product-list');
    if (!productList) return;

    // 添加iPhone特定的类
    productList.classList.add('iphone-product-list');

    // 监听产品卡片创建
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1 && node.classList.contains('product-card')) {
                        // 应用iPhone特定的产品卡片优化
                        optimizeIPhoneProductCard(node);
                    }
                });
            }
        });
    });

    // 开始观察
    observer.observe(productList, { childList: true });

    // 处理已有的产品卡片
    const existingCards = productList.querySelectorAll('.product-card');
    existingCards.forEach(card => {
        optimizeIPhoneProductCard(card);
    });
}

/**
 * 优化iPhone上的单个产品卡片
 * @param {HTMLElement} card - 产品卡片元素
 */
function optimizeIPhoneProductCard(card) {
    // 添加iPhone特定的类
    card.classList.add('iphone-product-card');

    // 确保图片正确加载和显示
    const img = card.querySelector('img');
    if (img) {
        // 添加图片加载错误处理
        img.onerror = function() {
            console.log('产品图片加载失败，使用占位图');
            this.src = '/lc-mall/images/product-placeholder.jpg';
        };

        // 确保图片完全加载
        img.loading = 'eager'; // 立即加载图片

        // 添加图片加载完成事件
        img.onload = function() {
            // 图片加载完成后添加类
            card.classList.add('image-loaded');
        };

        // 如果图片已经加载完成
        if (img.complete) {
            card.classList.add('image-loaded');
        }
    }

    // 优化产品信息布局
    const productInfo = card.querySelector('.product-info');
    if (productInfo) {
        // 确保产品信息区域有足够的高度
        productInfo.style.minHeight = '120px';
    }

    // 优化价格和购物车按钮行
    const priceCartRow = card.querySelector('.price-cart-row');
    if (priceCartRow) {
        // 确保价格和按钮在同一行且对齐
        priceCartRow.style.display = 'flex';
        priceCartRow.style.justifyContent = 'space-between';
        priceCartRow.style.alignItems = 'center';
    }
}

/**
 * 优化iOS设备上的触摸响应
 */
function enhanceIOSTouchResponse() {
    // 添加触摸反馈样式
    const style = document.createElement('style');
    style.textContent = `
        /* iOS触摸反馈 */
        .ios-device .product-card:active,
        .ios-device .btn:active,
        .ios-device .category-link:active,
        .ios-device .floating-cart:active {
            transform: scale(0.97);
            transition: transform 0.1s ease;
        }

        /* 确保iOS上的滚动平滑 */
        .ios-device .category-nav,
        .ios-device .product-container {
            -webkit-overflow-scrolling: touch;
        }

        /* 修复iOS上的固定元素问题 */
        .ios-device .floating-cart,
        .ios-device .back-to-top {
            transform: translateZ(0);
        }
    `;
    document.head.appendChild(style);
}

/**
 * 优化产品图片加载
 */
function optimizeProductImages() {
    // 获取所有产品图片
    const productImages = document.querySelectorAll('.product-card-header img');

    // 如果没有产品图片，直接返回
    if (!productImages.length) return;

    // 遍历所有产品图片
    productImages.forEach(img => {
        // 设置加载优先级
        img.loading = 'eager'; // 立即加载

        // 添加错误处理
        img.onerror = function() {
            this.src = '/lc-mall/images/product-placeholder.jpg';
        };

        // 优化图片尺寸
        optimizeImageSize(img);
    });

    console.log(`已优化 ${productImages.length} 个产品图片`);
}

/**
 * 优化图片尺寸
 * @param {HTMLImageElement} img - 图片元素
 */
function optimizeImageSize(img) {
    // 获取原始图片路径
    const originalSrc = img.src;

    // 如果是相对路径，不处理
    if (!originalSrc || originalSrc.includes('placeholder') || originalSrc.startsWith('data:')) {
        return;
    }

    // 根据设备像素比选择合适的图片尺寸
    const pixelRatio = deviceInfo.pixelRatio;

    // 设置图片尺寸属性
    img.setAttribute('width', '100%');
    img.setAttribute('height', 'auto');

    // 添加图片加载完成事件
    img.onload = function() {
        // 图片加载完成后添加类
        const card = img.closest('.product-card');
        if (card) {
            card.classList.add('image-loaded');
        }
    };
}

/**
 * 优化产品卡片布局
 */
function optimizeProductCardLayout() {
    // 获取产品列表容器
    const productList = document.getElementById('product-list');
    if (!productList) return;

    // 根据屏幕宽度调整产品卡片布局
    const adjustLayout = () => {
        const screenWidth = window.innerWidth;

        // 移除之前的布局类
        productList.classList.remove('single-column', 'two-columns');

        // 根据屏幕宽度设置布局
        if (screenWidth <= 360) {
            // 小屏幕使用单列布局
            productList.classList.add('single-column');
        } else {
            // 其他屏幕使用双列布局
            productList.classList.add('two-columns');
        }
    };

    // 初始调整
    adjustLayout();

    // 在窗口大小改变时重新调整
    window.addEventListener('resize', adjustLayout);

    // 添加产品卡片布局样式
    const style = document.createElement('style');
    style.textContent = `
        /* 产品卡片布局样式 */
        #product-list.single-column {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        #product-list.two-columns {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        /* 确保产品卡片在iPhone上正确显示 */
        .iphone-product-card {
            height: auto !important;
            min-height: 250px;
            display: flex;
            flex-direction: column;
        }

        .iphone-product-card .product-card-header {
            height: 120px;
            overflow: hidden;
        }

        .iphone-product-card .product-card-header img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .iphone-product-card .product-info {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .iphone-product-card .price-cart-row {
            margin-top: auto;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 添加触摸友好的交互
 */
function enhanceTouchInteractions() {
    // 添加触摸反馈样式
    const style = document.createElement('style');
    style.textContent = `
        /* 触摸反馈样式 */
        .product-card:active,
        .btn:active,
        .category-link:active,
        .floating-cart:active {
            transform: scale(0.97);
            transition: transform 0.1s ease;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 优化移动设备性能
 */
function optimizePerformance() {
    // 延迟加载非关键资源
    deferNonCriticalResources();

    // 优化滚动性能
    optimizeScrollPerformance();
}

/**
 * 延迟加载非关键资源
 */
function deferNonCriticalResources() {
    // 延迟加载图片
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => {
        img.src = img.getAttribute('data-src');
    });
}

/**
 * 优化滚动性能
 */
function optimizeScrollPerformance() {
    // 使用passive事件监听器提高滚动性能
    document.addEventListener('touchstart', function() {}, { passive: true });
    document.addEventListener('touchmove', function() {}, { passive: true });
    document.addEventListener('wheel', function() {}, { passive: true });
}

/**
 * 优化移动端化学公式显示
 * 在移动设备上只显示3个化学公式
 */
function optimizeChemicalFormulas() {
    // 获取化学公式容器
    const formulasContainer = document.querySelector('.chemical-formulas');
    if (!formulasContainer) return;

    // 获取所有化学公式
    const formulas = formulasContainer.querySelectorAll('span');
    if (!formulas.length) return;

    console.log(`找到 ${formulas.length} 个化学公式，移动端将只显示3个`);

    // 选择要保留的公式（聚氨酯相关的优先）
    const polyurethaneFormulas = [];
    const otherFormulas = [];

    // 分类公式
    formulas.forEach(formula => {
        // 检查是否是聚氨酯相关公式（通过文本内容判断）
        const text = formula.textContent;
        if (text.includes('NCO') || text.includes('NH-COO') || text.includes('聚氨酯')) {
            polyurethaneFormulas.push(formula);
        } else {
            otherFormulas.push(formula);
        }
    });

    // 确定要保留的公式
    let formulasToKeep = [];

    // 优先保留聚氨酯相关公式
    formulasToKeep = formulasToKeep.concat(polyurethaneFormulas);

    // 如果聚氨酯公式不足3个，添加其他公式
    if (formulasToKeep.length < 3) {
        // 随机打乱其他公式
        for (let i = otherFormulas.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [otherFormulas[i], otherFormulas[j]] = [otherFormulas[j], otherFormulas[i]];
        }

        // 添加足够的其他公式，直到达到3个
        formulasToKeep = formulasToKeep.concat(otherFormulas.slice(0, 3 - formulasToKeep.length));
    }

    // 如果超过3个，只保留前3个
    if (formulasToKeep.length > 3) {
        formulasToKeep = formulasToKeep.slice(0, 3);
    }

    // 隐藏所有公式
    formulas.forEach(formula => {
        formula.style.display = 'none';
    });

    // 显示要保留的公式
    formulasToKeep.forEach((formula, index) => {
        formula.style.display = 'block';

        // 调整位置，使3个公式分布更均匀
        switch (index) {
            case 0:
                formula.style.top = '20%';
                formula.style.left = '10%';
                break;
            case 1:
                formula.style.top = '50%';
                formula.style.left = '50%';
                break;
            case 2:
                formula.style.top = '80%';
                formula.style.left = '80%';
                break;
        }

        // 增大字体大小，使公式在移动设备上更清晰
        formula.style.fontSize = '18px';
        formula.style.opacity = '0.4'; // 稍微增加不透明度
    });

    console.log(`移动端化学公式优化完成，显示 ${formulasToKeep.length} 个公式`);
}
