<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 龙驰新材料</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header class="admin-header">
        <h1><img src="../images/logo.png" alt="龙驰新材料"> 龙驰新材料管理后台</h1>
        <nav class="admin-nav">
            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> 控制面板</a>
            <a href="products.html"><i class="fas fa-box"></i> 产品管理</a>
            <a href="orders.html" class="active"><i class="fas fa-shopping-cart"></i> 订单管理</a>
            <a href="users.html"><i class="fas fa-users"></i> 用户管理</a>
            <a href="messages.html"><i class="fas fa-comments"></i> 留言管理</a>
            <a href="sms.html"><i class="fas fa-sms"></i> 短信记录</a>
            <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出</a>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-content">
            <div class="admin-card">
                <h2>订单管理</h2>

                <div class="product-actions">
                    <div class="product-search">
                        <input type="text" id="order-search" placeholder="搜索订单...">
                        <button id="search-btn"><i class="fas fa-search"></i></button>
                    </div>
                    <div class="order-filter">
                        <label for="status-filter">状态:</label>
                        <select id="status-filter">
                            <option value="all">全部</option>
                            <option value="pending">待处理</option>
                            <option value="payment_pending">等待支付确认</option>
                            <option value="processing">处理中</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>
                    <div class="admin-actions">
                        <button class="btn btn-secondary" onclick="testWeChatWorkNotify()">
                            <i class="fas fa-bell"></i> 测试通知
                        </button>
                        <button class="btn btn-primary" onclick="refreshOrders()">
                            <i class="fas fa-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="product-table" id="orders-table">
                        <thead>
                            <tr>
                                <th>订单编号</th>
                                <th>客户</th>
                                <th>联系方式</th>
                                <th>日期</th>
                                <th>金额 (¥)</th>
                                <th>支付方式</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="empty-table">暂无订单数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Details Modal -->
    <div id="order-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>订单详情</h2>

            <div class="order-details">
                <div class="order-info">
                    <div class="order-info-group">
                        <h3>订单信息</h3>
                        <p><strong>订单编号:</strong> <span id="order-id"></span></p>
                        <p><strong>下单日期:</strong> <span id="order-date"></span></p>
                        <p><strong>支付方式:</strong> <span id="order-payment-method"></span></p>
                        <p><strong>状态:</strong> <span id="order-status"></span></p>
                    </div>

                    <div class="order-info-group">
                        <h3>客户信息</h3>
                        <p><strong>姓名:</strong> <span id="customer-name"></span></p>
                        <p><strong>电话:</strong> <span id="customer-phone"></span></p>
                        <p><strong>备注:</strong> <span id="customer-notes"></span></p>
                    </div>
                </div>

                <div class="order-items">
                    <h3>订单商品</h3>
                    <table class="product-table" id="order-items-table">
                        <thead>
                            <tr>
                                <th>产品</th>
                                <th>单价 (¥)</th>
                                <th>数量</th>
                                <th>小计 (¥)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Order items will be loaded here -->
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-right"><strong>总计:</strong></td>
                                <td id="order-total"></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <div class="order-actions">
                    <select id="update-status">
                        <option value="">更新状态</option>
                        <option value="pending">待处理</option>
                        <option value="payment_pending">等待支付确认</option>
                        <option value="processing">处理中</option>
                        <option value="completed">已完成</option>
                        <option value="cancelled">已取消</option>
                    </select>
                    <button class="btn save-btn" id="update-status-btn">更新</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入API客户端 -->
    <script src="../js/api-client.js"></script>
    <!-- 引入企业微信通知服务 -->
    <script src="../js/wechat-work-notify.js"></script>
    <!-- 引入管理后台脚本 -->
    <script src="admin.js"></script>
    <script src="orders.js"></script>
</body>
</html>
