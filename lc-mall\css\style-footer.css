/* Footer Styles */
footer {
    background-color: var(--secondary-color);
    color: white;
    padding: 4rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
    max-width: 1280px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 1.5rem;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo-small {
    height: 40px;
    margin-bottom: 1rem;
}

.footer-logo h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.footer-logo p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.footer-links h4, .footer-contact h4 {
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.75rem;
    font-weight: 600;
}

.footer-links h4::after, .footer-contact h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: var(--radius-full);
}

.footer-links ul li {
    margin-bottom: 0.75rem;
}

.footer-links ul li a {
    color: rgba(255, 255, 255, 0.8);
    transition: color var(--transition-fast);
}

.footer-links ul li a:hover {
    color: var(--accent-color);
}

.footer-contact p {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
}

.footer-contact p i {
    margin-right: 0.75rem;
    color: var(--accent-color);
    font-size: 1.125rem;
}

.qrcode {
    margin-top: 1.5rem;
    text-align: center;
}

.qrcode img {
    width: 120px;
    height: 120px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
    max-width: 1280px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* Responsive Footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-logo {
        align-items: center;
    }
    
    .footer-links h4::after, .footer-contact h4::after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .footer-contact p {
        justify-content: center;
    }
    
    .qrcode {
        margin-left: auto;
        margin-right: auto;
    }
}
