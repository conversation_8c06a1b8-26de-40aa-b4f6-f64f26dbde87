/* 化学实验动画样式 */
.chemistry-animation {
    position: relative;
    width: 100%;
    height: 400px;
    background-color: #1a2a3a;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
}

.chemistry-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: 10;
    text-shadow: 0 0 10px rgba(0, 100, 255, 0.8);
    animation: glow 3s infinite alternate;
}

.chemistry-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 80%;
    position: relative;
    z-index: 10;
    color: rgba(255, 255, 255, 0.9);
}

.cta-button {
    background-color: #ff6b00;
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.1rem;
    border-radius: 30px;
    cursor: pointer;
    position: relative;
    z-index: 10;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.cta-button:hover {
    background-color: #ff8c00;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.cta-button i {
    margin-left: 10px;
}

/* 烧杯容器 */
.beaker-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
}

/* 烧杯 */
.beaker {
    position: absolute;
    width: 120px;
    height: 150px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px 5px 20px 20px;
    bottom: 50px;
    transform: translateX(-50%);
    overflow: hidden;
}

.beaker-left {
    left: 30%;
}

.beaker-right {
    left: 70%;
}

/* 烧杯液体 */
.liquid {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70%;
    transition: all 1s ease;
}

.liquid-blue {
    background-color: rgba(0, 150, 255, 0.6);
    animation: bubbles 2s infinite, colorChange 10s infinite alternate;
}

.liquid-green {
    background-color: rgba(0, 200, 100, 0.6);
    animation: bubbles 3s infinite, colorChange2 10s infinite alternate;
}

/* 分子和气泡 */
.molecule {
    position: absolute;
    border-radius: 50%;
    opacity: 0.7;
    animation-name: float;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
}

.beaker-left .molecule {
    background-color: rgba(0, 200, 255, 0.8);
}

.beaker-right .molecule {
    background-color: rgba(100, 255, 100, 0.8);
}

/* 分子1 */
.molecule-1 {
    width: 10px;
    height: 10px;
    left: 20%;
    bottom: 10%;
    animation-duration: 4s;
}

/* 分子2 */
.molecule-2 {
    width: 8px;
    height: 8px;
    left: 50%;
    bottom: 20%;
    animation-duration: 6s;
    animation-delay: 1s;
}

/* 分子3 */
.molecule-3 {
    width: 12px;
    height: 12px;
    left: 70%;
    bottom: 15%;
    animation-duration: 5s;
    animation-delay: 2s;
}

/* 烧杯上方的烟雾 */
.smoke {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 30px;
    display: flex;
    justify-content: center;
}

.smoke-particle {
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: smoke 4s infinite;
}

.smoke-particle:nth-child(1) {
    animation-delay: 0s;
    left: 10px;
}

.smoke-particle:nth-child(2) {
    animation-delay: 1s;
    left: 30px;
}

.smoke-particle:nth-child(3) {
    animation-delay: 2s;
    left: 50px;
}

/* 化学符号 */
.chemical-symbols {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
}

.symbol {
    position: absolute;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.7);
    animation: fadeInOut 8s infinite;
}

.symbol-h2o {
    top: 20%;
    left: 20%;
    font-size: 24px;
    animation-delay: 0s;
}

.symbol-co2 {
    top: 30%;
    right: 25%;
    font-size: 20px;
    animation-delay: 2s;
}

.symbol-nh3 {
    bottom: 40%;
    left: 30%;
    font-size: 22px;
    animation-delay: 4s;
}

.symbol-o2 {
    bottom: 30%;
    right: 20%;
    font-size: 26px;
    animation-delay: 6s;
}

/* 背景粒子 */
.particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: particle-float 20s infinite linear;
}

/* 创建多个粒子 */
.particle:nth-child(1) { left: 10%; top: 20%; animation-duration: 15s; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; top: 40%; animation-duration: 18s; animation-delay: 1s; }
.particle:nth-child(3) { left: 30%; top: 60%; animation-duration: 20s; animation-delay: 2s; }
.particle:nth-child(4) { left: 40%; top: 80%; animation-duration: 17s; animation-delay: 3s; }
.particle:nth-child(5) { left: 50%; top: 30%; animation-duration: 19s; animation-delay: 4s; }
.particle:nth-child(6) { left: 60%; top: 50%; animation-duration: 16s; animation-delay: 5s; }
.particle:nth-child(7) { left: 70%; top: 70%; animation-duration: 21s; animation-delay: 6s; }
.particle:nth-child(8) { left: 80%; top: 10%; animation-duration: 14s; animation-delay: 7s; }
.particle:nth-child(9) { left: 90%; top: 90%; animation-duration: 22s; animation-delay: 8s; }
.particle:nth-child(10) { left: 15%; top: 45%; animation-duration: 19s; animation-delay: 9s; }
.particle:nth-child(11) { left: 25%; top: 65%; animation-duration: 17s; animation-delay: 1s; }
.particle:nth-child(12) { left: 35%; top: 85%; animation-duration: 20s; animation-delay: 2s; }
.particle:nth-child(13) { left: 45%; top: 25%; animation-duration: 18s; animation-delay: 3s; }
.particle:nth-child(14) { left: 55%; top: 55%; animation-duration: 16s; animation-delay: 4s; }
.particle:nth-child(15) { left: 75%; top: 35%; animation-duration: 15s; animation-delay: 5s; }

/* 动画定义 */
@keyframes float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-40px);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes bubbles {
    0% {
        box-shadow: 0 0 0 rgba(255, 255, 255, 0.1) inset;
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.3) inset;
    }
    100% {
        box-shadow: 0 0 0 rgba(255, 255, 255, 0.1) inset;
    }
}

@keyframes colorChange {
    0% {
        background-color: rgba(0, 150, 255, 0.6);
    }
    50% {
        background-color: rgba(0, 100, 200, 0.6);
    }
    100% {
        background-color: rgba(50, 100, 255, 0.6);
    }
}

@keyframes colorChange2 {
    0% {
        background-color: rgba(0, 200, 100, 0.6);
    }
    50% {
        background-color: rgba(100, 200, 50, 0.6);
    }
    100% {
        background-color: rgba(50, 150, 100, 0.6);
    }
}

@keyframes smoke {
    0% {
        transform: translateY(0) scale(1);
        opacity: 0.3;
    }
    100% {
        transform: translateY(-50px) scale(2);
        opacity: 0;
    }
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    20% {
        opacity: 0.7;
        transform: scale(1);
    }
    80% {
        opacity: 0.7;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0.8);
    }
}

@keyframes particle-float {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(20px, 20px);
    }
    50% {
        transform: translate(0, 40px);
    }
    75% {
        transform: translate(-20px, 20px);
    }
    100% {
        transform: translate(0, 0);
    }
}

@keyframes glow {
    0% {
        text-shadow: 0 0 10px rgba(0, 100, 255, 0.8);
    }
    100% {
        text-shadow: 0 0 20px rgba(0, 150, 255, 1), 0 0 30px rgba(0, 200, 255, 0.8);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chemistry-title {
        font-size: 2rem;
    }

    .chemistry-subtitle {
        font-size: 1rem;
    }

    .beaker {
        width: 80px;
        height: 120px;
    }

    .beaker-left {
        left: 25%;
    }

    .beaker-right {
        left: 75%;
    }
}
