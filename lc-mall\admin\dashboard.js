// Dashboard functionality
document.addEventListener('DOMContentLoaded', async function() {
    // DOM Elements
    const productCount = document.getElementById('product-count');
    const orderCount = document.getElementById('order-count');
    const customerCount = document.getElementById('customer-count');
    const messageCount = document.getElementById('message-count');
    const smsCount = document.getElementById('sms-count');
    const recentOrdersTable = document.getElementById('recent-orders');
    const recentMessagesTable = document.getElementById('recent-messages');
    const recentSmsTable = document.getElementById('recent-sms');

    // 显示加载中提示
    if (recentOrdersTable) {
        const tbody = recentOrdersTable.querySelector('tbody');
        tbody.innerHTML = '<tr><td colspan="5" class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在加载数据...</td></tr>';
    }

    // 从API获取数据
    let products = [];
    let orders = [];
    let messages = [];
    let smsRecords = [];
    let users = [];

    try {
        // 并行获取所有数据
        const [productsData, ordersData, messagesData, smsData, usersData] = await Promise.all([
            ApiClient.getProducts(),
            ApiClient.getOrders(),
            ApiClient.getMessages(),
            ApiClient.getSmsRecords(),
            ApiClient.getUsers()
        ]);

        products = productsData;
        orders = ordersData;
        messages = messagesData;
        smsRecords = smsData;
        users = usersData;
    } catch (error) {
        console.error('获取数据失败:', error);
    }

    // Update stats
    if (productCount) {
        productCount.textContent = products.length;
    }

    if (orderCount) {
        orderCount.textContent = orders.length;
    }

    // 显示用户数量
    if (customerCount) {
        customerCount.textContent = users.length;
    }

    // Update message count
    if (messageCount) {
        messageCount.textContent = messages.length;
    }

    // Update SMS count
    if (smsCount) {
        smsCount.textContent = smsRecords.length;
    }

    // Display recent orders
    if (recentOrdersTable) {
        const tbody = recentOrdersTable.querySelector('tbody');
        tbody.innerHTML = '';

        if (orders.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="empty-table">暂无订单数据</td></tr>';
        } else {
            // Sort orders by date (newest first)
            const sortedOrders = [...orders].sort((a, b) => new Date(b.date) - new Date(a.date));

            // Display up to 5 most recent orders
            const recentOrders = sortedOrders.slice(0, 5);

            recentOrders.forEach(order => {
                const tr = document.createElement('tr');

                // Format date
                const orderDate = new Date(order.date);
                const formattedDate = `${orderDate.getFullYear()}-${(orderDate.getMonth() + 1).toString().padStart(2, '0')}-${orderDate.getDate().toString().padStart(2, '0')}`;

                // Format status
                let statusClass = '';
                let statusText = '';

                switch (order.status) {
                    case 'pending':
                        statusClass = 'status-pending';
                        statusText = '待处理';
                        break;
                    case 'processing':
                        statusClass = 'status-processing';
                        statusText = '处理中';
                        break;
                    case 'completed':
                        statusClass = 'status-completed';
                        statusText = '已完成';
                        break;
                    case 'cancelled':
                        statusClass = 'status-cancelled';
                        statusText = '已取消';
                        break;
                    default:
                        statusClass = 'status-pending';
                        statusText = '待处理';
                }

                tr.innerHTML = `
                    <td>${order.id}</td>
                    <td>${order.customer.name}</td>
                    <td>${formattedDate}</td>
                    <td>¥${order.total.toFixed(2)}</td>
                    <td><span class="status ${statusClass}">${statusText}</span></td>
                `;

                tbody.appendChild(tr);
            });
        }
    }

    // Display recent messages
    if (recentMessagesTable) {
        const tbody = recentMessagesTable.querySelector('tbody');
        tbody.innerHTML = '';

        if (messages.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="empty-table">暂无留言数据</td></tr>';
        } else {
            // Sort messages by date (newest first)
            const sortedMessages = [...messages].sort((a, b) => new Date(b.date) - new Date(a.date));

            // Display up to 5 most recent messages
            const recentMessages = sortedMessages.slice(0, 5);

            recentMessages.forEach(message => {
                const tr = document.createElement('tr');

                // Format date
                const messageDate = new Date(message.date);
                const formattedDate = `${messageDate.getFullYear()}-${(messageDate.getMonth() + 1).toString().padStart(2, '0')}-${messageDate.getDate().toString().padStart(2, '0')}`;

                // Format status
                let statusClass = '';
                let statusText = '';

                switch (message.status) {
                    case 'unread':
                        statusClass = 'status-pending';
                        statusText = '未读';
                        break;
                    case 'read':
                        statusClass = 'status-processing';
                        statusText = '已读';
                        break;
                    case 'replied':
                        statusClass = 'status-completed';
                        statusText = '已回复';
                        break;
                    default:
                        statusClass = 'status-pending';
                        statusText = '未读';
                }

                // Truncate message content if too long
                const truncatedMessage = message.message.length > 30 ? message.message.substring(0, 30) + '...' : message.message;

                tr.innerHTML = `
                    <td>${message.name}</td>
                    <td>${message.phone}</td>
                    <td>${truncatedMessage}</td>
                    <td>${formattedDate}</td>
                    <td><span class="status ${statusClass}">${statusText}</span></td>
                `;

                tbody.appendChild(tr);
            });
        }
    }

    // Display recent SMS
    if (recentSmsTable) {
        const tbody = recentSmsTable.querySelector('tbody');
        tbody.innerHTML = '';

        if (smsRecords.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="empty-table">暂无短信数据</td></tr>';
        } else {
            // Sort SMS by date (newest first)
            const sortedSms = [...smsRecords].sort((a, b) => new Date(b.sentTime) - new Date(a.sentTime));

            // Display up to 5 most recent SMS
            const recentSms = sortedSms.slice(0, 5);

            recentSms.forEach(sms => {
                const tr = document.createElement('tr');

                // Format date
                const smsDate = new Date(sms.sentTime);
                const formattedDate = `${smsDate.getFullYear()}-${(smsDate.getMonth() + 1).toString().padStart(2, '0')}-${smsDate.getDate().toString().padStart(2, '0')} ${smsDate.getHours().toString().padStart(2, '0')}:${smsDate.getMinutes().toString().padStart(2, '0')}`;

                // Format type
                const typeText = sms.type === 'order' ? '订单通知' : '留言通知';

                // Truncate content if too long
                const truncatedContent = sms.content.length > 30 ? sms.content.substring(0, 30) + '...' : sms.content;

                tr.innerHTML = `
                    <td>${typeText}</td>
                    <td>${sms.recipient}</td>
                    <td>${sms.title}</td>
                    <td>${truncatedContent}</td>
                    <td>${formattedDate}</td>
                `;

                tbody.appendChild(tr);
            });
        }
    }
});
