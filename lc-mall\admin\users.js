// Users management functionality
document.addEventListener('DOMContentLoaded', async function() {
    // DOM Elements
    const userTable = document.getElementById('user-table');
    const userModal = document.getElementById('user-modal');
    const closeBtn = document.getElementById('close-btn');
    const userSearch = document.getElementById('user-search');
    const searchBtn = document.getElementById('search-btn');
    const userTypeFilter = document.getElementById('user-type-filter');
    const modalClose = userModal.querySelector('.close');
    const exportBtn = document.getElementById('export-btn');

    // 统计元素
    const totalUsersEl = document.getElementById('total-users');
    const phoneUsersEl = document.getElementById('phone-users');
    const wechatUsersEl = document.getElementById('wechat-users');
    const qqUsersEl = document.getElementById('qq-users');

    // Variables
    let users = [];
    let filteredUsers = [];
    let currentFilter = 'all';

    // 显示加载中提示
    const tbody = userTable.querySelector('tbody');
    tbody.innerHTML = '<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在加载用户数据...</td></tr>';

    // 从API获取用户数据
    try {
        const apiUsers = await ApiClient.getUsers();
        console.log('Users loaded from API:', apiUsers);

        if (apiUsers && apiUsers.length > 0) {
            // 更新用户数据
            users = apiUsers;
            filteredUsers = [...users];

            // 显示用户
            displayUsers(filteredUsers);
            console.log('Users loaded from API');

            // 更新用户统计
            updateUserStats();

            // 显示成功提示
            showNotification('用户数据已加载', 'success');
        } else {
            // 没有用户数据
            tbody.innerHTML = '<tr><td colspan="9" class="text-center">没有用户数据</td></tr>';
        }
    } catch (error) {
        console.error('加载用户数据失败:', error);
        tbody.innerHTML = '<tr><td colspan="9" class="text-center text-danger">加载用户数据失败，请刷新页面重试</td></tr>';
        showNotification('加载用户数据失败，请刷新页面重试', 'error');
    }

    // Event Listeners
    closeBtn.addEventListener('click', closeUserModal);
    modalClose.addEventListener('click', closeUserModal);
    searchBtn.addEventListener('click', searchUsers);
    userSearch.addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            searchUsers();
        }
    });

    userTypeFilter.addEventListener('change', function() {
        currentFilter = this.value;
        filterUsers();
    });

    // 导出用户数据
    exportBtn.addEventListener('click', exportUserData);

    // 点击模态框背景时的处理
    userModal.addEventListener('click', function(e) {
        // 只有当点击的是模态框背景（不是内容区域）时才处理
        if (e.target === userModal) {
            closeUserModal();
        }
    });

    // Functions
    function displayUsers(usersToDisplay) {
        const tbody = userTable.querySelector('tbody');
        tbody.innerHTML = '';

        if (usersToDisplay.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="empty-table">暂无用户数据</td></tr>';
            return;
        }

        usersToDisplay.forEach(user => {
            const tr = document.createElement('tr');

            // 确定注册方式
            let registerType = '';
            let registerTypeIcon = '';

            if (user.social_platform === 'wechat') {
                registerType = '微信';
                registerTypeIcon = '<i class="fab fa-weixin text-success"></i>';
            } else if (user.social_platform === 'qq') {
                registerType = 'QQ';
                registerTypeIcon = '<i class="fab fa-qq text-primary"></i>';
            } else {
                registerType = '手机';
                registerTypeIcon = '<i class="fas fa-mobile-alt text-secondary"></i>';
            }

            // 格式化日期
            const createdDate = user.created_at ? new Date(user.created_at) : null;
            const formattedCreatedDate = createdDate ?
                `${createdDate.getFullYear()}-${(createdDate.getMonth() + 1).toString().padStart(2, '0')}-${createdDate.getDate().toString().padStart(2, '0')}` :
                '-';

            const lastLoginDate = user.last_login ? new Date(user.last_login) : null;
            const formattedLastLoginDate = lastLoginDate ?
                `${lastLoginDate.getFullYear()}-${(lastLoginDate.getMonth() + 1).toString().padStart(2, '0')}-${lastLoginDate.getDate().toString().padStart(2, '0')}` :
                '-';

            // 头像处理
            const avatarSrc = user.avatar || '../images/placeholder/product-placeholder.svg';

            tr.innerHTML = `
                <td>${user.id}</td>
                <td><img src="${avatarSrc}" alt="${user.name}" class="user-avatar-small"></td>
                <td>${user.name}</td>
                <td>${user.phone || '-'}</td>
                <td>${user.company || '-'}</td>
                <td>${formattedCreatedDate}</td>
                <td>${formattedLastLoginDate}</td>
                <td>${registerTypeIcon} ${registerType}</td>
                <td class="table-actions">
                    <button class="view-btn" data-id="${user.id}" title="查看详情"><i class="fas fa-eye"></i></button>
                </td>
            `;
            tbody.appendChild(tr);
        });

        // Add event listeners to view buttons
        document.querySelectorAll('.view-btn').forEach(button => {
            button.addEventListener('click', function() {
                const userId = parseInt(this.getAttribute('data-id'));
                openUserModal(userId);
            });
        });
    }

    function openUserModal(userId) {
        const user = users.find(u => u.id === userId);

        if (user) {
            // 填充用户详情
            document.getElementById('user-id').textContent = user.id;
            document.getElementById('user-name').textContent = user.name || '-';
            document.getElementById('user-phone').textContent = user.phone || '-';
            document.getElementById('user-company').textContent = user.company || '-';
            document.getElementById('user-position').textContent = user.position || '-';
            document.getElementById('user-wechat').textContent = user.wechat || '-';
            document.getElementById('user-address').textContent = user.address || '-';

            // 格式化日期
            const createdDate = user.created_at ? new Date(user.created_at) : null;
            document.getElementById('user-created-at').textContent = createdDate ?
                `${createdDate.getFullYear()}-${(createdDate.getMonth() + 1).toString().padStart(2, '0')}-${createdDate.getDate().toString().padStart(2, '0')} ${createdDate.getHours().toString().padStart(2, '0')}:${createdDate.getMinutes().toString().padStart(2, '0')}` :
                '-';

            const lastLoginDate = user.last_login ? new Date(user.last_login) : null;
            document.getElementById('user-last-login').textContent = lastLoginDate ?
                `${lastLoginDate.getFullYear()}-${(lastLoginDate.getMonth() + 1).toString().padStart(2, '0')}-${lastLoginDate.getDate().toString().padStart(2, '0')} ${lastLoginDate.getHours().toString().padStart(2, '0')}:${lastLoginDate.getMinutes().toString().padStart(2, '0')}` :
                '从未登录';

            // 注册方式
            let registerType = '';
            if (user.social_platform === 'wechat') {
                registerType = '微信登录';
            } else if (user.social_platform === 'qq') {
                registerType = 'QQ登录';
            } else {
                registerType = '手机注册';
            }
            document.getElementById('user-register-type').textContent = registerType;

            // 头像
            const avatarImg = document.getElementById('user-avatar');
            if (user.avatar) {
                avatarImg.src = user.avatar;
            } else {
                avatarImg.src = '../images/placeholder/product-placeholder.svg';
            }

            // 显示模态框
            userModal.style.display = 'block';
            setTimeout(() => {
                userModal.classList.add('show');
            }, 10);
        }
    }

    function closeUserModal() {
        userModal.classList.remove('show');
        setTimeout(() => {
            userModal.style.display = 'none';
        }, 300);
    }

    function searchUsers() {
        const searchTerm = userSearch.value.toLowerCase().trim();

        if (searchTerm === '') {
            filterUsers();
            return;
        }

        const searchResults = users.filter(user =>
            (user.name && user.name.toLowerCase().includes(searchTerm)) ||
            (user.phone && user.phone.toLowerCase().includes(searchTerm)) ||
            (user.company && user.company.toLowerCase().includes(searchTerm))
        );

        filteredUsers = searchResults;
        displayUsers(filteredUsers);
    }

    function filterUsers() {
        const searchTerm = userSearch.value.toLowerCase().trim();

        // 先按类型筛选
        let typeFiltered = [];
        if (currentFilter === 'all') {
            typeFiltered = [...users];
        } else if (currentFilter === 'wechat') {
            typeFiltered = users.filter(user => user.social_platform === 'wechat');
        } else if (currentFilter === 'qq') {
            typeFiltered = users.filter(user => user.social_platform === 'qq');
        } else if (currentFilter === 'phone') {
            typeFiltered = users.filter(user => !user.social_platform && user.phone);
        }

        // 再按搜索词筛选
        if (searchTerm) {
            filteredUsers = typeFiltered.filter(user =>
                (user.name && user.name.toLowerCase().includes(searchTerm)) ||
                (user.phone && user.phone.toLowerCase().includes(searchTerm)) ||
                (user.company && user.company.toLowerCase().includes(searchTerm))
            );
        } else {
            filteredUsers = typeFiltered;
        }

        displayUsers(filteredUsers);
    }

    // 更新用户统计
    function updateUserStats() {
        // 总用户数
        totalUsersEl.textContent = users.length;

        // 手机注册用户数
        const phoneUsers = users.filter(user => !user.social_platform && user.phone).length;
        phoneUsersEl.textContent = phoneUsers;

        // 微信用户数
        const wechatUsers = users.filter(user => user.social_platform === 'wechat').length;
        wechatUsersEl.textContent = wechatUsers;

        // QQ用户数
        const qqUsers = users.filter(user => user.social_platform === 'qq').length;
        qqUsersEl.textContent = qqUsers;
    }

    // 导出用户数据为CSV
    function exportUserData() {
        // 如果没有用户数据，显示提示
        if (users.length === 0) {
            showNotification('没有用户数据可导出', 'error');
            return;
        }

        // 创建CSV内容
        let csvContent = "数据类型,ID,姓名,手机号,公司,职位,微信,地址,注册时间,最后登录,注册方式\n";

        // 添加用户数据
        users.forEach(user => {
            // 确定注册方式
            let registerType = '';
            if (user.social_platform === 'wechat') {
                registerType = '微信登录';
            } else if (user.social_platform === 'qq') {
                registerType = 'QQ登录';
            } else {
                registerType = '手机注册';
            }

            // 格式化日期
            const createdDate = user.created_at ? new Date(user.created_at) : null;
            const formattedCreatedDate = createdDate ?
                `${createdDate.getFullYear()}-${(createdDate.getMonth() + 1).toString().padStart(2, '0')}-${createdDate.getDate().toString().padStart(2, '0')} ${createdDate.getHours().toString().padStart(2, '0')}:${createdDate.getMinutes().toString().padStart(2, '0')}` :
                '';

            const lastLoginDate = user.last_login ? new Date(user.last_login) : null;
            const formattedLastLoginDate = lastLoginDate ?
                `${lastLoginDate.getFullYear()}-${(lastLoginDate.getMonth() + 1).toString().padStart(2, '0')}-${lastLoginDate.getDate().toString().padStart(2, '0')} ${lastLoginDate.getHours().toString().padStart(2, '0')}:${lastLoginDate.getMinutes().toString().padStart(2, '0')}` :
                '';

            // 添加一行数据
            csvContent += `用户,${user.id},"${user.name || ''}","${user.phone || ''}","${user.company || ''}","${user.position || ''}","${user.wechat || ''}","${user.address || ''}","${formattedCreatedDate}","${formattedLastLoginDate}","${registerType}"\n`;
        });

        // 创建Blob对象
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        // 创建下载链接
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        // 设置下载属性
        link.setAttribute('href', url);
        link.setAttribute('download', `用户数据_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';

        // 添加到文档并触发点击
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // 显示成功提示
        showNotification('用户数据导出成功', 'success');
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `admin-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            </div>
            <div class="notification-message">${message}</div>
        `;

        document.body.appendChild(notification);

        // 显示通知
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // 3秒后隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
});
