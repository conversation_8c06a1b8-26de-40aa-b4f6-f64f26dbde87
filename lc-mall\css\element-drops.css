/* 元素掉落动画样式 */
.element-drops-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none; /* 确保不会干扰用户交互 */
    z-index: 1;
}

/* 元素球体样式 */
.element-ball {
    position: absolute;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    will-change: transform, opacity;
    transition: opacity 0.5s ease-out;
}

/* 元素球体消失动画 */
@keyframes disappear {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(0);
        opacity: 0;
    }
}

/* 加载指示器样式 */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(240, 248, 255, 0.8);
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(100, 180, 255, 0.2);
    z-index: 10;
    transition: opacity 0.5s ease-out;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(100, 180, 255, 0.3);
    border-top: 3px solid #4a90e2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

.loading-text {
    color: #4a90e2;
    font-size: 14px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 元素颜色 - 第1周期 */
.H {
    background: linear-gradient(135deg, #e6f7ff, #b3e0ff);
    border: 1px solid #80caff;
}

.He {
    background: linear-gradient(135deg, #ffe6e6, #ffb3b3);
    border: 1px solid #ff8080;
}

/* 元素颜色 - 第2周期 */
.Li {
    background: linear-gradient(135deg, #ffcccc, #ff9999);
    border: 1px solid #ff6666;
}

.Be {
    background: linear-gradient(135deg, #ffe6cc, #ffcc99);
    border: 1px solid #ffb366;
}

.B {
    background: linear-gradient(135deg, #e6ffcc, #ccff99);
    border: 1px solid #b3ff66;
}

.C {
    background: linear-gradient(135deg, #ccffcc, #99ff99);
    border: 1px solid #66ff66;
}

.N {
    background: linear-gradient(135deg, #ccffe6, #99ffcc);
    border: 1px solid #66ffb3;
}

.O {
    background: linear-gradient(135deg, #ccffff, #99ffff);
    border: 1px solid #66ffff;
}

.F {
    background: linear-gradient(135deg, #cce6ff, #99ccff);
    border: 1px solid #66b3ff;
}

.Ne {
    background: linear-gradient(135deg, #e6ccff, #cc99ff);
    border: 1px solid #b366ff;
}

/* 元素颜色 - 第3周期 */
.Na {
    background: linear-gradient(135deg, #ffcce6, #ff99cc);
    border: 1px solid #ff66b3;
}

.Mg {
    background: linear-gradient(135deg, #ffe6e6, #ffcccc);
    border: 1px solid #ffb3b3;
}

.Al {
    background: linear-gradient(135deg, #e6e6ff, #ccccff);
    border: 1px solid #b3b3ff;
}

.Si {
    background: linear-gradient(135deg, #e6ffe6, #ccffcc);
    border: 1px solid #b3ffb3;
}

.P {
    background: linear-gradient(135deg, #fff2cc, #ffe699);
    border: 1px solid #ffd966;
}

.S {
    background: linear-gradient(135deg, #ffeecc, #ffdd99);
    border: 1px solid #ffcc66;
}

.Cl {
    background: linear-gradient(135deg, #ccffee, #99ffdd);
    border: 1px solid #66ffcc;
}

.Ar {
    background: linear-gradient(135deg, #f2ccff, #e699ff);
    border: 1px solid #d966ff;
}

/* 元素颜色 - 第4周期 */
.K {
    background: linear-gradient(135deg, #ffccff, #ff99ff);
    border: 1px solid #ff66ff;
}

.Ca {
    background: linear-gradient(135deg, #e6ffff, #ccffff);
    border: 1px solid #b3ffff;
}

.Sc {
    background: linear-gradient(135deg, #ffe6cc, #ffcc99);
    border: 1px solid #ffb366;
}

.Ti {
    background: linear-gradient(135deg, #cce6ff, #99ccff);
    border: 1px solid #66b3ff;
}

.V {
    background: linear-gradient(135deg, #e6ccff, #cc99ff);
    border: 1px solid #b366ff;
}

.Cr {
    background: linear-gradient(135deg, #ffcce6, #ff99cc);
    border: 1px solid #ff66b3;
}

.Mn {
    background: linear-gradient(135deg, #ffe6cc, #ffcc99);
    border: 1px solid #ffb366;
}

.Fe {
    background: linear-gradient(135deg, #ffcccc, #ff9999);
    border: 1px solid #ff6666;
}

.Co {
    background: linear-gradient(135deg, #ccffcc, #99ff99);
    border: 1px solid #66ff66;
}

.Ni {
    background: linear-gradient(135deg, #ccffe6, #99ffcc);
    border: 1px solid #66ffb3;
}

.Cu {
    background: linear-gradient(135deg, #cce6ff, #99ccff);
    border: 1px solid #66b3ff;
}

.Zn {
    background: linear-gradient(135deg, #e6ccff, #cc99ff);
    border: 1px solid #b366ff;
}

.Ga {
    background: linear-gradient(135deg, #ffcce6, #ff99cc);
    border: 1px solid #ff66b3;
}

.Ge {
    background: linear-gradient(135deg, #e6ffe6, #ccffcc);
    border: 1px solid #b3ffb3;
}

.As {
    background: linear-gradient(135deg, #fff2cc, #ffe699);
    border: 1px solid #ffd966;
}

.Se {
    background: linear-gradient(135deg, #ffeecc, #ffdd99);
    border: 1px solid #ffcc66;
}

.Br {
    background: linear-gradient(135deg, #ccffee, #99ffdd);
    border: 1px solid #66ffcc;
}

.Kr {
    background: linear-gradient(135deg, #f2ccff, #e699ff);
    border: 1px solid #d966ff;
}
