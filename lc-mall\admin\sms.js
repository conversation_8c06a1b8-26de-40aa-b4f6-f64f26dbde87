// 短信记录管理功能
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const smsTable = document.getElementById('sms-table');
    const smsTableContainer = document.getElementById('sms-table-container'); // 新的表格容器ID
    const emptySms = document.getElementById('empty-sms');
    const smsSearch = document.getElementById('sms-search');
    const searchBtn = document.getElementById('search-btn');
    const typeFilter = document.getElementById('type-filter');
    const smsModal = document.getElementById('sms-modal');
    const smsDetail = document.getElementById('sms-detail');
    const deleteSmsBtn = document.getElementById('delete-sms-btn');

    // 当前选中的短信ID
    let currentSmsId = null;

    // 从localStorage获取短信数据
    let smsRecords = JSON.parse(localStorage.getItem('smsRecords')) || [];

    // 初始化
    init();

    // 初始化函数
    function init() {
        // 加载短信数据
        loadSmsRecords();

        // 设置事件监听器
        setupEventListeners();
    }

    // 加载短信数据
    function loadSmsRecords(filteredRecords = null) {
        // 使用过滤后的短信或所有短信
        const recordsToDisplay = filteredRecords || smsRecords;

        // 清空表格
        smsTable.innerHTML = '';

        // 检查是否有短信
        if (recordsToDisplay.length === 0) {
            emptySms.style.display = 'flex';
            return;
        }

        // 隐藏空短信提示
        emptySms.style.display = 'none';

        // 按时间倒序排序短信（最新的在前面）
        const sortedRecords = [...recordsToDisplay].sort((a, b) => new Date(b.sentTime) - new Date(a.sentTime));

        // 遍历短信并添加到表格
        sortedRecords.forEach(record => {
            // 格式化日期
            const smsDate = new Date(record.sentTime);
            const formattedDate = `${smsDate.getFullYear()}-${(smsDate.getMonth() + 1).toString().padStart(2, '0')}-${smsDate.getDate().toString().padStart(2, '0')} ${smsDate.getHours().toString().padStart(2, '0')}:${smsDate.getMinutes().toString().padStart(2, '0')}`;

            // 创建表格行
            const tr = document.createElement('tr');

            // 设置类型文本
            let typeText = record.type === 'order' ? '订单通知' : '留言通知';

            // 截断内容，如果太长
            const truncatedContent = record.content.length > 30 ? record.content.substring(0, 30) + '...' : record.content;

            // 设置表格行内容
            tr.innerHTML = `
                <td>${record.id}</td>
                <td>${typeText}</td>
                <td>${record.recipient}</td>
                <td>${record.title}</td>
                <td>${truncatedContent}</td>
                <td>${formattedDate}</td>
                <td><span class="status status-completed">${record.status === 'sent' ? '已发送' : record.status}</span></td>
                <td>
                    <button class="btn-icon view-sms" data-id="${record.id}"><i class="fas fa-eye"></i></button>
                    <button class="btn-icon delete-sms" data-id="${record.id}"><i class="fas fa-trash"></i></button>
                </td>
            `;

            // 添加到表格
            smsTable.appendChild(tr);
        });
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 搜索按钮点击事件
        if (searchBtn) {
            searchBtn.addEventListener('click', searchSmsRecords);
        }

        // 搜索框回车事件
        if (smsSearch) {
            smsSearch.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    searchSmsRecords();
                }
            });
        }

        // 类型筛选事件
        if (typeFilter) {
            typeFilter.addEventListener('change', searchSmsRecords);
        }

        // 表格行点击事件（使用事件委托）
        if (smsTable) {
            smsTable.addEventListener('click', function(e) {
                // 查看短信
                if (e.target.classList.contains('view-sms') || e.target.closest('.view-sms')) {
                    const button = e.target.classList.contains('view-sms') ? e.target : e.target.closest('.view-sms');
                    const smsId = parseInt(button.getAttribute('data-id'));
                    openSmsModal(smsId);
                }

                // 删除短信
                if (e.target.classList.contains('delete-sms') || e.target.closest('.delete-sms')) {
                    const button = e.target.classList.contains('delete-sms') ? e.target : e.target.closest('.delete-sms');
                    const smsId = parseInt(button.getAttribute('data-id'));
                    if (confirm('确定要删除这条短信记录吗？')) {
                        deleteSmsRecord(smsId);
                    }
                }
            });
        }

        // 关闭模态框
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', function() {
                smsModal.style.display = 'none';
            });
        });

        // 点击模态框外部关闭
        window.addEventListener('click', function(e) {
            if (e.target === smsModal) {
                smsModal.style.display = 'none';
            }
        });

        // 删除短信按钮
        if (deleteSmsBtn) {
            deleteSmsBtn.addEventListener('click', function() {
                if (confirm('确定要删除这条短信记录吗？')) {
                    deleteSmsRecord(currentSmsId);
                    smsModal.style.display = 'none';
                }
            });
        }
    }

    // 搜索短信
    function searchSmsRecords() {
        const searchTerm = smsSearch.value.toLowerCase();
        const typeValue = typeFilter.value;

        // 过滤短信
        let filteredRecords = smsRecords;

        // 按类型过滤
        if (typeValue !== 'all') {
            filteredRecords = filteredRecords.filter(record => record.type === typeValue);
        }

        // 按搜索词过滤
        if (searchTerm) {
            filteredRecords = filteredRecords.filter(record =>
                record.title.toLowerCase().includes(searchTerm) ||
                record.content.toLowerCase().includes(searchTerm) ||
                record.recipient.includes(searchTerm)
            );
        }

        // 加载过滤后的短信
        loadSmsRecords(filteredRecords);
    }

    // 打开短信详情模态框
    function openSmsModal(smsId) {
        // 查找短信
        const smsRecord = smsRecords.find(r => r.id === smsId);

        if (smsRecord) {
            // 保存当前短信ID
            currentSmsId = smsId;

            // 格式化日期
            const smsDate = new Date(smsRecord.sentTime);
            const formattedDate = `${smsDate.getFullYear()}-${(smsDate.getMonth() + 1).toString().padStart(2, '0')}-${smsDate.getDate().toString().padStart(2, '0')} ${smsDate.getHours().toString().padStart(2, '0')}:${smsDate.getMinutes().toString().padStart(2, '0')}`;

            // 设置类型文本
            let typeText = smsRecord.type === 'order' ? '订单通知' : '留言通知';

            // 设置模态框内容
            smsDetail.innerHTML = `
                <div class="sms-info">
                    <p><strong>短信ID:</strong> ${smsRecord.id}</p>
                    <p><strong>类型:</strong> ${typeText}</p>
                    <p><strong>接收人:</strong> ${smsRecord.recipient}</p>
                    <p><strong>发送时间:</strong> ${formattedDate}</p>
                    <p><strong>状态:</strong>
                        <span class="status status-completed">
                            ${smsRecord.status === 'sent' ? '已发送' : smsRecord.status}
                        </span>
                    </p>
                </div>
                <div class="sms-content">
                    <h3>短信标题:</h3>
                    <p>${smsRecord.title}</p>
                    <h3>短信内容:</h3>
                    <p>${smsRecord.content}</p>
                </div>
                <div class="sms-data">
                    <h3>相关数据:</h3>
                    <pre>${JSON.stringify(smsRecord.data, null, 2)}</pre>
                </div>
            `;

            // 显示模态框
            smsModal.style.display = 'block';
        }
    }

    // 删除短信记录
    function deleteSmsRecord(smsId) {
        // 过滤掉要删除的短信
        smsRecords = smsRecords.filter(r => r.id !== smsId);

        // 保存到localStorage
        localStorage.setItem('smsRecords', JSON.stringify(smsRecords));

        // 重新加载短信列表
        loadSmsRecords();
    }
});
