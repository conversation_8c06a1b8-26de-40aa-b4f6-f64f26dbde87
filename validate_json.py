#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件验证工具
用于检查products.json文件的语法和数据完整性
"""

import json
import os
import sys

def validate_products_json():
    """验证products.json文件"""
    
    products_file = 'lc-mall/data/products.json'
    
    if not os.path.exists(products_file):
        print(f"❌ 错误：找不到文件 {products_file}")
        return False
    
    try:
        # 检查JSON语法
        with open(products_file, 'r', encoding='utf-8') as f:
            products = json.load(f)
        
        print(f"✅ JSON语法检查通过")
        print(f"📊 产品数量: {len(products)}")
        
        # 检查数据完整性
        errors = []
        warnings = []
        
        # 检查每个产品
        for i, product in enumerate(products):
            product_name = product.get('name', f'产品{i+1}')
            
            # 检查必需字段
            required_fields = ['id', 'name', 'category', 'price']
            for field in required_fields:
                if field not in product:
                    errors.append(f"产品 '{product_name}' 缺少必需字段: {field}")
            
            # 检查ID唯一性
            product_id = product.get('id')
            if product_id is not None:
                duplicate_ids = [p for p in products if p.get('id') == product_id]
                if len(duplicate_ids) > 1:
                    errors.append(f"产品ID {product_id} 重复，产品: {product_name}")
            
            # 检查推荐字段
            recommended_fields = ['description', 'image', 'specifications', 'sku']
            for field in recommended_fields:
                if field not in product or not product[field]:
                    warnings.append(f"产品 '{product_name}' 缺少推荐字段: {field}")
            
            # 检查details字段
            if 'details' not in product:
                warnings.append(f"产品 '{product_name}' 缺少 details 字段")
            
            # 检查manual字段
            if 'manual' not in product:
                warnings.append(f"产品 '{product_name}' 缺少 manual 字段")
            elif isinstance(product['manual'], dict):
                manual = product['manual']
                if 'intro' not in manual or not manual['intro']:
                    warnings.append(f"产品 '{product_name}' 缺少产品简介 (manual.intro)")
                if 'usageList' not in manual or not manual['usageList']:
                    warnings.append(f"产品 '{product_name}' 缺少推荐用途 (manual.usageList)")
                if 'dosageList' not in manual or not manual['dosageList']:
                    warnings.append(f"产品 '{product_name}' 缺少推荐用量 (manual.dosageList)")
        
        # 显示结果
        if errors:
            print(f"\n❌ 发现 {len(errors)} 个错误:")
            for error in errors:
                print(f"   • {error}")
        
        if warnings:
            print(f"\n⚠️  发现 {len(warnings)} 个警告:")
            for warning in warnings:
                print(f"   • {warning}")
        
        if not errors and not warnings:
            print(f"\n🎉 所有检查通过！数据完整性良好。")
        elif not errors:
            print(f"\n✅ 没有严重错误，但有一些建议改进的地方。")
        
        return len(errors) == 0
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON语法错误:")
        print(f"   行号: {e.lineno}")
        print(f"   列号: {e.colno}")
        print(f"   错误: {e.msg}")
        print(f"   位置: 字符 {e.pos}")
        
        # 尝试显示错误附近的内容
        try:
            with open(products_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if e.lineno <= len(lines):
                error_line = lines[e.lineno - 1]
                print(f"   错误行内容: {error_line.strip()}")
                
                # 显示错误位置
                if e.colno > 0:
                    pointer = ' ' * (e.colno - 1) + '^'
                    print(f"   错误位置: {pointer}")
        except:
            pass
        
        return False
    
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        return False

def main():
    """主函数"""
    print("🔍 开始验证 products.json 文件...")
    print("=" * 50)
    
    success = validate_products_json()
    
    print("=" * 50)
    if success:
        print("✅ 验证完成，文件可以正常使用")
        sys.exit(0)
    else:
        print("❌ 验证失败，请修复错误后重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
