console.log('Script loaded: main.js');

// 预创建痕迹点池，提高性能
const trailDotPool = [];
const POOL_SIZE = 20;

// 初始化痕迹点池
for (let i = 0; i < POOL_SIZE; i++) {
    const dot = document.createElement('div');
    dot.className = 'trail-dot';
    dot.style.display = 'none';
    document.body.appendChild(dot);
    trailDotPool.push(dot);
}

// 默认产品数据，将从API获取
let products = [
    {
        id: 1,
        name: "潜固化剂 WL-1031",
        category: "潜固化剂",
        price: 580,
        image: "/lc-mall/images/products/WL-1031.jpg",
        description: "WL-1031是一种高性能潜固化剂，适用于单组分聚氨酯涂料、胶粘剂和密封胶。具有优异的储存稳定性和快速固化特性。",
        specifications: "包装规格：25kg/桶",
        sku: "LC-QGH-1031",
        // 库存字段已移除
        featured: true
    },
    {
        id: 2,
        name: "潜固化剂 WL-102",
        category: "潜固化剂",
        price: 620,
        image: "/lc-mall/images/products/WL-102.jpg",
        description: "WL-102是一种中等反应活性的潜固化剂，适用于需要较长操作时间的聚氨酯体系。具有良好的耐水解性和耐候性。",
        specifications: "包装规格：25kg/桶",
        sku: "LC-QGH-102",
        // 库存字段已移除
        featured: true
    },
    {
        id: 3,
        name: "潜固化剂 WL-104",
        category: "潜固化剂",
        price: 650,
        image: "/lc-mall/images/products/WL-104.jpg",
        description: "WL-104是一种低粘度潜固化剂，适用于需要良好渗透性的聚氨酯体系。具有优异的耐水解性和耐候性。",
        specifications: "包装规格：25kg/桶",
        sku: "LC-QGH-104",
        // 库存字段已移除
        featured: false
    },
    {
        id: 4,
        name: "潜固化剂 WL-101",
        category: "潜固化剂",
        price: 600,
        image: "/lc-mall/images/products/WL-101.jpg",
        description: "WL-101是一种通用型潜固化剂，适用于各种聚氨酯涂料和胶粘剂。具有良好的储存稳定性和固化特性。",
        specifications: "包装规格：25kg/桶",
        sku: "LC-QGH-101",
        // 库存字段已移除
        featured: false
    },
    {
        id: 5,
        name: "生物基树脂LC-450",
        category: "生物基树脂",
        price: 780,
        image: "/lc-mall/images/products/LC-450.jpg",
        description: "LC-450是一种高性能生物基树脂，由可再生资源制造，具有优异的机械性能和环保特性。适用于高端涂料和复合材料。",
        specifications: "包装规格：200kg/桶",
        sku: "LC-SWJ-450",
        // 库存字段已移除
        featured: true
    },
    {
        id: 6,
        name: "生物基树脂LC-320",
        category: "生物基树脂",
        price: 720,
        image: "/lc-mall/images/products/LC-320.jpg",
        description: "LC-320是一种中等粘度的生物基树脂，具有良好的柔韧性和耐候性。适用于弹性涂料和胶粘剂。",
        specifications: "包装规格：200kg/桶",
        sku: "LC-SWJ-320",
        // 库存字段已移除
        featured: true
    }
];

// DOM Elements - 将在DOMContentLoaded事件中初始化
let productList;
let cartModal;
let productModal;
let cartItems;
let cartTotalPrice;
let cartCount;
let floatingCart;
let floatingCartCount;
let checkoutBtn;
let productDetail;
let mobileNavToggle;
let nav;
let contactForm;

// Search and Filter Elements - 将在DOMContentLoaded事件中初始化
let productSearchInput;
let productSearchBtn;
let sortFilter;
let categoryFilter;
let productCountNumber;
let productPagination;
let currentCategoryTitle;
let categoryLinks;
let tagLinks;

// 初始化DOM元素引用
function initDOMElements() {
    console.log('初始化DOM元素引用...');

    // 主要DOM元素
    productList = document.getElementById('product-list');
    cartModal = document.getElementById('cart-modal');
    productModal = document.getElementById('product-modal');
    cartItems = document.getElementById('cart-items');
    cartTotalPrice = document.getElementById('cart-total-price');
    cartCount = document.getElementById('cart-count');
    floatingCart = document.getElementById('floating-cart');
    floatingCartCount = document.getElementById('floating-cart-count');
    checkoutBtn = document.getElementById('checkout-btn');
    productDetail = document.getElementById('product-detail');
    mobileNavToggle = document.querySelector('.mobile-nav-toggle');
    nav = document.querySelector('nav');
    contactForm = document.getElementById('contact-form');
    backToTopBtn = document.getElementById('back-to-top');

    // 搜索和过滤元素
    productSearchInput = document.getElementById('product-search-input');
    productSearchBtn = document.getElementById('product-search-btn');
    sortFilter = document.getElementById('sort-filter');
    categoryFilter = document.getElementById('category-filter');
    productCountNumber = document.getElementById('product-count-number');
    productPagination = document.getElementById('product-pagination');
    currentCategoryTitle = document.getElementById('current-category');
    categoryLinks = document.querySelectorAll('.category-link');
    tagLinks = document.querySelectorAll('.tag');

    console.log('DOM元素初始化完成:', {
        productList: !!productList,
        cartModal: !!cartModal,
        productSearchInput: !!productSearchInput,
        categoryLinks: categoryLinks ? categoryLinks.length : 0
    });
}

// Cart data
let cart = [];

// Initialize the application
async function init() {
    console.log('Initializing application...');

    // 检查当前页面是否是产品页面
    const isProductPage = document.getElementById('product-list') !== null;
    console.log('Is product page:', isProductPage);

    // 设置事件监听器 - 所有页面都需要
    setupEventListeners();

    // 如果不是产品页面，不需要加载产品数据
    if (!isProductPage) {
        console.log('当前页面不是产品页面，跳过产品加载');
        return;
    }

    console.log('Initial products data:', products);
    console.log('Product list element:', productList);

    // 设置搜索和过滤功能 - 只在产品页面需要
    setupSearchAndFilter();

    // 设置初始类别为"all"
    if (categoryLinks) {
        categoryLinks.forEach(link => {
            if (link.getAttribute('data-category') === 'all') {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    // 设置类别标题
    if (currentCategoryTitle) {
        currentCategoryTitle.textContent = '全部产品';
    }

    // 显示加载中提示
    if (productList) {
        productList.innerHTML = `
            <div class="loading-indicator">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在加载商品数据...</p>
            </div>
        `;
    }

    // 从API获取数据
    try {
        console.log('尝试从API获取商品数据...');
        const apiProducts = await ApiClient.getProducts();
        console.log('Products loaded from API:', apiProducts);

        if (apiProducts && apiProducts.length > 0) {
            // 更新商品数据
            products = apiProducts;
            console.log('成功获取API商品数据，商品数量:', products.length);
        } else {
            // 如果API没有返回数据，使用默认数据
            const errorMsg = 'API返回空数据，可能是数据文件为空或格式错误，使用默认数据';
            console.log(errorMsg, '，商品数量:', products.length);
            showErrorMessage(errorMsg, 'warning');
        }
    } catch (error) {
        const errorMsg = `API请求失败: ${error.message}，使用默认数据`;
        console.error('Error loading products from API:', error);
        console.log(errorMsg, '，商品数量:', products.length);
        showErrorMessage(errorMsg, 'error');
    }

    // 无论API请求成功与否，都显示商品数据
    console.log('显示商品数据，商品数量:', products.length);
    displayProducts(products);

    // 更新商品计数
    if (productCountNumber) {
        productCountNumber.textContent = products.length;
    }

    console.log('Application initialized successfully');
}

// 显示错误信息
function showErrorMessage(message, type = 'error') {
    // 创建错误提示元素
    const errorDiv = document.createElement('div');
    errorDiv.className = `error-message ${type}`;
    errorDiv.innerHTML = `
        <div class="error-content">
            <span class="error-icon">${type === 'error' ? '❌' : '⚠️'}</span>
            <span class="error-text">${message}</span>
            <button class="error-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    // 添加到页面顶部
    document.body.insertBefore(errorDiv, document.body.firstChild);

    // 5秒后自动消失
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

// Set up search and filter functionality
function setupSearchAndFilter() {
    if (productSearchBtn && productSearchInput) {
        productSearchBtn.addEventListener('click', () => {
            searchAndFilterProducts();
        });

        productSearchInput.addEventListener('keyup', (e) => {
            if (e.key === 'Enter') {
                searchAndFilterProducts();
            }
        });
    }

    if (categoryFilter) {
        categoryFilter.addEventListener('change', () => {
            searchAndFilterProducts();
        });
    }

    if (sortFilter) {
        sortFilter.addEventListener('change', () => {
            searchAndFilterProducts();
        });
    }
}

// Set active category and filter products
function setActiveCategory(category) {
    // Update active class on category links
    if (categoryLinks) {
        categoryLinks.forEach(link => {
            if (link.getAttribute('data-category') === category) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    // Update category title
    if (currentCategoryTitle) {
        if (category === 'all') {
            currentCategoryTitle.textContent = '全部产品';
        } else {
            currentCategoryTitle.textContent = category;
        }
    }

    // Filter products by category
    let filteredProducts = [...products];

    if (category !== 'all') {
        filteredProducts = filteredProducts.filter(product => product.category === category);
    }

    // Display filtered products
    displayProducts(filteredProducts);
}

// Search and filter products
function searchAndFilterProducts() {
    let filteredProducts = [...products];

    // Apply search filter
    const searchTerm = productSearchInput ? productSearchInput.value.trim().toLowerCase() : '';
    if (searchTerm) {
        filteredProducts = filteredProducts.filter(product =>
            product.name.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm) ||
            (product.sku && product.sku.toLowerCase().includes(searchTerm))
        );

        // Update category title for search results if it exists
        if (currentCategoryTitle) {
            currentCategoryTitle.textContent = `搜索结果: "${searchTerm}"`;
        }
    }

    // Apply sort filter
    const sortValue = sortFilter ? sortFilter.value : 'default';

    if (sortValue && sortValue !== 'default') {
        switch (sortValue) {
            case 'price-asc':
                filteredProducts.sort((a, b) => a.price - b.price);
                break;
            case 'price-desc':
                filteredProducts.sort((a, b) => b.price - a.price);
                break;
            case 'name-asc':
                filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'name-desc':
                filteredProducts.sort((a, b) => b.name.localeCompare(a.name));
                break;
        }
    }

    // Display filtered and sorted products
    displayProducts(filteredProducts);
}

// Display products in the product list
function displayProducts(productsToDisplay) {
    console.log('Displaying products:', productsToDisplay);

    // 更新商品计数
    if (productCountNumber) {
        productCountNumber.textContent = productsToDisplay.length;
    }

    // 检查是否有懒加载功能
    if (typeof window.displayProductsWithLazyLoad === 'function') {
        // 使用懒加载显示产品
        window.displayProductsWithLazyLoad(productsToDisplay);
    } else {
        // 回退到原始显示方法
        displayProductsOriginal(productsToDisplay);
    }
}

// 原始产品显示方法（作为备用）
function displayProductsOriginal(productsToDisplay) {
    console.log('使用原始方法显示产品:', productsToDisplay);
    console.log('Current DOM state - productList:', productList);
    console.log('Current DOM state - productList parent:', productList ? productList.parentElement : null);

    // 强制检查DOM元素
    if (!productList) {
        console.log('Product list element not found - 当前可能不是产品页面');
        return; // 静默返回，不显示错误
    }

    try {
        // 清空商品列表
        productList.innerHTML = '';
        console.log('Cleared product list');

        // 处理空商品列表的情况
        if (!productsToDisplay || productsToDisplay.length === 0) {
            console.log('没有商品可显示');
            const noProducts = document.createElement('div');
            noProducts.className = 'no-products';
            noProducts.innerHTML = `
                <i class="fas fa-search" style="font-size: 3rem; color: var(--text-lighter); margin-bottom: 1rem;"></i>
                <p>没有找到匹配的产品</p>
                <button class="btn btn-small" id="reset-filters">重置筛选条件</button>
            `;
            productList.appendChild(noProducts);

            // 添加重置按钮事件监听器
            const resetFiltersBtn = document.getElementById('reset-filters');
            if (resetFiltersBtn) {
                resetFiltersBtn.addEventListener('click', () => {
                    if (productSearchInput) productSearchInput.value = '';
                    if (categoryFilter) categoryFilter.value = 'all';
                    if (sortFilter) sortFilter.value = 'default';
                    displayProducts(products);
                });
            }

            return;
        }

        console.log(`准备显示 ${productsToDisplay.length} 个商品`);
    } catch (error) {
        console.error('处理商品列表时出错:', error);
        return;
    }

    productsToDisplay.forEach(product => {
        console.log('Creating card for product:', product.name);

        // 库存状态已移除

        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        productCard.setAttribute('data-id', product.id);
        productCard.innerHTML = `
            <div class="product-card-header">
                <img src="${product.image}" alt="${product.name}">
                <div class="product-badges">
                    ${product.featured ? '<span class="product-badge badge-featured">推荐</span>' : ''}
                </div>
                <div class="product-category-tag">${product.category}</div>
            </div>
            <div class="product-info">
                <div class="product-title-row">
                    <h3>${product.name}</h3>
                </div>
                <div class="product-meta">
                    ${product.sku ? `<div class="product-sku">SKU: ${product.sku}</div>` : ''}
                </div>
                <p>${product.description ? product.description.substring(0, 100) + '...' : '暂无产品描述'}</p>
                <div class="price-cart-row">
                    <div class="price">¥${product.price.toFixed(2)}</div>
                    <a href="#" class="btn-product add-to-cart" data-id="${product.id}">
                        加入购物车
                    </a>
                </div>
            </div>
        `;
        productList.appendChild(productCard);
        console.log('Added product card to DOM:', product.name);
    });

    console.log('Finished displaying products');
}

// Set up event listeners
function setupEventListeners() {
    // Product section link in hero section - 只在首页添加
    const productSectionLink = document.querySelector('a[href="#products-section"]');
    if (productSectionLink) {
        productSectionLink.addEventListener('click', (e) => {
            e.preventDefault();
            const productsSection = document.getElementById('products-section');
            if (productsSection) {
                productsSection.scrollIntoView({ behavior: 'smooth' });
            }
        });
    }

    // 支付方式选择（只有微信支付，无需切换）
    const paymentMethods = document.querySelectorAll('.payment-method');
    if (paymentMethods.length > 0) {
        // 确保微信支付始终被选中
        paymentMethods.forEach(method => {
            method.classList.add('selected');
        });
    }

    // 回到顶部按钮
    if (backToTopBtn) {
        // 监听滚动事件，控制按钮显示/隐藏
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });

        // 点击回到顶部
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Category links in sidebar
    if (categoryLinks) {
        categoryLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const category = e.target.getAttribute('data-category');
                setActiveCategory(category);
            });
        });
    }

    // 产品卡片点击事件 - 只在产品页面添加
    if (productList) {
        productList.addEventListener('click', (e) => {
            // 处理加入购物车按钮点击
            if (e.target.classList.contains('add-to-cart') || e.target.closest('.add-to-cart')) {
                e.preventDefault();
                e.stopPropagation(); // 阻止事件冒泡到卡片
                const button = e.target.classList.contains('add-to-cart') ? e.target : e.target.closest('.add-to-cart');

                // 检查按钮是否被禁用
                if (button.hasAttribute('disabled') || button.classList.contains('disabled')) {
                    return;
                }

                const productId = parseInt(button.getAttribute('data-id'));
                addToCart(productId, button);
                return;
            }

            // 处理整个产品卡片点击 - 打开详情模态框
            const productCard = e.target.closest('.product-card');
            if (productCard) {
                e.preventDefault();
                const productId = parseInt(productCard.getAttribute('data-id'));
                openProductModal(productId);
            }
        });
    }

    // Close modals
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', () => {
            cartModal.classList.remove('show');
            productModal.classList.remove('show');

            // Hide modal after animation completes
            setTimeout(() => {
                cartModal.style.display = 'none';
                productModal.style.display = 'none';
            }, 300);
        });
    });

    // 固定购物车图标点击事件
    if (floatingCart) {
        floatingCart.addEventListener('click', (e) => {
            e.preventDefault();

            // 添加点击波纹效果
            floatingCart.classList.add('clicked');

            // 添加触觉反馈（如果浏览器支持）
            if (navigator.vibrate) {
                navigator.vibrate(50); // 50毫秒的振动
            }

            // 播放点击声音
            playCartClickSound();

            // 移除波纹效果类（动画结束后）
            setTimeout(() => {
                floatingCart.classList.remove('clicked');
            }, 600);

            openCartModal();
        });
    }

    // 原购物车图标点击事件（保留兼容性）
    const headerCartIcon = document.querySelector('.cart-icon a');
    if (headerCartIcon) {
        headerCartIcon.addEventListener('click', (e) => {
            e.preventDefault();

            // 添加点击效果
            const cartIconParent = headerCartIcon.closest('.cart-icon');
            if (cartIconParent) {
                cartIconParent.classList.add('cart-icon-pulse');

                // 移除效果类（动画结束后）
                setTimeout(() => {
                    cartIconParent.classList.remove('cart-icon-pulse');
                }, 600);
            }

            // 添加触觉反馈（如果浏览器支持）
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            // 播放点击声音
            playCartClickSound();

            openCartModal();
        });
    }

    // Checkout button
    checkoutBtn.addEventListener('click', checkout);

    // Mobile navigation toggle
    mobileNavToggle.addEventListener('click', () => {
        const navContainer = document.querySelector('.nav-container');
        navContainer.classList.toggle('active');

        // Toggle icon
        const icon = mobileNavToggle.querySelector('i');
        if (navContainer.classList.contains('active')) {
            icon.className = 'fas fa-times';

            // 添加遮罩层
            const overlay = document.createElement('div');
            overlay.className = 'mobile-nav-overlay';
            document.body.appendChild(overlay);

            // 禁止背景滚动
            document.body.style.overflow = 'hidden';

            // 点击遮罩层关闭导航
            overlay.addEventListener('click', () => {
                navContainer.classList.remove('active');
                icon.className = 'fas fa-bars';
                document.body.removeChild(overlay);
                document.body.style.overflow = '';
            });
        } else {
            icon.className = 'fas fa-bars';

            // 移除遮罩层
            const overlay = document.querySelector('.mobile-nav-overlay');
            if (overlay) {
                document.body.removeChild(overlay);
            }

            // 恢复背景滚动
            document.body.style.overflow = '';
        }
    });

    // Contact form submission
    if (contactForm) {
        contactForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            // Get form data
            const name = document.getElementById('name').value;
            const phone = document.getElementById('phone').value;
            const message = document.getElementById('message').value;

            // Create message object
            const messageData = {
                name: name,
                phone: phone,
                message: message,
                date: new Date().toISOString(),
                status: 'unread'
            };

            try {
                // 使用API保存留言
                const savedMessage = await ApiClient.addMessage(messageData);

                if (savedMessage) {
                    console.log('留言已保存到服务器:', savedMessage);

                    // 发送短信通知
                    if (window.smsService) {
                        window.smsService.sendSmsNotification(window.smsService.SMS_TYPES.MESSAGE, savedMessage);
                    }

                    // Show success message
                    alert('感谢您的留言，我们会尽快与您联系！');
                    contactForm.reset();
                } else {
                    throw new Error('保存留言失败');
                }
            } catch (error) {
                console.error('提交留言失败:', error);
                alert('留言提交失败，请稍后重试');
            }
        });
    }

    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === cartModal) {
            cartModal.classList.remove('show');
            setTimeout(() => {
                cartModal.style.display = 'none';
            }, 300);
        }
        if (e.target === productModal) {
            productModal.classList.remove('show');
            setTimeout(() => {
                productModal.style.display = 'none';
            }, 300);
        }
    });
}

// Open product modal with details
function openProductModal(productId) {
    const product = products.find(p => p.id === productId);

    if (product) {
        // 库存状态已移除

        // 提取产品型号（如果有）
        const modelMatch = product.name.match(/([A-Z]+-\d+)/);
        const productModel = modelMatch ? modelMatch[1] : '';

        productDetail.innerHTML = `
            <div class="product-modal-content">
                <div class="product-modal-header">
                    <h2 class="product-detail-title">${product.name}</h2>
                    <div class="registration-bubble">
                        <div class="bubble-content">
                            <i class="fas fa-gift"></i> 注册可免费领取一份50g样品
                            <div class="bubble-arrow"></div>
                        </div>
                    </div>
                </div>

                <div class="product-detail-main">
                    <div class="product-modal-image">
                        <img src="${product.image}" alt="${product.name}">
                    </div>

                    <div class="product-modal-info">
                        <div class="product-detail-basic-info">
                            <table class="product-detail-table">
                                <tbody>
                                    <tr>
                                        <td class="table-label">产品名称</td>
                                        <td>${product.name}</td>
                                        <td class="table-label">执行标准</td>
                                        <td>${product.details && product.details.standard ? product.details.standard : '国标'}</td>
                                    </tr>
                                    <tr>
                                        <td class="table-label">外观性状</td>
                                        <td>${product.details && product.details.appearance ? product.details.appearance : '液体'}</td>
                                        <td class="table-label">型号</td>
                                        <td>${product.details && product.details.model ? product.details.model : (productModel || '/')}</td>
                                    </tr>
                                    <tr>
                                        <td class="table-label">包装规格</td>
                                        <td>${product.specifications || '25kg/桶'}</td>
                                        <td class="table-label">用途范围</td>
                                        <td>${product.details && product.details.usage ? product.details.usage : '聚氨酯、粘结剂、聚醚等'}</td>
                                    </tr>
                                    <tr>
                                        <td class="table-label">闪点</td>
                                        <td>${product.details && product.details.flashpoint ? product.details.flashpoint : '147℃'}</td>
                                        <td class="table-label">气味</td>
                                        <td>${product.details && product.details.smell ? product.details.smell : '低'}</td>
                                    </tr>
                                    <tr>
                                        <td class="table-label">功能</td>
                                        <td>${product.details && product.details.function ? product.details.function : '交联固化'}</td>
                                        <td class="table-label">胺值(mgKOH/g)</td>
                                        <td>${product.details && product.details.amineValue ? product.details.amineValue : '235'}</td>
                                    </tr>
                                    <tr>
                                        <td class="table-label">有效成分含量</td>
                                        <td>${product.details && product.details.content ? product.details.content : '99%'}</td>
                                        <td class="table-label">是否进口</td>
                                        <td>${product.details && product.details.imported ? product.details.imported : '否'}</td>
                                    </tr>
                                    <tr>
                                        <td class="table-label">比重(25℃, g/ml)</td>
                                        <td>${product.details && product.details.density ? product.details.density : '1.02'}</td>
                                        <td class="table-label">颜色</td>
                                        <td>${product.details && product.details.color ? product.details.color : '无色或淡黄色'}</td>
                                    </tr>
                                    <tr>
                                        <td class="table-label">品牌</td>
                                        <td>龙驰科技</td>
                                        <td class="table-label">粘度@25℃</td>
                                        <td>${product.details && product.details.viscosity ? product.details.viscosity : '300 mPa.s'}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="product-detail-actions">
                            <div class="product-price-stock">
                                <div class="product-price">${product.price.toFixed(2)}</div>
                            </div>
                            <button class="add-to-cart-modal" data-id="${product.id}">
                                加入购物车
                            </button>
                        </div>
                    </div>
                </div>

                <div class="product-detail-document">
                    <h3>${productModel || product.name} 说明书</h3>

                    <div class="product-detail-tabs">
                        <div class="tab-header">
                            <div class="tab-item active" data-tab="intro">产品简介</div>
                            <div class="tab-item" data-tab="usage">推荐用途</div>
                            <div class="tab-item" data-tab="dosage">推荐用量</div>
                        </div>

                        <div class="tab-content">
                            <div class="tab-pane active" id="tab-intro">
                                <div class="product-intro">
                                    <div class="intro-content">
                                        ${(() => {
                                            // 优先使用manual.intro，如果没有则使用description
                                            if (product.manual && product.manual.intro) {
                                                // 将换行符分割的内容转换为卡片式布局
                                                const lines = product.manual.intro.split('\n').filter(line => line.trim());
                                                if (lines.length > 1 && lines[0].includes('产品特点')) {
                                                    // 如果第一行是"产品特点:"，则处理为卡片格式
                                                    return lines.slice(1).map(line => {
                                                        if (line.trim().startsWith('◆')) {
                                                            return `<div class="intro-item">
                                                                <span class="intro-icon">◆</span>
                                                                <span>${line.replace('◆', '').trim()}</span>
                                                            </div>`;
                                                        }
                                                        return `<div class="intro-item">
                                                            <span class="intro-icon">◆</span>
                                                            <span>${line.trim()}</span>
                                                        </div>`;
                                                    }).join('');
                                                } else {
                                                    // 普通格式，转换为卡片
                                                    return lines.map(line => `<div class="intro-item">
                                                        <span class="intro-icon">◆</span>
                                                        <span>${line.trim()}</span>
                                                    </div>`).join('');
                                                }
                                            } else if (product.description) {
                                                // 处理description字段
                                                const lines = product.description.split('\n').filter(line => line.trim());
                                                if (lines.length > 1 && lines[0].includes('产品特点')) {
                                                    return lines.slice(1).map(line => {
                                                        if (line.trim().startsWith('◆')) {
                                                            return `<div class="intro-item">
                                                                <span class="intro-icon">◆</span>
                                                                <span>${line.replace('◆', '').trim()}</span>
                                                            </div>`;
                                                        }
                                                        return `<div class="intro-item">
                                                            <span class="intro-icon">◆</span>
                                                            <span>${line.trim()}</span>
                                                        </div>`;
                                                    }).join('');
                                                } else {
                                                    return lines.map(line => `<div class="intro-item">
                                                        <span class="intro-icon">◆</span>
                                                        <span>${line.trim()}</span>
                                                    </div>`).join('');
                                                }
                                            } else {
                                                return `<div class="intro-item">
                                                    <span class="intro-icon">◆</span>
                                                    <span>${productModel || product.name} 是一款主要类产品，产品粘度低，适用于单组分型及多组分型的聚氨酯材料。</span>
                                                </div>
                                                <div class="intro-item">
                                                    <span class="intro-icon">◆</span>
                                                    <span>作为聚氨酯粘合剂，能与空气中的湿气反应，释放二元胺，可做为单组分聚氨酯酸性环氧树脂的交联剂，也可做为制品的除水剂。该产品能与异氰酸酯共存，特别用于单液型聚氨酯涂料和密封胶制品时可以提高体系的储存稳定性。</span>
                                                </div>`;
                                            }
                                        })()}
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane" id="tab-usage">
                                <div class="product-usage">
                                    ${product.manual && product.manual.usageList && product.manual.usageList.length > 0 ?
                                        `<ul class="usage-list">
                                            ${product.manual.usageList.map(usage => `<li>${usage}</li>`).join('')}
                                        </ul>` :
                                        `<div class="no-data">暂无推荐用途信息</div>`
                                    }
                                </div>
                            </div>

                            <div class="tab-pane" id="tab-dosage">
                                <div class="product-dosage">
                                    ${product.manual && product.manual.dosageList && product.manual.dosageList.length > 0 ?
                                        `<div class="dosage-content">
                                            ${product.manual.dosageList.map(dosage => `
                                                <div class="dosage-item">
                                                    <span class="dosage-icon">◆</span>
                                                    <span>${dosage}</span>
                                                </div>
                                            `).join('')}
                                            <div class="dosage-note">
                                                最佳用量根据产品体系试验为准。
                                            </div>
                                        </div>` :
                                        `<div class="no-data">暂无推荐用量信息</div>`
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 价格说明部分 -->
                <div class="price-explanation-section">
                    <h3>价格说明</h3>
                    <p>本产品价格为出厂价，不含运费。大宗采购可享受优惠，详情请联系销售人员。</p>
                    <p>价格有效期：自发布之日起30天内有效。</p>
                    <p>付款方式：支持银行转账、支付宝、微信支付等多种付款方式。</p>
                </div>

                <!-- 联系我们部分 -->
                <div class="contact-info-section">
                    <h3>联系我们</h3>
                    <p><strong>广州市龙驰新材料科技有限公司</strong></p>
                    <p>吴经理：17620086966</p>
                    <p>郑经理：17620026642</p>
                    <p>邮箱：<a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>地址：广州市白云区鹤龙街康庄路1号</p>
                </div>
            </div>
        `;

        // 顶部标签已移除，不需要添加切换功能

        // 添加产品详情标签切换功能
        const tabItems = productDetail.querySelectorAll('.tab-item');
        const tabPanes = productDetail.querySelectorAll('.tab-pane');

        tabItems.forEach(item => {
            item.addEventListener('click', () => {
                // 移除所有活动状态
                tabItems.forEach(tab => tab.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // 添加当前活动状态
                item.classList.add('active');
                const tabId = `tab-${item.getAttribute('data-tab')}`;
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Add event listener to the add to cart button in the modal
        const addToCartBtn = productDetail.querySelector('.add-to-cart-modal');
        addToCartBtn.addEventListener('click', () => {
            if (!addToCartBtn.hasAttribute('disabled')) {
                addToCart(productId, addToCartBtn);

                // 不要立即关闭模态框，让用户看到飞入购物车的动画
                setTimeout(() => {
                    productModal.classList.remove('show');
                    setTimeout(() => {
                        productModal.style.display = 'none';
                    }, 300);
                }, 1000);
            }
        });

        // Show modal with animation
        productModal.style.display = 'block';
        setTimeout(() => {
            productModal.classList.add('show');
        }, 10);
    }
}

// Add product to cart
function addToCart(productId, sourceElement = null) {
    const product = products.find(p => p.id === productId);

    if (product) {
        // 库存检查已移除

        // Check if product is already in cart
        const existingItem = cart.find(item => item.product.id === productId);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            cart.push({
                product: product,
                quantity: 1
            });
        }

        // 创建飞入购物车的动画
        if (sourceElement) {
            createFlyToCartAnimation(sourceElement);
        }

        updateCartCount();
        showAddToCartNotification(product.name);
    }
}

// 创建飞入购物车的动画（使用产品名称）
function createFlyToCartAnimation(sourceElement) {
    // 获取点击元素的位置
    const sourceRect = sourceElement.getBoundingClientRect();
    const sourceX = sourceRect.left + sourceRect.width / 2;
    const sourceY = sourceRect.top + sourceRect.height / 2;

    // 获取购物车图标的位置（优先使用固定在右侧的购物车图标）
    const cartIcon = document.getElementById('floating-cart') || document.querySelector('.cart-icon');
    const cartRect = cartIcon.getBoundingClientRect();
    const cartX = cartRect.left + cartRect.width / 2;
    const cartY = cartRect.top + cartRect.height / 2;

    // 查找产品名称
    let productName = '';
    let productCard = sourceElement.closest('.product-card');

    if (productCard) {
        const nameElement = productCard.querySelector('h3');
        if (nameElement) {
            productName = nameElement.textContent;
        }
    } else {
        // 可能是在产品详情模态框中
        const modalContent = sourceElement.closest('.product-modal-content');
        if (modalContent) {
            const nameElement = modalContent.querySelector('h2');
            if (nameElement) {
                productName = nameElement.textContent;
            }
        }
    }

    // 如果没有找到产品名称，使用默认文本
    if (!productName) {
        productName = '已加入购物车';
    }

    // 创建飞行元素
    const flyElement = document.createElement('div');
    flyElement.className = 'product-name-fly';
    flyElement.textContent = productName;
    flyElement.style.left = `${sourceX - 75}px`; // 居中显示
    flyElement.style.top = `${sourceY}px`;
    document.body.appendChild(flyElement);

    // 创建抛物线动画
    setTimeout(() => {
        // 添加动画类
        flyElement.classList.add('animate');

        // 设置动画起始时间
        const startTime = Date.now();

        // 动画总时间（毫秒）
        const duration = 600; // 更快的动画

        // 抛物线高度（负值表示向上）
        const arcHeight = -200;

        // 动画帧
        function animateFrame() {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用二次贝塞尔曲线计算抛物线路径
            const startX = sourceX - 75; // 考虑元素宽度
            const startY = sourceY;
            const endX = cartX - 75; // 考虑元素宽度
            const endY = cartY;

            // 计算当前位置
            const t = progress;
            const u = 1 - t;
            const tt = t * t;
            const uu = u * u;

            // 二次贝塞尔曲线
            const controlX = startX + (endX - startX) * 0.5;
            const controlY = Math.min(startY, endY) + arcHeight;

            const x = uu * startX + 2 * u * t * controlX + tt * endX;
            const y = uu * startY + 2 * u * t * controlY + tt * endY;

            // 应用位置
            flyElement.style.left = `${x}px`;
            flyElement.style.top = `${y}px`;

            // 添加缩放和透明度效果
            const scale = 1 - 0.5 * progress; // 从1缩小到0.5
            const opacity = 1 - 0.7 * progress; // 逐渐变透明

            flyElement.style.transform = `scale(${scale})`;
            flyElement.style.opacity = opacity;

            // 继续动画或结束
            if (progress < 1) {
                requestAnimationFrame(animateFrame);
            } else {
                // 动画结束，移除元素
                document.body.removeChild(flyElement);

                // 添加购物车图标缩放动画
                cartIcon.classList.add('cart-icon-pulse');

                // 播放添加到购物车的音效
                playAddToCartSound();

                setTimeout(() => {
                    cartIcon.classList.remove('cart-icon-pulse');
                }, 600);
            }
        }

        // 开始动画
        requestAnimationFrame(animateFrame);
    }, 10);
}

// 创建轨迹痕迹点
function createTrailDot(x, y, scale) {
    // 从池中获取一个可用的点
    const dot = trailDotPool.find(d => d.style.display === 'none');
    if (!dot) return; // 如果没有可用的点，则跳过

    // 随机颜色
    const colors = [
        '#FF6B6B', // 红色
        '#4ECDC4', // 青色
        '#45B7D1', // 蓝色
        '#96CEB4', // 绿色
        '#FFEEAD', // 黄色
        '#D4A5A5'  // 粉色
    ];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];

    // 设置点的位置和样式
    dot.style.display = 'block';
    dot.style.opacity = '1';
    dot.style.backgroundColor = randomColor;
    dot.style.transform = `translate3d(${x}px, ${y}px, 0) scale(${scale})`;

    // 添加消失动画
    requestAnimationFrame(() => {
        dot.style.opacity = '0';
        dot.style.transform = `translate3d(${x}px, ${y}px, 0) scale(${scale * 1.5})`;
    });

    // 在动画结束后将点返回池中
    setTimeout(() => {
        dot.style.display = 'none';
    }, 1000);
}

// 创建购物车图标飞行动画
function createFlyingCartIcon(sourceX, sourceY, cartX, cartY) {
    const flyElement = document.createElement('div');
    flyElement.className = 'cart-item-fly';
    flyElement.innerHTML = '<i class="fas fa-shopping-cart"></i>';
    flyElement.style.left = `${sourceX}px`;
    flyElement.style.top = `${sourceY}px`;
    document.body.appendChild(flyElement);

    // 创建抛物线动画
    setTimeout(() => {
        // 添加动画类
        flyElement.classList.add('animate');

        // 设置动画起始时间
        const startTime = Date.now();

        // 动画总时间（毫秒）- 减少时间使移动更快
        const duration = 800;

        // 抛物线高度（负值表示向上）- 保持较大值使抛物线明显
        const arcHeight = -350;

        // 动画帧
        function animateFrame() {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用二次贝塞尔曲线计算抛物线路径
            const startX = sourceX;
            const startY = sourceY;
            const endX = cartX;
            const endY = cartY;

            // 计算当前位置
            const t = progress;
            const u = 1 - t;
            const tt = t * t;
            const uu = u * u;

            // 二次贝塞尔曲线
            const controlX = startX + (endX - startX) * 0.5;
            const controlY = Math.min(startY, endY) + arcHeight;

            const x = uu * startX + 2 * u * t * controlX + tt * endX;
            const y = uu * startY + 2 * u * t * controlY + tt * endY;

            // 应用位置和缩放
            const scale = 1 - 0.4 * progress; // 从1缩小到0.6，减少缩小程度
            flyElement.style.transform = `translate3d(${x}px, ${y}px, 0) scale(${scale})`;

            // 创建轨迹痕迹 - 增加频率以适应更快的动画
            if (progress % 0.1 < 0.01) { // 降低痕迹点创建频率
                createTrailDot(x, y, scale);
            }

            // 继续动画或结束
            if (progress < 1) {
                requestAnimationFrame(animateFrame);
            } else {
                // 动画结束，移除元素
                flyElement.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(flyElement);
                }, 300);

                // 添加购物车图标缩放动画
                if (floatingCart) {
                    floatingCart.classList.add('cart-icon-pulse');
                }

                // 播放添加到购物车的音效
                playAddToCartSound();

                // 创建粒子爆炸效果
                createParticleExplosion(cartX, cartY);

                // 显示添加成功提示
                showAddSuccessIndicator(cartX, cartY);

                setTimeout(() => {
                    if (floatingCart) {
                        floatingCart.classList.remove('cart-icon-pulse');
                    }
                }, 600);
            }
        }

        // 开始动画
        requestAnimationFrame(animateFrame);
    }, 10);
}

function createTrailDot(x, y, scale) {
    // 从池中获取一个可用的点
    const dot = trailDotPool.find(d => d.style.display === 'none');
    if (!dot) return; // 如果没有可用的点，则跳过

    // 设置点的位置和样式
    dot.style.display = 'block';
    dot.style.opacity = '1';
    dot.style.transform = `translate3d(${x}px, ${y}px, 0) scale(${scale})`;

    // 添加消失动画
    requestAnimationFrame(() => {
        dot.style.opacity = '0';
        dot.style.transform = `translate3d(${x}px, ${y}px, 0) scale(${scale * 1.5})`;
    });

    // 在动画结束后将点返回池中
    setTimeout(() => {
        dot.style.display = 'none';
    }, 1000);
}

// 显示添加成功提示
function showAddSuccessIndicator(x, y) {
    const indicator = document.createElement('div');
    indicator.className = 'add-success-indicator';
    indicator.textContent = '✓ 已添加到购物车';
    indicator.style.left = `${x - 75}px`;
    indicator.style.top = `${y - 50}px`;
    document.body.appendChild(indicator);

    // 显示提示
    setTimeout(() => {
        indicator.classList.add('show');

        // 3秒后隐藏并移除
        setTimeout(() => {
            indicator.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(indicator);
            }, 300);
        }, 2000);
    }, 10);
}

// 播放添加到购物车的音效
function playAddToCartSound() {
    try {
        // 创建音频上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // 创建振荡器
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        // 连接节点
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // 设置音频参数
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // A5
        oscillator.frequency.exponentialRampToValueAtTime(440, audioContext.currentTime + 0.2); // A4

        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.05);
        gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);

        // 播放音频
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.3);
    } catch (e) {
        console.log('Audio context not supported or user interaction required');
    }
}

// 播放购物车点击的音效
function playCartClickSound() {
    try {
        // 创建音频上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // 创建振荡器
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        // 连接节点
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // 设置音频参数 - 使用不同的声音
        oscillator.type = 'triangle'; // 使用三角波
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime); // 较低的频率
        oscillator.frequency.exponentialRampToValueAtTime(300, audioContext.currentTime + 0.1); // 快速下降

        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.02);
        gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.2);

        // 播放音频
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.2);
    } catch (e) {
        console.log('Audio context not supported or user interaction required');
    }
}

// 粒子爆炸效果已移除

// Update cart count display
function updateCartCount() {
    const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
    if (cartCount) {
        cartCount.textContent = totalItems;
    }
    if (floatingCartCount) {
        floatingCartCount.textContent = totalItems;

        // 添加数量变化动画
        floatingCartCount.classList.remove('count-change');
        void floatingCartCount.offsetWidth; // 触发重绘
        floatingCartCount.classList.add('count-change');
    }
}

// 显示通知
function showNotification(message, type = 'success') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    // 添加图标
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-times-circle"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle"></i>';
            break;
        case 'info':
            icon = '<i class="fas fa-info-circle"></i>';
            break;
    }

    notification.innerHTML = `
        <div class="notification-icon">${icon}</div>
        <div class="notification-message">${message}</div>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后移除
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 3000);
}

// 显示添加到购物车的通知
function showAddToCartNotification(productName) {
    showNotification(`${productName} 已添加到购物车`, 'success');
}

// Open cart modal
function openCartModal() {
    // Update cart items display
    updateCartDisplay();

    // Show modal with animation
    cartModal.style.display = 'block';
    setTimeout(() => {
        cartModal.classList.add('show');
    }, 10);
}

// Update cart display
function updateCartDisplay() {
    cartItems.innerHTML = '';

    if (cart.length === 0) {
        cartItems.innerHTML = '<p class="empty-cart">购物车为空</p>';
        cartTotalPrice.textContent = '¥0.00';
        return;
    }

    let total = 0;

    cart.forEach(item => {
        const itemTotal = item.product.price * item.quantity;
        total += itemTotal;

        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        cartItem.innerHTML = `
            <div class="cart-item-image">
                <img src="${item.product.image}" alt="${item.product.name}">
            </div>
            <div class="cart-item-details">
                <h3>${item.product.name}</h3>
                <p>¥${item.product.price.toFixed(2)} × ${item.quantity}</p>
                <p class="cart-item-total">¥${itemTotal.toFixed(2)}</p>
            </div>
            <div class="cart-item-actions">
                <button class="cart-item-decrease" data-id="${item.product.id}">-</button>
                <span class="cart-item-quantity">${item.quantity}</span>
                <button class="cart-item-increase" data-id="${item.product.id}">+</button>
                <button class="cart-item-remove" data-id="${item.product.id}">×</button>
            </div>
        `;

        cartItems.appendChild(cartItem);
    });

    // Update total price
    cartTotalPrice.textContent = `¥${total.toFixed(2)}`;

    // Add event listeners to cart item buttons
    document.querySelectorAll('.cart-item-decrease').forEach(button => {
        button.addEventListener('click', () => {
            const productId = parseInt(button.getAttribute('data-id'));
            decreaseQuantity(productId);
        });
    });

    document.querySelectorAll('.cart-item-increase').forEach(button => {
        button.addEventListener('click', () => {
            const productId = parseInt(button.getAttribute('data-id'));
            increaseQuantity(productId);
        });
    });

    document.querySelectorAll('.cart-item-remove').forEach(button => {
        button.addEventListener('click', () => {
            const productId = parseInt(button.getAttribute('data-id'));
            removeFromCart(productId);
        });
    });
}

// Decrease item quantity in cart
function decreaseQuantity(productId) {
    const item = cart.find(item => item.product.id === productId);

    if (item) {
        item.quantity -= 1;

        if (item.quantity <= 0) {
            removeFromCart(productId);
        } else {
            updateCartDisplay();
            updateCartCount();
        }
    }
}

// Increase item quantity in cart
function increaseQuantity(productId) {
    const item = cart.find(item => item.product.id === productId);

    if (item) {
        item.quantity += 1;
        updateCartDisplay();
        updateCartCount();
    }
}

// Remove item from cart
function removeFromCart(productId) {
    cart = cart.filter(item => item.product.id !== productId);
    updateCartDisplay();
    updateCartCount();
}

// Checkout process
async function checkout() {
    if (cart.length === 0) {
        showNotification('购物车为空，请先添加商品', 'warning');
        return;
    }

    // 库存检查已移除

    // 检查用户是否已登录
    const loginStatus = ApiClient.checkUserLogin();

    if (!loginStatus.loggedIn) {
        // 用户未登录，提示登录
        showNotification('请先登录后再提交订单', 'warning');

        // 打开登录模态框
        const loginModal = document.getElementById('login-modal');
        if (loginModal) {
            loginModal.style.display = 'block';
        }

        return;
    }

    // 用户已登录，获取用户信息
    const user = loginStatus.user;

    // 获取选择的支付方式（默认微信支付）
    const selectedPaymentMethod = document.querySelector('.payment-method.selected');
    const paymentMethod = selectedPaymentMethod ? selectedPaymentMethod.getAttribute('data-payment') : 'wechat';

    // 创建订单
    const order = {
        date: new Date().toISOString(),
        customer: {
            id: user.id,
            name: user.name,
            phone: user.phone,
            company: user.company,
            wechat: user.wechat || "",
            position: user.position || "",
            address: user.address || "",
            notes: ""
        },
        items: cart.map(item => ({
            id: item.product.id,
            name: item.product.name,
            price: item.product.price,
            quantity: item.quantity
        })),
        total: cart.reduce((total, item) => total + (item.product.price * item.quantity), 0),
        status: 'pending',
        payment_method: paymentMethod
    };

    // 显示加载中提示
    showNotification('正在提交订单...', 'info');

    try {
        // 使用API保存订单
        const savedOrder = await ApiClient.addOrder(order);

        if (savedOrder) {
            console.log('订单已保存到服务器:', savedOrder);

            // 从API获取更新的商品数据（库存已更新）
            const apiProducts = await ApiClient.getProducts();
            if (apiProducts && apiProducts.length > 0) {
                products = apiProducts;
            }

            // 显示微信支付收款码
            try {
                // 显示微信收款码
                const paymentSuccess = await WeChatPay.showPaymentQRCode(savedOrder.id, order.total);

                if (paymentSuccess) {
                    // 用户确认已支付，更新订单状态
                    try {
                        // 这里可以调用API更新订单状态为"等待确认"
                        savedOrder.status = 'payment_pending';
                        savedOrder.payment_method = 'wechat';
                        savedOrder.payment_confirmed_at = new Date().toISOString();

                        // 发送企业微信通知
                        try {
                            console.log('发送企业微信通知...');
                            const notifySuccess = await WeChatWorkNotify.sendPaymentConfirmation(savedOrder);
                            if (notifySuccess) {
                                console.log('企业微信通知发送成功');
                            } else {
                                console.warn('企业微信通知发送失败，但不影响订单处理');
                            }
                        } catch (notifyError) {
                            console.error('企业微信通知发送异常:', notifyError);
                            // 通知失败不影响订单流程
                        }

                        showNotification('感谢您的支付确认！我们会在收到款项后尽快处理您的订单', 'success');
                    } catch (updateError) {
                        console.error('更新订单状态失败:', updateError);
                        showNotification('支付确认已记录，订单处理中', 'success');
                    }
                } else {
                    // 支付取消
                    showNotification('支付已取消，您可以稍后在订单中心完成支付', 'warning');
                }
            } catch (payError) {
                console.error('微信支付处理失败:', payError);
                showNotification(`支付处理失败: ${payError.message}`, 'error');

                // 支付失败，但订单已创建，提示用户
                showNotification('订单已创建，您可以稍后在订单中心完成支付', 'info');
            }
        } else {
            throw new Error('保存订单失败');
        }
    } catch (error) {
        console.error('提交订单失败:', error);
        showNotification('订单提交失败，请稍后重试', 'error');
        return; // 如果订单提交失败，不清空购物车
    }

    // 发送短信通知
    if (window.smsService) {
        window.smsService.sendSmsNotification(window.smsService.SMS_TYPES.ORDER, order);
    }

    // 清空购物车
    cart = [];
    updateCartDisplay();
    updateCartCount();

    // 关闭模态框
    cartModal.classList.remove('show');
    setTimeout(() => {
        cartModal.style.display = 'none';
    }, 300);
}

// 生成订单ID
function generateOrderId() {
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 1000);
    return `LC-${timestamp}-${random}`;
}

// 移动端调试函数
function addMobileDebugInfo() {
    // 创建调试信息元素
    const debugInfo = document.createElement('div');
    debugInfo.style.position = 'fixed';
    debugInfo.style.bottom = '10px';
    debugInfo.style.left = '10px';
    debugInfo.style.right = '10px';
    debugInfo.style.backgroundColor = 'rgba(0,0,0,0.7)';
    debugInfo.style.color = 'white';
    debugInfo.style.padding = '10px';
    debugInfo.style.fontSize = '12px';
    debugInfo.style.zIndex = '9999';
    debugInfo.style.maxHeight = '150px';
    debugInfo.style.overflow = 'auto';
    debugInfo.id = 'mobile-debug-info';

    // 添加到页面
    document.body.appendChild(debugInfo);

    // 记录设备信息
    const deviceInfo = `
        <p>设备: ${navigator.userAgent}</p>
        <p>屏幕: ${window.innerWidth}x${window.innerHeight}</p>
        <p>商品数量: ${products.length}</p>
        <p>产品列表元素: ${document.getElementById('product-list') ? '存在' : '不存在'}</p>
    `;

    debugInfo.innerHTML = deviceInfo;

    // 添加日志函数
    window.mobileLog = function(message) {
        const logItem = document.createElement('p');
        logItem.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
        debugInfo.appendChild(logItem);
        debugInfo.scrollTop = debugInfo.scrollHeight;
        console.log(message);
    };

    window.mobileLog('移动端调试已启用');
}

// 确保在DOM完全加载后立即执行初始化函数
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM完全加载，开始初始化应用...');

    // 检测是否为移动设备
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    if (isMobile) {
        console.log('检测到移动设备，启用移动端调试');
        // 在页面加载完成后添加调试信息
        window.addEventListener('load', function() {
            addMobileDebugInfo();
        });
    }

    // 首先初始化所有DOM元素引用
    initDOMElements();

    // 检查当前页面是否是产品页面
    const isProductPage = document.getElementById('product-list') !== null;
    console.log('Is product page:', isProductPage);

    // 如果不是产品页面，直接执行初始化并返回
    if (!isProductPage) {
        console.log('当前页面不是产品页面，只执行基本初始化');
        init();
        return;
    }

    // 以下代码只在产品页面执行
    console.log('当前是产品页面，执行产品相关初始化');

    // 立即显示商品
    console.log('立即显示商品数据...');
    displayProducts(products);

    // 然后执行完整的初始化
    console.log('执行完整初始化...');
    init();

    // 额外的保障措施：如果商品列表为空，强制再次显示
    setTimeout(function() {
        if (productList && productList.children.length === 0 && products.length > 0) {
            console.log('商品列表为空，强制再次显示商品...');
            displayProducts(products);

            if (isMobile && window.mobileLog) {
                window.mobileLog(`强制显示商品，数量: ${products.length}`);
            }
        }
    }, 500);

    // 添加窗口加载完成事件，确保所有资源（包括图片）都加载完成后再次检查
    window.addEventListener('load', function() {
        console.log('窗口完全加载，再次检查商品显示...');
        if (productList && productList.children.length === 0 && products.length > 0) {
            console.log('窗口加载后商品列表为空，强制再次显示商品...');
            displayProducts(products);

            if (isMobile && window.mobileLog) {
                window.mobileLog(`窗口加载后强制显示商品，数量: ${products.length}`);
            }
        }
    });
});
