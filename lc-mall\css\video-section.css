.video-section {
    padding: 60px 0;
    background-color: #f0f6ff;
    position: relative;
    overflow: hidden;
}

.video-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,123,255,0.05) 0%, rgba(0,123,255,0) 100%);
    z-index: 0;
}

.video-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
    z-index: 1;
}

.video-section-title {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.video-section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #00c6ff);
    margin: 20px auto 0;
    border-radius: 3px;
}

.video-section-title h2 {
    font-size: 32px;
    color: #1a3c6e;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.video-section-title p {
    font-size: 16px;
    color: #5a6a7e;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

/* 视频列表容器 */
.video-list-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 40px;
    margin-top: 20px;
}

/* 视频项目 */
.video-item {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    background-color: white;
    transition: all 0.3s ease;
    position: relative;
}

.video-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.video-item:nth-child(1) {
    grid-column: span 2;
}

/* 视频占位符 */
.video-placeholder {
    position: relative;
    width: 100%;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;
}

.video-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.7) 100%);
    z-index: 1;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.video-item:hover .video-placeholder::before {
    opacity: 0.6;
}

.video-placeholder img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.video-item:hover .video-placeholder img {
    transform: scale(1.05);
}

/* 视频时长标签 */
.video-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 2;
}

/* 播放按钮 */
.play-button {
    position: absolute;
    width: 70px;
    height: 70px;
    background-color: rgba(0, 123, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

.play-button:hover {
    background-color: #007bff;
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.5);
}

.play-button i {
    color: white;
    font-size: 28px;
    margin-left: 5px; /* Adjust for the play icon */
}

/* 视频信息 */
.video-info {
    padding: 20px;
    background-color: white;
    position: relative;
}

.video-info h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #1a3c6e;
    font-weight: 600;
    line-height: 1.4;
}

.video-info p {
    font-size: 14px;
    color: #5a6a7e;
    margin-bottom: 15px;
    line-height: 1.6;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.video-meta {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #8a9aae;
}

.video-meta .date {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.video-meta .date i,
.video-meta .views i {
    margin-right: 5px;
    font-size: 14px;
}

.video-meta .views {
    display: flex;
    align-items: center;
}

/* 加载指示器 */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    z-index: 3;
}

.loading-indicator.active {
    display: block;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 4px solid #007bff;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 视频模态框 */
.video-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-modal.active {
    display: flex;
    opacity: 1;
}

.video-modal-content {
    position: relative;
    width: 85%;
    max-width: 1000px;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.video-modal.active .video-modal-content {
    transform: scale(1);
}

.video-modal-close {
    position: absolute;
    top: -50px;
    right: 0;
    color: white;
    font-size: 32px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.video-modal-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);
}

.modal-video-container {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.modal-video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
}

.modal-video-info {
    margin-top: 20px;
    color: white;
}

.modal-video-info h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: white;
}

.modal-video-info p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

/* 视频错误消息样式 */
.video-error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    color: white;
    z-index: 10;
    width: 80%;
    max-width: 300px;
}

.video-error-message p {
    margin-bottom: 15px;
    font-size: 16px;
}

.video-error-message button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.video-error-message button:hover {
    background-color: #0056b3;
}

/* 查看更多按钮 */
.view-more-container {
    text-align: center;
    margin-top: 30px;
}

.view-more-btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(90deg, #007bff, #00c6ff);
    color: white;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
    border: none;
    cursor: pointer;
}

.view-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.4);
    background: linear-gradient(90deg, #0062cc, #00a6df);
}

.view-more-btn:active {
    transform: translateY(0);
}

/* 响应式样式 */
@media (max-width: 992px) {
    .video-list-container {
        grid-template-columns: 1fr;
    }

    .video-item:nth-child(1) {
        grid-column: span 1;
    }

    .video-section-title h2 {
        font-size: 28px;
    }
}

@media (max-width: 768px) {
    .video-section {
        padding: 40px 0;
    }

    .video-section-title {
        margin-bottom: 30px;
    }

    .video-section-title h2 {
        font-size: 24px;
    }

    .video-section-title p {
        font-size: 14px;
    }

    .video-list-container {
        gap: 20px;
    }

    .play-button {
        width: 50px;
        height: 50px;
    }

    .play-button i {
        font-size: 20px;
    }

    .video-info {
        padding: 15px;
    }

    .video-info h3 {
        font-size: 18px;
    }

    .video-modal-content {
        width: 95%;
    }

    .video-modal-close {
        top: -40px;
        right: 0;
        font-size: 24px;
    }

    .modal-video-info h3 {
        font-size: 20px;
    }

    .modal-video-info p {
        font-size: 14px;
    }

    .view-more-btn {
        padding: 10px 25px;
        font-size: 14px;
    }
}
