<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 龙驰新材料</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header class="admin-header">
        <h1><img src="../images/logo.png" alt="龙驰新材料"> 龙驰新材料管理后台</h1>
        <nav class="admin-nav">
            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> 控制面板</a>
            <a href="products.html"><i class="fas fa-box"></i> 产品管理</a>
            <a href="orders.html"><i class="fas fa-shopping-cart"></i> 订单管理</a>
            <a href="users.html" class="active"><i class="fas fa-users"></i> 用户管理</a>
            <a href="messages.html"><i class="fas fa-comments"></i> 留言管理</a>
            <a href="sms.html"><i class="fas fa-sms"></i> 短信记录</a>
            <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出</a>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-content">
            <div class="admin-card">
                <h2>用户管理</h2>
                <div class="product-actions">
                    <div class="product-search">
                        <input type="text" id="user-search" placeholder="搜索用户...">
                        <button id="search-btn"><i class="fas fa-search"></i></button>
                    </div>
                    <div class="filter-options">
                        <select id="user-type-filter">
                            <option value="all">所有用户</option>
                            <option value="phone">手机注册用户</option>
                            <option value="wechat">微信用户</option>
                            <option value="qq">QQ用户</option>
                        </select>
                        <button id="export-btn" class="btn" title="导出用户数据"><i class="fas fa-file-export"></i> 导出数据</button>
                    </div>
                </div>
                <div class="stats-summary">
                    <div class="stat-item">
                        <i class="fas fa-users"></i>
                        <span>总用户数: <strong id="total-users">0</strong></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span>手机注册: <strong id="phone-users">0</strong></span>
                    </div>
                    <div class="stat-item">
                        <i class="fab fa-weixin"></i>
                        <span>微信用户: <strong id="wechat-users">0</strong></span>
                    </div>
                    <div class="stat-item">
                        <i class="fab fa-qq"></i>
                        <span>QQ用户: <strong id="qq-users">0</strong></span>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="product-table" id="user-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>头像</th>
                                <th>姓名</th>
                                <th>手机号</th>
                                <th>公司</th>
                                <th>注册时间</th>
                                <th>最后登录</th>
                                <th>注册方式</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="empty-table"><i class="fas fa-spinner fa-spin"></i> 正在加载用户数据...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户详情模态框 -->
    <div id="user-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="modal-title">用户详情</h2>
            <div class="user-details">
                <div class="user-avatar">
                    <img id="user-avatar" src="../images/placeholder/product-placeholder.svg" alt="用户头像">
                </div>
                <div class="user-info">
                    <div class="info-group">
                        <label>用户ID:</label>
                        <span id="user-id"></span>
                    </div>
                    <div class="info-group">
                        <label>姓名:</label>
                        <span id="user-name"></span>
                    </div>
                    <div class="info-group">
                        <label>手机号:</label>
                        <span id="user-phone"></span>
                    </div>
                    <div class="info-group">
                        <label>公司:</label>
                        <span id="user-company"></span>
                    </div>
                    <div class="info-group">
                        <label>职位:</label>
                        <span id="user-position"></span>
                    </div>
                    <div class="info-group">
                        <label>微信:</label>
                        <span id="user-wechat"></span>
                    </div>
                    <div class="info-group">
                        <label>地址:</label>
                        <span id="user-address"></span>
                    </div>
                    <div class="info-group">
                        <label>注册时间:</label>
                        <span id="user-created-at"></span>
                    </div>
                    <div class="info-group">
                        <label>最后登录:</label>
                        <span id="user-last-login"></span>
                    </div>
                    <div class="info-group">
                        <label>注册方式:</label>
                        <span id="user-register-type"></span>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn cancel-btn" id="close-btn">关闭</button>
            </div>
        </div>
    </div>

    <!-- 引入API客户端 -->
    <script src="../js/api-client.js"></script>
    <!-- 引入管理后台脚本 -->
    <script src="admin.js"></script>
    <script src="users.js"></script>
</body>
</html>
