/* 元素球体动画 - 从顶部掉落并吸入化学瓶 */

/* 加载指示器样式 */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    background-color: rgba(240, 248, 255, 0.8);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(100, 180, 255, 0.2);
    transition: opacity 0.5s ease-out;
}

.loading-indicator p {
    margin-top: 15px;
    color: #4a90e2;
    font-size: 16px;
    font-weight: 500;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(100, 180, 255, 0.3);
    border-top: 4px solid #4a90e2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 动画区域样式 */
.element-balls-animation {
    position: relative;
    width: 100%;
    height: 400px;
    background-color: #f0f8ff; /* 非常淡的蓝色背景 */
    background-image:
        linear-gradient(to right, rgba(100, 180, 255, 0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(100, 180, 255, 0.1) 1px, transparent 1px),
        linear-gradient(to right, rgba(100, 180, 255, 0.05) 0.5px, transparent 0.5px),
        linear-gradient(to bottom, rgba(100, 180, 255, 0.05) 0.5px, transparent 0.5px);
    background-size: 20px 20px, 20px 20px, 5px 5px, 5px 5px;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: inset 0 0 20px rgba(100, 180, 255, 0.1); /* 更淡的内阴影效果 */
}

/* 化学瓶相关样式已移除 */

/* 元素球体容器 */
.element-balls-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    pointer-events: none; /* 允许点击穿透 */
}

/* 元素球体基础样式 - 增强视觉效果 */
.element-ball {
    position: absolute;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    color: white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.4), /* 增强外阴影 */
                inset 0 0 20px rgba(255, 255, 255, 0.6); /* 增强内发光 */
    text-shadow: 0 2px 3px rgba(0, 0, 0, 0.6); /* 增强文字阴影 */
    z-index: 5;
    transition: transform 0.1s linear, opacity 0.8s ease-out;
    border: 1px solid rgba(255, 255, 255, 0.3); /* 添加细边框 */
    will-change: transform, opacity; /* 提示浏览器这些属性将会变化，优化性能 */
}

/* 元素球体内部光泽 - 增强光泽效果 */
.element-ball::after {
    content: '';
    position: absolute;
    top: 15%;
    left: 15%;
    width: 40%; /* 增大光泽区域 */
    height: 40%; /* 增大光泽区域 */
    background: rgba(255, 255, 255, 0.7); /* 增强亮度 */
    border-radius: 50%;
    filter: blur(3px); /* 增强模糊效果 */
}

/* 元素球体颜色变体 - 按周期表分组 */
/* 第1周期 */
.element-ball.hydrogen { background: radial-gradient(circle at 30% 30%, #ff5e5e, #d10000); }
.element-ball.helium { background: radial-gradient(circle at 30% 30%, #ffaf5e, #d17000); }

/* 第2周期 */
.element-ball.lithium { background: radial-gradient(circle at 30% 30%, #ff7e7e, #d13030); }
.element-ball.beryllium { background: radial-gradient(circle at 30% 30%, #7eff7e, #30d130); }
.element-ball.boron { background: radial-gradient(circle at 30% 30%, #7e7eff, #3030d1); }
.element-ball.carbon { background: radial-gradient(circle at 30% 30%, #5e5e5e, #000000); }
.element-ball.nitrogen { background: radial-gradient(circle at 30% 30%, #5effb8, #00d15e); }
.element-ball.oxygen { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.fluorine { background: radial-gradient(circle at 30% 30%, #ff9f5e, #d16000); }
.element-ball.neon { background: radial-gradient(circle at 30% 30%, #ff5eaf, #d10070); }

/* 第3周期 */
.element-ball.sodium { background: radial-gradient(circle at 30% 30%, #c15eff, #6000d1); }
.element-ball.magnesium { background: radial-gradient(circle at 30% 30%, #ff8f5e, #d15000); }
.element-ball.aluminum { background: radial-gradient(circle at 30% 30%, #aaaaaa, #777777); }
.element-ball.silicon { background: radial-gradient(circle at 30% 30%, #d1d1d1, #a0a0a0); }
.element-ball.phosphorus { background: radial-gradient(circle at 30% 30%, #ff5eff, #d100d1); }
.element-ball.sulfur { background: radial-gradient(circle at 30% 30%, #ffff5e, #d1d100); }
.element-ball.chlorine { background: radial-gradient(circle at 30% 30%, #5effff, #00d1d1); }
.element-ball.argon { background: radial-gradient(circle at 30% 30%, #af5eff, #7000d1); }

/* 第4周期 */
.element-ball.potassium { background: radial-gradient(circle at 30% 30%, #ff5e9f, #d10060); }
.element-ball.calcium { background: radial-gradient(circle at 30% 30%, #5effc1, #00d160); }
.element-ball.scandium { background: radial-gradient(circle at 30% 30%, #e8e8e8, #b0b0b0); }
.element-ball.titanium { background: radial-gradient(circle at 30% 30%, #bfbfbf, #8a8a8a); }
.element-ball.vanadium { background: radial-gradient(circle at 30% 30%, #a0c5ff, #4a90e2); }
.element-ball.chromium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.manganese { background: radial-gradient(circle at 30% 30%, #9c9c9c, #646464); }
.element-ball.iron { background: radial-gradient(circle at 30% 30%, #8f5eff, #5000d1); }
.element-ball.cobalt { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.nickel { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.copper { background: radial-gradient(circle at 30% 30%, #ff5e8f, #d10050); }
.element-ball.zinc { background: radial-gradient(circle at 30% 30%, #5e8fff, #0050d1); }
.element-ball.gallium { background: radial-gradient(circle at 30% 30%, #7e7eff, #3030d1); }
.element-ball.germanium { background: radial-gradient(circle at 30% 30%, #a0a0a0, #707070); }
.element-ball.arsenic { background: radial-gradient(circle at 30% 30%, #c15eff, #6000d1); }
.element-ball.selenium { background: radial-gradient(circle at 30% 30%, #ff9f5e, #d16000); }
.element-ball.bromine { background: radial-gradient(circle at 30% 30%, #ff5e5e, #d10000); }
.element-ball.krypton { background: radial-gradient(circle at 30% 30%, #5effff, #00d1d1); }

/* 第5周期 */
.element-ball.rubidium { background: radial-gradient(circle at 30% 30%, #ff7e7e, #d13030); }
.element-ball.strontium { background: radial-gradient(circle at 30% 30%, #7eff7e, #30d130); }
.element-ball.yttrium { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.zirconium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.niobium { background: radial-gradient(circle at 30% 30%, #a0c5ff, #4a90e2); }
.element-ball.molybdenum { background: radial-gradient(circle at 30% 30%, #9c9c9c, #646464); }
.element-ball.technetium { background: radial-gradient(circle at 30% 30%, #ff5e9f, #d10060); }
.element-ball.ruthenium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.rhodium { background: radial-gradient(circle at 30% 30%, #e8e8e8, #b0b0b0); }
.element-ball.palladium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.silver { background: radial-gradient(circle at 30% 30%, #cfcfcf, #909090); }
.element-ball.cadmium { background: radial-gradient(circle at 30% 30%, #5e8fff, #0050d1); }
.element-ball.indium { background: radial-gradient(circle at 30% 30%, #7e7eff, #3030d1); }
.element-ball.tin { background: radial-gradient(circle at 30% 30%, #a0a0a0, #707070); }
.element-ball.antimony { background: radial-gradient(circle at 30% 30%, #c15eff, #6000d1); }
.element-ball.tellurium { background: radial-gradient(circle at 30% 30%, #ff9f5e, #d16000); }
.element-ball.iodine { background: radial-gradient(circle at 30% 30%, #ff5eff, #d100d1); }
.element-ball.xenon { background: radial-gradient(circle at 30% 30%, #5effff, #00d1d1); }

/* 第6周期 */
.element-ball.cesium { background: radial-gradient(circle at 30% 30%, #ff7e7e, #d13030); }
.element-ball.barium { background: radial-gradient(circle at 30% 30%, #7eff7e, #30d130); }
.element-ball.lanthanum { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.cerium { background: radial-gradient(circle at 30% 30%, #ffcf5e, #d19f00); }
.element-ball.praseodymium { background: radial-gradient(circle at 30% 30%, #ff9f5e, #d16000); }
.element-ball.neodymium { background: radial-gradient(circle at 30% 30%, #c15eff, #6000d1); }
.element-ball.promethium { background: radial-gradient(circle at 30% 30%, #ff5eff, #d100d1); }
.element-ball.samarium { background: radial-gradient(circle at 30% 30%, #ff5e9f, #d10060); }
.element-ball.europium { background: radial-gradient(circle at 30% 30%, #5effc1, #00d160); }
.element-ball.gadolinium { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.terbium { background: radial-gradient(circle at 30% 30%, #7eff7e, #30d130); }
.element-ball.dysprosium { background: radial-gradient(circle at 30% 30%, #ffcf5e, #d19f00); }
.element-ball.holmium { background: radial-gradient(circle at 30% 30%, #ff9f5e, #d16000); }
.element-ball.erbium { background: radial-gradient(circle at 30% 30%, #ff5e5e, #d10000); }
.element-ball.thulium { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.ytterbium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.lutetium { background: radial-gradient(circle at 30% 30%, #7eff7e, #30d130); }
.element-ball.hafnium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.tantalum { background: radial-gradient(circle at 30% 30%, #9c9c9c, #646464); }
.element-ball.tungsten { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.rhenium { background: radial-gradient(circle at 30% 30%, #9c9c9c, #646464); }
.element-ball.osmium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.iridium { background: radial-gradient(circle at 30% 30%, #e8e8e8, #b0b0b0); }
.element-ball.platinum { background: radial-gradient(circle at 30% 30%, #e8e8e8, #b0b0b0); }
.element-ball.gold { background: radial-gradient(circle at 30% 30%, #ffcf5e, #d19f00); }
.element-ball.mercury { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.thallium { background: radial-gradient(circle at 30% 30%, #7e7eff, #3030d1); }
.element-ball.lead { background: radial-gradient(circle at 30% 30%, #9c9c9c, #646464); }
.element-ball.bismuth { background: radial-gradient(circle at 30% 30%, #ff5e9f, #d10060); }
.element-ball.polonium { background: radial-gradient(circle at 30% 30%, #ff5e5e, #d10000); }
.element-ball.astatine { background: radial-gradient(circle at 30% 30%, #ff5eff, #d100d1); }
.element-ball.radon { background: radial-gradient(circle at 30% 30%, #5effff, #00d1d1); }

/* 第7周期 */
.element-ball.francium { background: radial-gradient(circle at 30% 30%, #ff7e7e, #d13030); }
.element-ball.radium { background: radial-gradient(circle at 30% 30%, #7eff7e, #30d130); }
.element-ball.actinium { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.thorium { background: radial-gradient(circle at 30% 30%, #9c9c9c, #646464); }
.element-ball.protactinium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.uranium { background: radial-gradient(circle at 30% 30%, #7eff7e, #30d130); }
.element-ball.neptunium { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.plutonium { background: radial-gradient(circle at 30% 30%, #ff5e5e, #d10000); }
.element-ball.americium { background: radial-gradient(circle at 30% 30%, #ff5e9f, #d10060); }
.element-ball.curium { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.berkelium { background: radial-gradient(circle at 30% 30%, #7eff7e, #30d130); }
.element-ball.californium { background: radial-gradient(circle at 30% 30%, #ffcf5e, #d19f00); }
.element-ball.einsteinium { background: radial-gradient(circle at 30% 30%, #ff5eff, #d100d1); }
.element-ball.fermium { background: radial-gradient(circle at 30% 30%, #5e9fff, #0055d1); }
.element-ball.mendelevium { background: radial-gradient(circle at 30% 30%, #ff5e9f, #d10060); }
.element-ball.nobelium { background: radial-gradient(circle at 30% 30%, #7eff7e, #30d130); }
.element-ball.lawrencium { background: radial-gradient(circle at 30% 30%, #ff5e5e, #d10000); }
.element-ball.rutherfordium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.dubnium { background: radial-gradient(circle at 30% 30%, #9c9c9c, #646464); }
.element-ball.seaborgium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.bohrium { background: radial-gradient(circle at 30% 30%, #9c9c9c, #646464); }
.element-ball.hassium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.meitnerium { background: radial-gradient(circle at 30% 30%, #e8e8e8, #b0b0b0); }
.element-ball.darmstadtium { background: radial-gradient(circle at 30% 30%, #e8e8e8, #b0b0b0); }
.element-ball.roentgenium { background: radial-gradient(circle at 30% 30%, #ffcf5e, #d19f00); }
.element-ball.copernicium { background: radial-gradient(circle at 30% 30%, #b3b3b3, #7a7a7a); }
.element-ball.nihonium { background: radial-gradient(circle at 30% 30%, #7e7eff, #3030d1); }
.element-ball.flerovium { background: radial-gradient(circle at 30% 30%, #9c9c9c, #646464); }
.element-ball.moscovium { background: radial-gradient(circle at 30% 30%, #ff5e9f, #d10060); }
.element-ball.livermorium { background: radial-gradient(circle at 30% 30%, #ff5e5e, #d10000); }
.element-ball.tennessine { background: radial-gradient(circle at 30% 30%, #ff5eff, #d100d1); }
.element-ball.oganesson { background: radial-gradient(circle at 30% 30%, #5effff, #00d1d1); }

/* 默认颜色 - 确保所有元素都有颜色 */
.element-ball {
    background: radial-gradient(circle at 30% 30%, #7e7e7e, #4a4a4a);
}

/* 化学瓶中的气泡 */
.flask-bubble {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    animation: bubble-rise linear infinite;
}

.flask-bubble::after {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 40%;
    height: 40%;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
}

/* 气泡上升动画 */
@keyframes bubble-rise {
    0% {
        transform: translateY(0) translateX(0);
        opacity: 0.7;
    }
    100% {
        transform: translateY(-100px) translateX(10px);
        opacity: 0;
    }
}

/* 元素球体碰撞效果 - 已移除 */

/* 吸入效果动画 - 使用绝对位置 */
@keyframes suck-in {
    0% {
        /* 保持当前位置 */
        opacity: 1;
    }
    60% {
        /* 使用贝塞尔曲线创建抛物线效果 */
        transform: translate3d(calc((var(--target-x) - var(--current-x)) * 0.7),
                              calc((var(--target-y) - var(--current-y)) * 0.7 - 20px), 0)
                   scale(0.8);
        opacity: 0.9;
    }
    90% {
        transform: translate3d(calc(var(--target-x) - var(--current-x)),
                              calc(var(--target-y) - var(--current-y)), 0)
                   scale(0.3);
        opacity: 0.5;
    }
    100% {
        transform: translate3d(calc(var(--target-x) - var(--current-x)),
                              calc(var(--target-y) - var(--current-y)), 0)
                   scale(0);
        opacity: 0;
    }
}

/* 球体消失动画 */
@keyframes ball-disappear {
    0% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 0 25px rgba(255, 255, 255, 0.9);
    }
    50% {
        transform: scale(0.6);
        opacity: 0.7;
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
    }
    100% {
        transform: scale(0.2);
        opacity: 0;
        box-shadow: 0 0 5px rgba(255, 255, 255, 0);
    }
}

/* 球体闪光效果 */
@keyframes ball-flash {
    0% {
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.6);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.9), inset 0 0 30px rgba(255, 255, 255, 0.9);
    }
    100% {
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.6);
    }
}
