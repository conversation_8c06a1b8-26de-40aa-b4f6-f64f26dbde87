#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高性能Web服务器 - 使用aiohttp提供异步处理能力
支持静态文件服务、缓存控制和CORS
"""

import os
import sys
import json
import argparse
import logging
import mimetypes
import asyncio
import base64
from pathlib import Path
from datetime import datetime, timedelta

# 导入产品介绍API处理程序
try:
    from product_intro_api_handler import ProductIntroHandler
except ImportError:
    print("警告: 未找到产品介绍API处理程序，相关功能将不可用")
    ProductIntroHandler = None

try:
    from aiohttp import web
    import aiohttp_cors
except ImportError:
    print("请先安装必要的依赖: pip install aiohttp aiohttp_cors")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("server.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 确保所有文件类型都有正确的MIME类型
mimetypes.add_type("application/javascript", ".js")
mimetypes.add_type("text/css", ".css")
mimetypes.add_type("image/svg+xml", ".svg")

# 缓存控制 - 静态资源缓存时间（秒）
CACHE_MAX_AGE = 3600  # 1小时

class HighPerformanceServer:
    def __init__(self, host="0.0.0.0", port=8000, root_dir=None):
        self.host = host
        self.port = port
        self.root_dir = Path(root_dir or os.getcwd())
        self.app = web.Application(client_max_size=20*1024*1024)  # 允许上传最大20MB
        self.setup_routes()
        self.setup_cors()
        self.setup_static_files()

        # 数据文件路径
        self.data_dir = self.root_dir / "data"
        self.product_intro_file = self.data_dir / "product-intro.json"
        self.images_dir = self.root_dir / "images" / "products"

        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.images_dir, exist_ok=True)

        # 初始化产品介绍API处理程序
        if ProductIntroHandler:
            self.product_intro_handler = ProductIntroHandler(self.root_dir)
        else:
            self.product_intro_handler = None

        # 请求计数器和限流
        self.request_count = 0
        self.last_reset = datetime.now()
        self.request_limit = 1000  # 每分钟最大请求数

    def setup_routes(self):
        self.app.router.add_get('/', self.handle_index)

        # API路由
        self.app.router.add_get('/api/product-intro', self.handle_product_intro_get)
        self.app.router.add_post('/api/product-intro/category', self.handle_product_category_update)
        self.app.router.add_post('/api/product-intro/table', self.handle_product_table_update)
        self.app.router.add_post('/api/product-intro/upload-image', self.handle_product_image_upload)

        # 静态文件路由 - 必须放在最后
        self.app.router.add_get('/{path:.*}', self.handle_static)

    def setup_cors(self):
        # 设置CORS，允许所有来源的请求
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                max_age=3600,
            )
        })

        # 为所有路由应用CORS设置
        for route in list(self.app.router.routes()):
            cors.add(route)

    def setup_static_files(self):
        # 静态文件处理已经在handle_static中实现
        pass

    async def rate_limit_check(self, request):
        """请求限流检查"""
        self.request_count += 1

        # 每分钟重置计数器
        now = datetime.now()
        if (now - self.last_reset).total_seconds() > 60:
            self.request_count = 1
            self.last_reset = now

        # 如果请求数超过限制，返回429错误
        if self.request_count > self.request_limit:
            return web.Response(
                status=429,
                text="Too Many Requests - Please try again later",
                content_type="text/plain"
            )

        return None

    async def handle_index(self, request):
        """处理根路径请求，重定向到index.html"""
        return await self.handle_static(web.Request.clone(request, match_info={'path': 'index.html'}))

    # 产品介绍API处理函数
    async def handle_product_intro_get(self, request):
        """获取产品介绍数据"""
        try:
            # 如果有产品介绍API处理程序，使用它
            if self.product_intro_handler:
                result = self.product_intro_handler.get_product_intro()
                if result["success"]:
                    return web.json_response(result["data"])
                else:
                    return web.Response(status=500, text=f"Internal Server Error: {result['error']}")

            # 否则使用内置处理逻辑
            if self.product_intro_file.exists():
                with open(self.product_intro_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = {"categories": {}, "tables": {}}

            return web.json_response(data)
        except Exception as e:
            logger.error(f"获取产品介绍数据时出错: {str(e)}", exc_info=True)
            return web.Response(status=500, text=f"Internal Server Error: {str(e)}")

    async def handle_product_category_update(self, request):
        """更新产品类别数据"""
        try:
            data = await request.json()

            category_id = data.get('categoryId')
            category_data = data.get('data', {})

            # 如果有产品介绍API处理程序，使用它
            if self.product_intro_handler:
                result = self.product_intro_handler.update_category(category_id, category_data)
                return web.json_response(result)

            # 否则使用内置处理逻辑
            if not category_id or not category_data:
                return web.Response(status=400, text="Missing required fields")

            # 读取现有数据
            if self.product_intro_file.exists():
                with open(self.product_intro_file, 'r', encoding='utf-8') as f:
                    product_data = json.load(f)
            else:
                product_data = {"categories": {}, "tables": {}}

            # 更新类别数据
            if "categories" not in product_data:
                product_data["categories"] = {}

            product_data["categories"][category_id] = category_data

            # 保存数据
            with open(self.product_intro_file, 'w', encoding='utf-8') as f:
                json.dump(product_data, f, ensure_ascii=False, indent=2)

            return web.json_response({"success": True})
        except Exception as e:
            logger.error(f"更新产品类别数据时出错: {str(e)}", exc_info=True)
            return web.Response(status=500, text=f"Internal Server Error: {str(e)}")

    async def handle_product_table_update(self, request):
        """更新产品表格数据"""
        try:
            data = await request.json()

            detail_id = data.get('detailId')
            table_data = data.get('data', [])

            # 如果有产品介绍API处理程序，使用它
            if self.product_intro_handler:
                result = self.product_intro_handler.update_table(detail_id, table_data)
                return web.json_response(result)

            # 否则使用内置处理逻辑
            if not detail_id or not isinstance(table_data, list):
                return web.Response(status=400, text="Missing required fields")

            # 读取现有数据
            if self.product_intro_file.exists():
                with open(self.product_intro_file, 'r', encoding='utf-8') as f:
                    product_data = json.load(f)
            else:
                product_data = {"categories": {}, "tables": {}}

            # 更新表格数据
            if "tables" not in product_data:
                product_data["tables"] = {}

            product_data["tables"][detail_id] = table_data

            # 保存数据
            with open(self.product_intro_file, 'w', encoding='utf-8') as f:
                json.dump(product_data, f, ensure_ascii=False, indent=2)

            return web.json_response({"success": True})
        except Exception as e:
            logger.error(f"更新产品表格数据时出错: {str(e)}", exc_info=True)
            return web.Response(status=500, text=f"Internal Server Error: {str(e)}")

    async def handle_product_image_upload(self, request):
        """上传产品图片"""
        try:
            data = await request.json()

            image_data = data.get('imageData', '')
            file_name = data.get('fileName', '')

            # 如果有产品介绍API处理程序，使用它
            if self.product_intro_handler:
                result = self.product_intro_handler.upload_image(image_data, file_name)
                return web.json_response(result)

            # 否则使用内置处理逻辑
            if not image_data or not file_name:
                return web.Response(status=400, text="Missing required fields")

            # 解码Base64图片数据
            if ',' in image_data:
                image_data = image_data.split(',')[1]

            image_binary = base64.b64decode(image_data)

            # 保存图片
            file_path = self.images_dir / file_name
            with open(file_path, 'wb') as f:
                f.write(image_binary)

            # 返回图片URL
            image_url = f"/images/products/{file_name}"

            return web.json_response({"success": True, "imageUrl": image_url})
        except Exception as e:
            logger.error(f"上传产品图片时出错: {str(e)}", exc_info=True)
            return web.Response(status=500, text=f"Internal Server Error: {str(e)}")

    async def handle_static(self, request):
        """处理静态文件请求"""
        # 请求限流检查
        rate_limit_response = await self.rate_limit_check(request)
        if rate_limit_response:
            return rate_limit_response

        path = request.match_info.get('path', '')
        file_path = self.root_dir / path

        try:
            # 安全检查：确保请求的文件在根目录下
            file_path = file_path.resolve()
            if not file_path.is_relative_to(self.root_dir):
                logger.warning(f"安全警告: 尝试访问根目录外的文件: {path}")
                return web.Response(status=403, text="Forbidden")

            # 如果是目录，尝试提供index.html
            if file_path.is_dir():
                file_path = file_path / "index.html"

            # 检查文件是否存在
            if not file_path.exists() or not file_path.is_file():
                logger.warning(f"文件未找到: {file_path}")
                return web.Response(status=404, text=f"File not found: {path}")

            # 获取文件的MIME类型
            content_type = mimetypes.guess_type(str(file_path))[0] or 'application/octet-stream'

            # 读取文件内容
            with open(file_path, 'rb') as f:
                content = f.read()

            # 创建响应
            response = web.Response(body=content, content_type=content_type)

            # 设置缓存控制
            if any(file_path.suffix == ext for ext in ['.html', '.htm', '.json']):
                # HTML和JSON文件不缓存
                response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
            else:
                # 静态资源使用缓存
                cache_time = datetime.now(datetime.timezone.utc) + timedelta(seconds=CACHE_MAX_AGE)
                response.headers['Cache-Control'] = f'public, max-age={CACHE_MAX_AGE}'
                response.headers['Expires'] = cache_time.strftime('%a, %d %b %Y %H:%M:%S GMT')

            logger.info(f"提供文件: {path} ({content_type})")
            return response

        except Exception as e:
            logger.error(f"处理请求 {path} 时出错: {str(e)}", exc_info=True)
            return web.Response(status=500, text=f"Internal Server Error: {str(e)}")

    async def start(self):
        """启动服务器"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)

        logger.info(f"启动高性能服务器 - http://{self.host}:{self.port}/")
        logger.info(f"根目录: {self.root_dir}")

        await site.start()

        # 保持服务器运行
        while True:
            await asyncio.sleep(3600)  # 每小时检查一次

    def run(self):
        """运行服务器"""
        try:
            asyncio.run(self.start())
        except KeyboardInterrupt:
            logger.info("服务器已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="高性能Web服务器")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8007, help="服务器端口")
    parser.add_argument("--root", help="网站根目录")
    args = parser.parse_args()

    server = HighPerformanceServer(host=args.host, port=args.port, root_dir=args.root)
    server.run()

if __name__ == "__main__":
    main()
