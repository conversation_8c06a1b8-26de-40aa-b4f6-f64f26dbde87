/* 购物车数量显示修复 */

/* 确保购物车数量正确显示 */
.cart-count {
    min-width: 24px !important;
    height: 24px !important;
    padding: 0 5px !important;
    border-radius: 50% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    font-weight: 700 !important;
    z-index: 10000 !important;
    box-sizing: border-box !important;
}

/* 顶部导航栏购物车数量 */
#cart-count {
    min-width: 20px !important;
    height: 20px !important;
    padding: 0 4px !important;
    border-radius: 50% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    font-weight: 600 !important;
    z-index: 101 !important;
    box-sizing: border-box !important;
}

/* 浮动购物车数量 */
#floating-cart-count {
    min-width: 24px !important;
    height: 24px !important;
    padding: 0 5px !important;
    border-radius: 50% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    font-weight: 700 !important;
    z-index: 10000 !important;
    box-sizing: border-box !important;
    background-color: white !important;
    color: var(--accent-color) !important;
    position: absolute !important;
    top: -5px !important;
    right: -5px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* 确保购物车图标正确显示 */
.floating-cart {
    position: fixed !important;
    right: 20px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 9999 !important;
    width: 70px !important;
    height: 70px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    overflow: visible !important; /* 确保数量标签可见 */
}

/* 响应式调整 */
@media (max-width: 768px) {
    .floating-cart {
        width: 60px !important;
        height: 60px !important;
    }
    
    #floating-cart-count {
        min-width: 22px !important;
        height: 22px !important;
        font-size: 12px !important;
    }
}
