/* 用户相关样式 */

/* 登录和注册模态框 */
#login-modal,
#register-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(15, 23, 42, 0.75);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

#login-modal.show,
#register-modal.show {
    opacity: 1;
}

#login-modal .modal-content,
#register-modal .modal-content {
    background-color: var(--bg-color);
    margin: 5% auto;
    padding: 2.5rem;
    border-radius: var(--radius-md);
    max-width: 500px;
    position: relative;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

#login-modal h2,
#register-modal h2 {
    color: var(--secondary-color);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
}

/* 用户状态区域 */
.user-status {
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.user-status .btn {
    margin-left: 8px;
    font-size: 14px;
    padding: 6px 12px;
}

.user-status .btn-sm {
    padding: 4px 10px;
    font-size: 12px;
}

.user-status .btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.user-status .btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.user-welcome {
    font-size: 14px;
    margin-right: 10px;
    color: white !important; /* 改为白色 */
    font-weight: 500;
    display: flex;
    align-items: center;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2); /* 添加文字阴影增强可读性 */
}

.user-welcome i {
    margin-right: 5px;
    font-size: 16px;
    color: white !important; /* 图标也改为白色 */
}

/* 用户表单样式 */
.user-form {
    padding: 15px 0;
}

.user-form .form-group {
    margin-bottom: 15px;
    width: 100%;
}

.user-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-color);
}

.user-form .required {
    color: var(--danger-color);
    margin-left: 2px;
}

.user-form textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
}

.user-form textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.user-form input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.user-form input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.user-form button,
.btn-primary {
    width: 100%;
    padding: 10px;
    margin-top: 10px;
    background-color: var(--primary-color);
    color: white !important; /* 确保文字颜色始终为白色 */
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    display: block; /* 确保按钮正确显示 */
}

.user-form button:hover,
.btn-primary:hover {
    background-color: var(--primary-dark);
    color: white !important;
}

.form-switch {
    text-align: center;
    margin-top: 15px;
    font-size: 14px;
    color: #666;
}

.form-switch a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.form-switch a:hover {
    text-decoration: underline;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .user-status {
        margin-left: 10px;
    }

    .user-status .btn {
        padding: 4px 8px;
        font-size: 12px;
    }

    .user-welcome {
        font-size: 12px;
        max-width: 80px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* 移动设备上的模态框样式 */
    #login-modal .modal-content,
    #register-modal .modal-content {
        width: 90%;
        margin: 10% auto;
        padding: 1.5rem;
    }

    /* 确保按钮在移动设备上正确显示 */
    .user-form button,
    .btn-primary {
        width: 100%;
        padding: 12px;
        font-size: 16px;
        margin-top: 15px;
    }
}
