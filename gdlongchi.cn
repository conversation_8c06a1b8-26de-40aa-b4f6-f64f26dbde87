server {
    listen 80;
    server_name gdlongchi.cn ************;

    location = / {
        proxy_pass http://127.0.0.1:9091/lc-mall/index.html;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /css/ {
        proxy_pass http://127.0.0.1:9091/lc-mall/css/;
        proxy_set_header Host $host;
    }
    location /images/ {
        proxy_pass http://127.0.0.1:9091/lc-mall/images/;
        proxy_set_header Host $host;
    }
    location /js/ {
        proxy_pass http://127.0.0.1:9091/lc-mall/js/;
        proxy_set_header Host $host;
    }

    # 正确的API代理
    location /api/ {
        proxy_pass http://127.0.0.1:9091/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /lc-mall/ {
        proxy_pass http://127.0.0.1:9091/lc-mall/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}