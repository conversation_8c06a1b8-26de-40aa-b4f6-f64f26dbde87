/**
 * 图片压缩工具
 * 用于在上传前压缩图片，减少数据传输量
 */

const ImageCompressor = {
    /**
     * 压缩图片文件
     * @param {File} file - 图片文件
     * @param {Object} options - 压缩选项
     * @param {number} options.maxWidth - 最大宽度 (默认 1200px)
     * @param {number} options.maxHeight - 最大高度 (默认 1200px)
     * @param {number} options.quality - 图片质量 (0-1, 默认 0.8)
     * @param {number} options.maxSizeMB - 最大文件大小 (MB, 默认 1MB)
     * @param {Function} options.onProgress - 进度回调函数 (0-100)
     * @returns {Promise<string>} - 压缩后的图片 Data URL
     */
    compressFile: function(file, options = {}) {
        return new Promise((resolve, reject) => {
            // 检查文件类型
            if (!file || !file.type.startsWith('image/')) {
                reject(new Error('不是有效的图片文件'));
                return;
            }

            // 设置默认选项
            const settings = {
                maxWidth: options.maxWidth || 1200,
                maxHeight: options.maxHeight || 1200,
                quality: options.quality || 0.8,
                maxSizeMB: options.maxSizeMB || 1,
                onProgress: options.onProgress || function() {}
            };

            // 进度回调
            settings.onProgress(10);

            // 将文件转换为 Data URL
            const reader = new FileReader();
            reader.onload = (event) => {
                settings.onProgress(30);
                this.compressImage(event.target.result, settings)
                    .then(resolve)
                    .catch(reject);
            };
            reader.onerror = () => {
                reject(new Error('读取文件失败'));
            };
            reader.readAsDataURL(file);
        });
    },

    /**
     * 压缩图片 Data URL
     * @param {string} dataUrl - 图片 Data URL
     * @param {Object} options - 压缩选项
     * @returns {Promise<string>} - 压缩后的图片 Data URL
     */
    compressImage: function(dataUrl, options = {}) {
        return new Promise((resolve, reject) => {
            // 设置默认选项
            const settings = {
                maxWidth: options.maxWidth || 1200,
                maxHeight: options.maxHeight || 1200,
                quality: options.quality || 0.8,
                maxSizeMB: options.maxSizeMB || 1,
                onProgress: options.onProgress || function() {}
            };

            try {
                // 创建图片对象
                const img = new Image();
                img.onload = () => {
                    settings.onProgress(50);

                    // 创建 canvas
                    const canvas = document.createElement('canvas');
                    let width = img.width;
                    let height = img.height;

                    // 计算最大尺寸，保持宽高比
                    if (width > height) {
                        if (width > settings.maxWidth) {
                            height = Math.round(height * (settings.maxWidth / width));
                            width = settings.maxWidth;
                        }
                    } else {
                        if (height > settings.maxHeight) {
                            width = Math.round(width * (settings.maxHeight / height));
                            height = settings.maxHeight;
                        }
                    }

                    // 设置 canvas 尺寸
                    canvas.width = width;
                    canvas.height = height;

                    // 绘制图片到 canvas
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, width, height);

                    settings.onProgress(70);

                    // 压缩图片
                    let quality = settings.quality;
                    let compressedDataUrl = canvas.toDataURL('image/jpeg', quality);

                    // 计算大小 (MB)
                    let sizeInMB = compressedDataUrl.length / (1024 * 1024);

                    // 如果大小仍然超过限制，继续降低质量
                    let compressionAttempts = 0;
                    const MAX_ATTEMPTS = 5;

                    const compressStep = () => {
                        if (sizeInMB <= settings.maxSizeMB || quality <= 0.1 || compressionAttempts >= MAX_ATTEMPTS) {
                            // 压缩完成
                            settings.onProgress(100);
                            console.log(`图片压缩完成: ${sizeInMB.toFixed(2)}MB, 质量: ${quality.toFixed(1)}`);
                            resolve(compressedDataUrl);
                            return;
                        }

                        // 继续压缩
                        compressionAttempts++;
                        quality -= 0.1;
                        quality = Math.max(0.1, quality); // 确保质量不低于 0.1
                        
                        compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
                        sizeInMB = compressedDataUrl.length / (1024 * 1024);
                        
                        // 更新进度
                        settings.onProgress(70 + (compressionAttempts / MAX_ATTEMPTS) * 30);
                        
                        // 继续下一步压缩
                        setTimeout(compressStep, 0);
                    };

                    // 开始压缩
                    compressStep();
                };

                img.onerror = () => {
                    reject(new Error('图片加载失败'));
                };

                // 设置图片源
                img.src = dataUrl;
            } catch (error) {
                reject(error);
            }
        });
    },

    /**
     * 压缩 JSON 数据
     * @param {Object} data - 要压缩的数据对象
     * @param {Object} options - 压缩选项
     * @param {Array<string>} options.imageFields - 包含图片数据的字段名数组
     * @param {number} options.maxSizeMB - 每个图片的最大大小 (MB)
     * @param {Function} options.onProgress - 进度回调函数 (0-100)
     * @returns {Promise<Object>} - 压缩后的数据对象
     */
    compressJsonData: async function(data, options = {}) {
        // 设置默认选项
        const settings = {
            imageFields: options.imageFields || ['image'],
            maxSizeMB: options.maxSizeMB || 1,
            onProgress: options.onProgress || function() {}
        };

        // 创建数据副本，避免修改原始数据
        const compressedData = JSON.parse(JSON.stringify(data));
        
        // 查找并压缩所有图片字段
        const imageFields = settings.imageFields.filter(field => 
            compressedData[field] && 
            typeof compressedData[field] === 'string' && 
            compressedData[field].startsWith('data:')
        );
        
        // 如果没有图片字段需要压缩，直接返回
        if (imageFields.length === 0) {
            settings.onProgress(100);
            return compressedData;
        }
        
        // 压缩每个图片字段
        for (let i = 0; i < imageFields.length; i++) {
            const field = imageFields[i];
            const startProgress = (i / imageFields.length) * 100;
            const endProgress = ((i + 1) / imageFields.length) * 100;
            
            // 更新进度
            const updateProgress = (progress) => {
                settings.onProgress(startProgress + (progress / 100) * (endProgress - startProgress));
            };
            
            // 压缩图片
            updateProgress(0);
            try {
                compressedData[field] = await this.compressImage(compressedData[field], {
                    maxSizeMB: settings.maxSizeMB,
                    onProgress: updateProgress
                });
            } catch (error) {
                console.error(`压缩字段 ${field} 失败:`, error);
                // 继续处理其他字段
            }
        }
        
        // 完成
        settings.onProgress(100);
        return compressedData;
    }
};

// 导出压缩工具
window.ImageCompressor = ImageCompressor;
