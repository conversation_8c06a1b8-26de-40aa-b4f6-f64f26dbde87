/**
 * iPhone设备专用优化样式
 * 解决iPhone设备上的特定显示问题
 */

/* iPhone设备通用样式 */
.iphone-device {
    /* 使用更安全的字体堆栈 */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    
    /* 修复iOS上的滚动问题 */
    -webkit-overflow-scrolling: touch;
}

/* 使用CSS变量定义视口高度 */
:root {
    --vh: 1vh;
}

/* 使用计算的视口高度 */
.iphone-device .full-height {
    height: calc(100 * var(--vh));
}

/* 修复iOS上的固定元素问题 */
.iphone-device .floating-cart,
.iphone-device .back-to-top,
.iphone-device .modal {
    transform: translateZ(0);
}

/* 修复iOS上的输入框问题 */
.iphone-device input,
.iphone-device textarea,
.iphone-device select {
    -webkit-appearance: none;
    border-radius: 0;
}

/* 修复iOS上的按钮点击延迟 */
.iphone-device a,
.iphone-device button,
.iphone-device [role="button"],
.iphone-device input[type="button"],
.iphone-device input[type="submit"],
.iphone-device input[type="reset"] {
    cursor: pointer;
    touch-action: manipulation;
}

/* 产品列表优化 */
.iphone-device #product-list {
    /* 确保网格布局在iPhone上正确显示 */
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    width: 100%;
    padding: 10px;
}

/* 小屏幕iPhone使用单列布局 */
@media (max-width: 360px) {
    .iphone-device #product-list {
        grid-template-columns: 1fr;
    }
}

/* 产品卡片优化 */
.iphone-device .product-card {
    /* 确保卡片有固定的高度和正确的布局 */
    height: auto !important;
    min-height: 250px;
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    background-color: #fff;
}

/* 产品卡片头部（图片区域）优化 */
.iphone-device .product-card-header {
    height: 120px;
    overflow: hidden;
    position: relative;
}

.iphone-device .product-card-header img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* 产品信息区域优化 */
.iphone-device .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
}

.iphone-device .product-title-row {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 5px;
}

.iphone-device .product-title-row h3 {
    font-size: 14px;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.iphone-device .product-stock {
    font-size: 12px;
    margin-bottom: 5px;
}

.iphone-device .product-meta {
    font-size: 12px;
    margin-bottom: 5px;
}

.iphone-device .product-info p {
    font-size: 12px;
    margin-bottom: 10px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 价格和购物车按钮行优化 */
.iphone-device .price-cart-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.iphone-device .price {
    font-size: 16px;
    font-weight: bold;
    color: #ff6b6b;
}

.iphone-device .add-to-cart {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 4px;
    background-color: #4ecdc4;
    color: white;
    border: none;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

/* 图片加载状态 */
.iphone-device .product-card:not(.image-loaded) .product-card-header {
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.iphone-device .product-card:not(.image-loaded) .product-card-header::after {
    content: '加载中...';
    color: #999;
    font-size: 12px;
}

/* 模态框优化 */
.iphone-device .modal-content {
    width: 95%;
    max-width: 100%;
    margin: 5% auto;
    border-radius: 8px;
    max-height: 90vh;
}

.iphone-device .product-modal-content {
    flex-direction: column;
}

.iphone-device .product-modal-image {
    width: 100%;
    height: auto;
    max-height: 200px;
}

.iphone-device .product-modal-info {
    width: 100%;
    padding: 10px 0;
}

/* 购物车模态框优化 */
.iphone-device .cart-item {
    padding: 10px;
    margin-bottom: 10px;
}

.iphone-device .cart-item-image {
    width: 60px;
    height: 60px;
}

.iphone-device .cart-item-details {
    padding-left: 10px;
}

.iphone-device .cart-item-actions {
    margin-left: auto;
}

/* 触摸反馈优化 */
.iphone-device .product-card:active,
.iphone-device .btn:active,
.iphone-device .category-link:active,
.iphone-device .floating-cart:active {
    transform: scale(0.97);
    transition: transform 0.1s ease;
}

/* 滚动优化 */
.iphone-device .category-nav,
.iphone-device .modal-content {
    -webkit-overflow-scrolling: touch;
}

/* 修复Safari上的一些特定问题 */
@supports (-webkit-touch-callout: none) {
    /* 仅在Safari上应用的样式 */
    .iphone-device .product-card {
        /* 修复Safari上的flex布局问题 */
        display: flex;
        flex-direction: column;
    }
    
    .iphone-device .product-info {
        /* 确保flex子元素在Safari上正确显示 */
        flex: 1 0 auto;
    }
}
