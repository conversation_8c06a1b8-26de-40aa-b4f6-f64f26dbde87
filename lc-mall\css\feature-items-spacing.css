/* 特色项目文本排版优化 */

/* 特色项目容器 */
.about-features {
    margin-top: 50px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

/* 特色项目卡片 */
.feature-item {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #f0f0f0;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 特色项目图标 */
.feature-icon {
    width: 70px;
    height: 70px;
    background-color: #f5f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    color: #0066cc;
    font-size: 28px;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    background-color: #0066cc;
    color: #fff;
}

/* 特色项目标题 */
.feature-content h3 {
    font-size: 20px;
    color: #333;
    margin: 0 0 15px;
    letter-spacing: 0.5px;
    font-weight: 600;
}

/* 特色项目描述 */
.feature-content p {
    font-size: 16px;
    color: #666;
    line-height: 1.7;
    margin: 0;
    letter-spacing: 0.3px;
    word-spacing: 1px;
}

/* 强调文本 */
.feature-content strong {
    font-weight: 600;
    color: #333;
}

.feature-content em {
    font-style: italic;
    color: #0066cc;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .about-features {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .feature-item {
        padding: 20px;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
        margin-bottom: 20px;
    }
    
    .feature-content h3 {
        font-size: 18px;
    }
    
    .feature-content p {
        font-size: 15px;
        line-height: 1.6;
    }
}
