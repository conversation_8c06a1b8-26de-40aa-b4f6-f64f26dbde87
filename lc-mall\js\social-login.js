/**
 * 社交登录功能
 * 处理QQ和微信第三方登录
 */

// 初始化社交登录
function initSocialLogin() {
    console.log('初始化社交登录...');

    // 获取社交登录按钮
    const wechatLoginBtn = document.getElementById('wechat-login-btn');
    const qqLoginBtn = document.getElementById('qq-login-btn');

    // 添加点击事件
    if (wechatLoginBtn) {
        wechatLoginBtn.addEventListener('click', handleWechatLogin);
    }

    if (qqLoginBtn) {
        qqLoginBtn.addEventListener('click', handleQQLogin);
    }
}

// 处理微信登录
function handleWechatLogin() {
    console.log('处理微信登录...');

    // 显示加载中提示
    showSocialLoginLoading('wechat-login-btn', '处理中...');

    // 获取社交登录配置
    fetch('/api/social/config')
        .then(response => response.json())
        .then(config => {
            console.log('获取社交登录配置:', config);

            if (config.wechat && config.wechat.enabled) {
                if (isWeixinBrowser()) {
                    // 在微信浏览器中，使用微信网页授权
                    redirectToWechatAuth(config.wechat);
                } else {
                    // 在非微信浏览器中，显示二维码或提示
                    showWechatQRCode(config.wechat);
                }
            } else {
                // 微信登录未启用
                restoreSocialLoginButton('wechat-login-btn');
                showNotification('微信登录功能暂未启用', 'error');
            }
        })
        .catch(error => {
            console.error('获取社交登录配置失败:', error);
            restoreSocialLoginButton('wechat-login-btn');
            showNotification('获取社交登录配置失败', 'error');
        });
}

// 检查是否在微信浏览器中
function isWeixinBrowser() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.indexOf('micromessenger') !== -1;
}

// 重定向到微信授权页面
function redirectToWechatAuth(wechatConfig) {
    // 构建授权URL
    const redirectUri = encodeURIComponent(wechatConfig.redirect_uri);
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${wechatConfig.app_id}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`;

    console.log('重定向到微信授权页面:', authUrl);

    // 在实际应用中，这里应该跳转到微信授权页面
    // window.location.href = authUrl;

    // 目前使用模拟数据
    setTimeout(() => {
        const mockWechatUser = {
            id: 'wx_' + Math.floor(Math.random() * 1000000),
            name: '微信用户' + Math.floor(Math.random() * 1000),
            avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/sample/132',
            social_platform: 'wechat',
            social_openid: 'wx_openid_' + Math.floor(Math.random() * 1000000)
        };

        // 处理微信登录成功
        handleSocialLoginSuccess('wechat', mockWechatUser);
    }, 1500);
}

// 显示微信二维码
function showWechatQRCode(wechatConfig) {
    // 恢复按钮状态
    restoreSocialLoginButton('wechat-login-btn');

    // 创建二维码模态框
    const qrcodeModal = document.createElement('div');
    qrcodeModal.className = 'qrcode-modal';
    qrcodeModal.innerHTML = `
        <div class="qrcode-content">
            <span class="close">&times;</span>
            <h3>微信扫码登录</h3>
            <div class="modal-qrcode-container">
                <div class="qrcode-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>正在生成二维码...</p>
                </div>
                <div class="qrcode-image" style="display: none;"></div>
            </div>
            <p class="qrcode-tip">请使用微信扫描二维码登录</p>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(qrcodeModal);

    // 显示模态框
    setTimeout(() => {
        qrcodeModal.classList.add('show');
    }, 10);

    // 关闭按钮事件
    const closeBtn = qrcodeModal.querySelector('.close');
    closeBtn.addEventListener('click', () => {
        qrcodeModal.classList.remove('show');
        setTimeout(() => {
            qrcodeModal.remove();
        }, 300);
    });

    // 模拟生成二维码
    setTimeout(() => {
        const qrcodeLoading = qrcodeModal.querySelector('.qrcode-loading');
        const qrcodeImage = qrcodeModal.querySelector('.qrcode-image');

        qrcodeLoading.style.display = 'none';
        qrcodeImage.style.display = 'block';

        // 在实际应用中，这里应该生成真实的二维码
        qrcodeImage.innerHTML = `
            <img src="https://via.placeholder.com/200x200?text=Wechat+QR+Code" alt="微信二维码">
        `;

        // 模拟扫码成功
        setTimeout(() => {
            const mockWechatUser = {
                id: 'wx_' + Math.floor(Math.random() * 1000000),
                name: '微信用户' + Math.floor(Math.random() * 1000),
                avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/sample/132',
                social_platform: 'wechat',
                social_openid: 'wx_openid_' + Math.floor(Math.random() * 1000000)
            };

            // 关闭二维码模态框
            qrcodeModal.classList.remove('show');
            setTimeout(() => {
                qrcodeModal.remove();
            }, 300);

            // 处理微信登录成功
            handleSocialLoginSuccess('wechat', mockWechatUser);
        }, 3000);
    }, 1500);
}

// 处理QQ登录
function handleQQLogin() {
    console.log('处理QQ登录...');

    // 显示加载中提示
    showSocialLoginLoading('qq-login-btn', '处理中...');

    // 获取社交登录配置
    fetch('/api/social/config')
        .then(response => response.json())
        .then(config => {
            console.log('获取社交登录配置:', config);

            if (config.qq && config.qq.enabled) {
                // 在实际应用中，这里应该跳转到QQ授权页面
                // 目前使用模拟数据
                setTimeout(() => {
                    const mockQQUser = {
                        id: 'qq_' + Math.floor(Math.random() * 1000000),
                        name: 'QQ用户' + Math.floor(Math.random() * 1000),
                        avatar: 'https://thirdqq.qlogo.cn/g?b=qq&k=sample&s=100',
                        social_platform: 'qq',
                        social_openid: 'qq_openid_' + Math.floor(Math.random() * 1000000)
                    };

                    // 处理QQ登录成功
                    handleSocialLoginSuccess('qq', mockQQUser);
                }, 1500);
            } else {
                // QQ登录未启用
                restoreSocialLoginButton('qq-login-btn');
                showNotification('QQ登录功能暂未启用', 'error');
            }
        })
        .catch(error => {
            console.error('获取社交登录配置失败:', error);
            restoreSocialLoginButton('qq-login-btn');
            showNotification('获取社交登录配置失败', 'error');
        });
}

// 显示社交登录加载中状态
function showSocialLoginLoading(btnId, loadingText) {
    const btn = document.getElementById(btnId);
    if (!btn) return;

    const originalContent = btn.innerHTML;
    btn.setAttribute('data-original-content', originalContent);
    btn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${loadingText}`;
    btn.disabled = true;
}

// 恢复社交登录按钮状态
function restoreSocialLoginButton(btnId) {
    const btn = document.getElementById(btnId);
    if (!btn) return;

    const originalContent = btn.getAttribute('data-original-content');
    if (originalContent) {
        btn.innerHTML = originalContent;
    }
    btn.disabled = false;
}

// 处理社交登录成功
function handleSocialLoginSuccess(platform, userData) {
    console.log(`${platform}登录成功:`, userData);

    // 恢复按钮状态
    restoreSocialLoginButton(platform === 'wechat' ? 'wechat-login-btn' : 'qq-login-btn');

    // 构建登录请求数据
    const loginData = {
        social_platform: platform,
        social_openid: userData.social_openid,
        name: userData.name,
        avatar: userData.avatar,
        company: platform === 'wechat' ? '微信用户' : 'QQ用户'
    };

    // 调用API进行社交登录
    fetch('/api/users/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(loginData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('社交登录失败');
        }
        return response.json();
    })
    .then(user => {
        console.log('社交登录成功，用户数据:', user);

        // 关闭登录模态框
        closeLoginModal();

        // 保存用户会话
        ApiClient.saveUserSession(user);

        // 更新用户状态
        updateUserStatus();

        // 显示成功通知
        showNotification(`${platform === 'wechat' ? '微信' : 'QQ'}登录成功！`, 'success');
    })
    .catch(error => {
        console.error('社交登录失败:', error);
        showNotification(`${platform === 'wechat' ? '微信' : 'QQ'}登录失败，请稍后重试`, 'error');
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initSocialLogin();
});
