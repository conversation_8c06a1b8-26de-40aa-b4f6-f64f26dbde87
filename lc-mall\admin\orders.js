// Orders management functionality
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const ordersTable = document.getElementById('orders-table');
    const orderModal = document.getElementById('order-modal');
    const orderItemsTable = document.getElementById('order-items-table');
    const statusFilter = document.getElementById('status-filter');
    const orderSearch = document.getElementById('order-search');
    const searchBtn = document.getElementById('search-btn');
    const updateStatusSelect = document.getElementById('update-status');
    const updateStatusBtn = document.getElementById('update-status-btn');

    // Order data
    let orders = [];

    // Variables
    let currentOrderId = null;

    // Initialize - load orders from API
    loadOrders();

    // Function to load orders from API
    async function loadOrders() {
        try {
            // Show loading indicator
            const tbody = ordersTable.querySelector('tbody');
            tbody.innerHTML = '<tr><td colspan="8" class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在加载订单数据...</td></tr>';

            // Get orders from API
            orders = await ApiClient.getOrders();

            // Display orders
            displayOrders(orders);
        } catch (error) {
            console.error('加载订单数据失败:', error);
            const tbody = ordersTable.querySelector('tbody');
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">加载订单数据失败，请刷新页面重试</td></tr>';
        }
    }

    // Event Listeners
    statusFilter.addEventListener('change', filterOrders);
    searchBtn.addEventListener('click', searchOrders);
    orderSearch.addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            searchOrders();
        }
    });

    updateStatusBtn.addEventListener('click', updateOrderStatus);

    // Functions
    function displayOrders(ordersToDisplay) {
        const tbody = ordersTable.querySelector('tbody');
        tbody.innerHTML = '';

        if (ordersToDisplay.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="empty-table">暂无订单数据</td></tr>';
            return;
        }

        ordersToDisplay.forEach(order => {
            const tr = document.createElement('tr');

            // Format date
            const orderDate = new Date(order.date);
            const formattedDate = `${orderDate.getFullYear()}-${(orderDate.getMonth() + 1).toString().padStart(2, '0')}-${orderDate.getDate().toString().padStart(2, '0')}`;

            // Format status
            let statusClass = '';
            let statusText = '';

            switch (order.status) {
                case 'pending':
                    statusClass = 'status-pending';
                    statusText = '待处理';
                    break;
                case 'payment_pending':
                    statusClass = 'status-payment-pending';
                    statusText = '等待支付确认';
                    break;
                case 'processing':
                    statusClass = 'status-processing';
                    statusText = '处理中';
                    break;
                case 'completed':
                    statusClass = 'status-completed';
                    statusText = '已完成';
                    break;
                case 'cancelled':
                    statusClass = 'status-cancelled';
                    statusText = '已取消';
                    break;
            }

            // Format payment method (默认微信支付)
            let paymentMethod = '微信支付';
            let paymentIcon = '<i class="fab fa-weixin" style="color: #07c160;"></i>';

            if (order.payment_method && order.payment_method !== 'wechat') {
                paymentMethod = '其他支付方式';
                paymentIcon = '<i class="fas fa-question-circle"></i>';
            }

            tr.innerHTML = `
                <td>${order.id}</td>
                <td>${order.customer.name}</td>
                <td>${order.customer.phone}</td>
                <td>${formattedDate}</td>
                <td>¥${order.total.toFixed(2)}</td>
                <td>${paymentIcon} ${paymentMethod}</td>
                <td><span class="status ${statusClass}">${statusText}</span></td>
                <td class="table-actions">
                    <button class="view-btn" data-id="${order.id}"><i class="fas fa-eye"></i></button>
                </td>
            `;

            tbody.appendChild(tr);
        });

        // Add event listeners to view buttons
        document.querySelectorAll('.view-btn').forEach(button => {
            button.addEventListener('click', function() {
                const orderId = this.getAttribute('data-id');
                openOrderModal(orderId);
            });
        });
    }

    function filterOrders() {
        const status = statusFilter.value;

        if (status === 'all') {
            displayOrders(orders);
            return;
        }

        const filteredOrders = orders.filter(order => order.status === status);
        displayOrders(filteredOrders);
    }

    function searchOrders() {
        const searchTerm = orderSearch.value.toLowerCase().trim();

        if (searchTerm === '') {
            filterOrders(); // Apply current filter
            return;
        }

        let filteredOrders = orders.filter(order =>
            (order.id && order.id.toString().includes(searchTerm)) ||
            (order.customer && order.customer.name && order.customer.name.toLowerCase().includes(searchTerm)) ||
            (order.customer && order.customer.phone && order.customer.phone.includes(searchTerm))
        );

        // Apply status filter if active
        const status = statusFilter.value;
        if (status !== 'all') {
            filteredOrders = filteredOrders.filter(order => order.status === status);
        }

        displayOrders(filteredOrders);
    }

    function openOrderModal(orderId) {
        const order = orders.find(o => o.id === orderId || o.id.toString() === orderId.toString());

        if (order) {
            // Fill order details
            document.getElementById('order-id').textContent = order.id;

            const orderDate = new Date(order.date);
            document.getElementById('order-date').textContent = `${orderDate.getFullYear()}-${(orderDate.getMonth() + 1).toString().padStart(2, '0')}-${orderDate.getDate().toString().padStart(2, '0')} ${orderDate.getHours().toString().padStart(2, '0')}:${orderDate.getMinutes().toString().padStart(2, '0')}`;

            let statusText = '';
            switch (order.status) {
                case 'pending':
                    statusText = '待处理';
                    break;
                case 'payment_pending':
                    statusText = '等待支付确认';
                    break;
                case 'processing':
                    statusText = '处理中';
                    break;
                case 'completed':
                    statusText = '已完成';
                    break;
                case 'cancelled':
                    statusText = '已取消';
                    break;
            }

            document.getElementById('order-status').textContent = statusText;

            // Display payment method (默认微信支付)
            let paymentMethodText = '🟢 微信支付';
            if (order.payment_method && order.payment_method !== 'wechat') {
                paymentMethodText = '❓ 其他支付方式';
            }
            document.getElementById('order-payment-method').textContent = paymentMethodText;

            document.getElementById('customer-name').textContent = order.customer.name;
            document.getElementById('customer-phone').textContent = order.customer.phone;
            document.getElementById('customer-notes').textContent = order.customer.notes || '无';

            // Fill order items
            const tbody = orderItemsTable.querySelector('tbody');
            tbody.innerHTML = '';

            order.items.forEach(item => {
                const tr = document.createElement('tr');
                const itemTotal = item.price * item.quantity;

                tr.innerHTML = `
                    <td>${item.name}</td>
                    <td>¥${item.price.toFixed(2)}</td>
                    <td>${item.quantity}</td>
                    <td>¥${itemTotal.toFixed(2)}</td>
                `;

                tbody.appendChild(tr);
            });

            document.getElementById('order-total').textContent = `¥${order.total.toFixed(2)}`;

            // Set current order ID for status update
            currentOrderId = orderId;

            // Reset status select
            updateStatusSelect.value = '';

            // Show modal
            orderModal.style.display = 'block';
        }
    }

    async function updateOrderStatus() {
        const newStatus = updateStatusSelect.value;

        if (!newStatus) {
            alert('请选择订单状态');
            return;
        }

        if (currentOrderId) {
            try {
                // Show loading indicator
                updateStatusBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 更新中...';
                updateStatusBtn.disabled = true;

                // Update status via API
                const updatedOrder = await ApiClient.updateOrderStatus(currentOrderId, newStatus);

                if (updatedOrder) {
                    // Reload orders from API
                    orders = await ApiClient.getOrders();

                    // Update display
                    let statusText = '';
                    switch (newStatus) {
                        case 'pending':
                            statusText = '待处理';
                            break;
                        case 'payment_pending':
                            statusText = '等待支付确认';
                            break;
                        case 'processing':
                            statusText = '处理中';
                            break;
                        case 'completed':
                            statusText = '已完成';
                            break;
                        case 'cancelled':
                            statusText = '已取消';
                            break;
                    }

                    document.getElementById('order-status').textContent = statusText;

                    // Refresh orders table
                    filterOrders();

                    alert('订单状态已更新');
                } else {
                    throw new Error('更新订单状态失败');
                }
            } catch (error) {
                console.error('更新订单状态失败:', error);
                alert('更新订单状态失败: ' + error.message);
            } finally {
                // Reset button
                updateStatusBtn.innerHTML = '更新状态';
                updateStatusBtn.disabled = false;
            }
        }
    }
});

// 刷新订单列表
function refreshOrders() {
    console.log('刷新订单列表...');
    loadOrders();
}

// 测试企业微信通知
async function testWeChatWorkNotify() {
    try {
        console.log('测试企业微信通知...');

        // 显示加载状态
        const testBtn = document.querySelector('button[onclick="testWeChatWorkNotify()"]');
        const originalText = testBtn.innerHTML;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
        testBtn.disabled = true;

        // 发送测试消息
        const success = await WeChatWorkNotify.testConnection();

        if (success) {
            alert('✅ 企业微信通知测试成功！\n\n请检查您的企业微信群是否收到测试消息。');
        } else {
            alert('❌ 企业微信通知测试失败！\n\n请检查网络连接和webhook配置。');
        }

        // 恢复按钮状态
        testBtn.innerHTML = originalText;
        testBtn.disabled = false;

    } catch (error) {
        console.error('测试企业微信通知失败:', error);
        alert(`❌ 企业微信通知测试失败！\n\n错误信息: ${error.message}`);

        // 恢复按钮状态
        const testBtn = document.querySelector('button[onclick="testWeChatWorkNotify()"]');
        testBtn.innerHTML = '<i class="fas fa-bell"></i> 测试通知';
        testBtn.disabled = false;
    }
}
