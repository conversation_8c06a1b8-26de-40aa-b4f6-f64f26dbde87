<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品管理 - 龙驰新材料</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header class="admin-header">
        <h1><img src="../images/logo.png" alt="龙驰新材料"> 龙驰新材料管理后台</h1>
        <nav class="admin-nav">
            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> 控制面板</a>
            <a href="products.html" class="active"><i class="fas fa-box"></i> 产品管理</a>
            <a href="orders.html"><i class="fas fa-shopping-cart"></i> 订单管理</a>
            <a href="users.html"><i class="fas fa-users"></i> 用户管理</a>
            <a href="messages.html"><i class="fas fa-comments"></i> 留言管理</a>
            <a href="sms.html"><i class="fas fa-sms"></i> 短信记录</a>
            <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出</a>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-content">
            <div class="admin-card">
                <h2>产品管理</h2>

                <div class="product-actions">
                    <div class="product-search">
                        <input type="text" id="product-search" placeholder="搜索产品...">
                        <button id="search-btn"><i class="fas fa-search"></i></button>
                    </div>
                    <button class="add-product-btn" id="add-product-btn">
                        <i class="fas fa-plus"></i> 添加产品
                    </button>
                </div>

                <div class="table-responsive">
                    <table class="product-table" id="product-table">
                        <thead>
                            <tr>
                                <th>图片</th>
                                <th>产品名称</th>
                                <th>分类</th>
                                <th>SKU</th>
                                <th>价格 (¥)</th>
                                <!-- 库存列已移除 -->
                                <th>推荐</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Products will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Product Modal -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="modal-title">添加产品</h2>

            <form id="product-form">
                <input type="hidden" id="product-id">

                <div class="product-form">
                    <div class="form-group">
                        <label for="product-name">产品名称</label>
                        <input type="text" id="product-name" required>
                    </div>

                    <div class="form-group">
                        <label for="product-category">产品分类</label>
                        <select id="product-category" required>
                            <option value="">选择分类</option>
                            <option value="生物基树脂">生物基树脂</option>
                            <option value="潜固化剂">潜固化剂</option>
                            <option value="附着力促进剂">附着力促进剂</option>
                            <option value="发泡剂">发泡剂</option>
                            <option value="分散剂">分散剂</option>
                            <option value="消泡剂">消泡剂</option>
                            <option value="催化剂">催化剂</option>
                            <option value="防沉剂">防沉剂</option>
                            <option value="粉体助剂">粉体助剂</option>
                            <option value="流平剂">流平剂</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="product-price">价格 (¥)</label>
                        <input type="number" id="product-price" min="0" step="0.01" required>
                    </div>

                    <div class="form-group">
                        <label for="product-image">产品图片</label>
                        <div class="image-upload-container">
                            <div class="image-preview" id="image-preview">
                                <img id="preview-img" src="https://via.placeholder.com/200x200?text=产品图片" alt="预览图">
                            </div>
                            <div class="image-upload-controls">
                                <input type="text" id="product-image" placeholder="图片URL" required>
                                <div class="upload-options">
                                    <button type="button" class="btn btn-small" id="upload-image-btn">
                                        <i class="fas fa-upload"></i> 上传图片
                                    </button>
                                    <input type="file" id="image-file" accept="image/*" style="display: none;">
                                    <button type="button" class="btn btn-small" id="use-placeholder-btn">
                                        <i class="fas fa-image"></i> 使用占位图
                                    </button>
                                </div>
                                <div class="compression-progress" id="compression-progress" style="display: none;">
                                    <div class="progress-bar">
                                        <div class="progress-bar-fill" id="progress-bar-fill"></div>
                                    </div>
                                    <div class="progress-text" id="progress-text">压缩中: 0%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 产品描述字段已移除，使用产品简介代替 -->

                    <div class="form-group">
                        <label for="product-specifications">规格参数</label>
                        <input type="text" id="product-specifications" required>
                    </div>

                    <div class="form-group">
                        <label for="product-sku">产品SKU</label>
                        <input type="text" id="product-sku" placeholder="例如: LC-2024-001">
                    </div>

                    <!-- 库存字段已移除 -->

                    <div class="form-group">
                        <label for="product-featured">推荐产品</label>
                        <select id="product-featured">
                            <option value="true">是</option>
                            <option value="false">否</option>
                        </select>
                    </div>

                    <h3 class="form-section-title">产品详细信息</h3>

                    <div class="form-group">
                        <label for="product-model">产品型号</label>
                        <input type="text" id="product-model" placeholder="例如: WL-101">
                    </div>

                    <div class="form-group">
                        <label for="product-appearance">外观性状</label>
                        <input type="text" id="product-appearance" placeholder="例如: 液体">
                    </div>

                    <div class="form-group">
                        <label for="product-standard">执行标准</label>
                        <input type="text" id="product-standard" placeholder="例如: 国标">
                    </div>

                    <div class="form-group">
                        <label for="product-usage">用途范围</label>
                        <input type="text" id="product-usage" placeholder="例如: 聚氨酯、粘结剂、聚醚等">
                    </div>

                    <div class="form-group">
                        <label for="product-flashpoint">闪点</label>
                        <input type="text" id="product-flashpoint" placeholder="例如: 147℃">
                    </div>

                    <div class="form-group">
                        <label for="product-smell">气味</label>
                        <input type="text" id="product-smell" placeholder="例如: 低">
                    </div>

                    <div class="form-group">
                        <label for="product-function">功能</label>
                        <input type="text" id="product-function" placeholder="例如: 交联固化">
                    </div>

                    <div class="form-group">
                        <label for="product-amine-value">胺值(mgKOH/g)</label>
                        <input type="text" id="product-amine-value" placeholder="例如: 235">
                    </div>

                    <div class="form-group">
                        <label for="product-content">有效成分含量</label>
                        <input type="text" id="product-content" placeholder="例如: 99%">
                    </div>

                    <div class="form-group">
                        <label for="product-imported">是否进口</label>
                        <select id="product-imported">
                            <option value="否">否</option>
                            <option value="是">是</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="product-density">比重(25℃, g/ml)</label>
                        <input type="text" id="product-density" placeholder="例如: 1.02">
                    </div>

                    <div class="form-group">
                        <label for="product-color">颜色</label>
                        <input type="text" id="product-color" placeholder="例如: 无色或淡黄色">
                    </div>

                    <div class="form-group">
                        <label for="product-viscosity">粘度@20℃</label>
                        <input type="text" id="product-viscosity" placeholder="例如: 300 mPa.s">
                    </div>

                    <h3 class="form-section-title">产品说明书</h3>

                    <div class="form-group form-group-full">
                        <label for="product-intro">产品简介</label>
                        <textarea id="product-intro" rows="4" placeholder="详细的产品简介（必填）" required></textarea>
                    </div>

                    <div class="form-group form-group-full">
                        <label for="product-usage-list">推荐用途</label>
                        <textarea id="product-usage-list" rows="4" placeholder="每行一个推荐用途"></textarea>
                    </div>

                    <div class="form-group form-group-full">
                        <label for="product-dosage-list">推荐用量</label>
                        <textarea id="product-dosage-list" rows="4" placeholder="每行一个推荐用量"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn cancel-btn" id="cancel-btn">取消</button>
                        <button type="submit" class="btn save-btn">保存</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>确认删除</h2>
            <p>您确定要删除这个产品吗？此操作无法撤销。</p>
            <div class="form-actions">
                <button type="button" class="btn cancel-btn" id="delete-cancel-btn">取消</button>
                <button type="button" class="btn delete-btn" id="confirm-delete-btn">删除</button>
            </div>
        </div>
    </div>

    <script src="../js/api-client.js"></script>
    <script src="../js/image-compressor.js"></script>
    <script src="admin.js"></script>
    <script src="products.js"></script>
</body>
</html>
