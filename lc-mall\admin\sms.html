<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信记录 - 龙驰新材料</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header class="admin-header">
        <h1><img src="../images/logo.png" alt="龙驰新材料"> 龙驰新材料管理后台</h1>
        <nav class="admin-nav">
            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> 控制面板</a>
            <a href="products.html"><i class="fas fa-box"></i> 产品管理</a>
            <a href="orders.html"><i class="fas fa-shopping-cart"></i> 订单管理</a>
            <a href="users.html"><i class="fas fa-users"></i> 用户管理</a>
            <a href="messages.html"><i class="fas fa-comments"></i> 留言管理</a>
            <a href="sms.html" class="active"><i class="fas fa-sms"></i> 短信记录</a>
            <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> 退出</a>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-content">
            <div class="admin-card">
                <h2>短信记录</h2>

                <div class="product-actions">
                    <div class="product-search">
                        <input type="text" id="sms-search" placeholder="搜索短信...">
                        <button id="search-btn"><i class="fas fa-search"></i></button>
                    </div>
                    <div class="filter-options">
                        <select id="type-filter">
                            <option value="all">全部类型</option>
                            <option value="order">订单通知</option>
                            <option value="message">留言通知</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="product-table" id="sms-table-container">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>类型</th>
                                <th>接收人</th>
                                <th>标题</th>
                                <th>内容</th>
                                <th>发送时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="sms-table">
                            <!-- 短信数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                <div class="empty-table" id="empty-sms" style="display: none;">
                    <i class="fas fa-sms"></i>
                    <p>暂无短信记录</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 短信详情模态框 -->
    <div id="sms-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>短信详情</h2>
            <div id="sms-detail">
                <!-- 短信详情将通过JavaScript动态加载 -->
            </div>
            <div class="modal-actions">
                <button id="delete-sms-btn" class="btn btn-danger">删除记录</button>
            </div>
        </div>
    </div>

    <!-- 引入API客户端 -->
    <script src="../js/api-client.js"></script>
    <!-- 引入管理后台脚本 -->
    <script src="admin.js"></script>
    <script src="sms.js"></script>
</body>
</html>
