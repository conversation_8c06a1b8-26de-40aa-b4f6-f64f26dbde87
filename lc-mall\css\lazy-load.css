/* 
 * 产品懒加载样式
 * 用于产品列表的懒加载功能
 */

/* 加载指示器 */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    width: 100%;
    color: var(--text-light);
}

.loading-indicator i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--primary);
}

.loading-indicator p {
    font-size: 1rem;
    margin: 0;
}

/* 没有更多产品指示器 */
.no-more-products {
    display: flex;
    justify-content: center;
    padding: 1.5rem;
    width: 100%;
    color: var(--text-light);
    font-size: 0.9rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

/* 产品卡片动画 */
.product-card {
    transition: opacity 0.5s ease, transform 0.5s ease;
}

/* 产品卡片悬停效果增强 */
.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .loading-indicator {
        padding: 1.5rem;
    }
    
    .loading-indicator i {
        font-size: 1.5rem;
    }
    
    .loading-indicator p {
        font-size: 0.9rem;
    }
    
    .no-more-products {
        padding: 1rem;
        font-size: 0.8rem;
    }
}
