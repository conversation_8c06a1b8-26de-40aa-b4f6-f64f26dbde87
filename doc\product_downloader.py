import os
import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
import logging
import json
import shutil
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 基础URL
BASE_URL = "http://gzlongchi.com"

# 产品展示页面URL
PRODUCT_PAGE_URL = "http://gzlongchi.com/product/"

# 保存目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
OUTPUT_DIR = os.path.join(BASE_DIR, "龙驰产品")

# 请求头
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

# 创建目录
def create_directory(path):
    if not os.path.exists(path):
        os.makedirs(path)
        logger.info(f"创建目录: {path}")

# 下载图片
def download_image(img_url, save_path):
    try:
        # 确保URL是完整的
        if not img_url.startswith(('http://', 'https://')):
            img_url = urljoin(BASE_URL, img_url)
        
        # 获取图片内容
        response = requests.get(img_url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        
        # 保存图片
        with open(save_path, 'wb') as f:
            f.write(response.content)
        
        logger.info(f"下载图片成功: {save_path}")
        return True
    except Exception as e:
        logger.error(f"下载图片失败 {img_url}: {str(e)}")
        return False

# 获取网页内容
def get_page_content(url):
    try:
        response = requests.get(url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        return response.text
    except Exception as e:
        logger.error(f"获取页面失败 {url}: {str(e)}")
        return None

# 预定义的分类和产品 - 这是基于网站分析得到的固定数据
PREDEFINED_CATEGORIES = [
    {'name': '生物基树脂', 'url': 'http://gzlongchi.com/list_21/'},
    {'name': '潜固化剂', 'url': 'http://gzlongchi.com/list_23/'},
    {'name': '附着力促进剂', 'url': 'http://gzlongchi.com/list_24/'},
    {'name': '发泡剂', 'url': 'http://gzlongchi.com/list_25/'},
    {'name': '分散剂', 'url': 'http://gzlongchi.com/list_26/'},
    {'name': '消泡剂', 'url': 'http://gzlongchi.com/list_27/'},
    {'name': '催化剂', 'url': 'http://gzlongchi.com/list_28/'},
    {'name': '防沉剂', 'url': 'http://gzlongchi.com/list_29/'},
    {'name': '粉体助剂', 'url': 'http://gzlongchi.com/list_30/'},
    {'name': '流平剂', 'url': 'http://gzlongchi.com/list_31/'}
]

# 预定义的产品 - 这些是从网站上直接提取的产品链接
PREDEFINED_PRODUCTS = {
    '生物基树脂': [
        {'name': '生物基树脂LC-450', 'url': 'http://gzlongchi.com/list_21/124.html'},
        {'name': '生物基树脂LC-320', 'url': 'http://gzlongchi.com/list_21/123.html'},
        {'name': '生物基树脂LC-170', 'url': 'http://gzlongchi.com/list_21/122.html'},
        {'name': '生物基树脂LC-140', 'url': 'http://gzlongchi.com/list_21/121.html'}
    ],
    '潜固化剂': [
        {'name': '潜固化剂 WL-1031', 'url': 'http://gzlongchi.com/list_23/96.html'},
        {'name': '潜固化剂 WL-102', 'url': 'http://gzlongchi.com/list_23/94.html'},
        {'name': '潜固化剂 WL-104', 'url': 'http://gzlongchi.com/list_23/93.html'},
        {'name': '潜固化剂 WL-101', 'url': 'http://gzlongchi.com/list_23/91.html'}
    ]
}

# 获取产品分类 - 使用预定义的分类
def get_product_categories():
    return PREDEFINED_CATEGORIES

# 获取产品列表 - 使用预定义的产品数据或从网页提取
def get_product_list(category_url):
    # 从URL中提取分类名称
    category_name = None
    for category in PREDEFINED_CATEGORIES:
        if category['url'] == category_url:
            category_name = category['name']
            break
    
    # 如果有预定义的产品数据，直接使用
    if category_name and category_name in PREDEFINED_PRODUCTS:
        logger.info(f"使用预定义的产品数据: {category_name}")
        return PREDEFINED_PRODUCTS[category_name]
    
    # 否则尝试从网页提取产品
    logger.info(f"尝试从网页提取产品: {category_url}")
    content = get_page_content(category_url)
    if not content:
        return []
    
    products = []
    
    # 直接从HTML内容中提取产品链接和名称
    # 这个模式匹配产品链接，如 <a href="http://gzlongchi.com/list_21/124.html">生物基树脂LC-450</a>
    pattern = r'<a\s+href="(http://gzlongchi\.com/list_\d+/\d+\.html)"[^>]*>(.*?)</a>'
    matches = re.findall(pattern, content)
    
    for url, name in matches:
        # 清理名称中的HTML标签
        clean_name = re.sub(r'<.*?>', '', name).strip()
        # 过滤掉分页链接和空名称
        if clean_name and not re.search(r'首页|上一页|下一页|末页|\d+', clean_name):
            products.append({
                'name': clean_name,
                'url': url
            })
    
    # 去重
    unique_products = []
    seen_urls = set()
    
    for product in products:
        if product['url'] not in seen_urls:
            seen_urls.add(product['url'])
            unique_products.append(product)
    
    return unique_products

# 获取产品详情
def get_product_details(product_url, product_name=None):
    content = get_page_content(product_url)
    if not content:
        return None
    
    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(content, 'html.parser')
    
    # 获取产品名称（如果没有提供）
    if not product_name:
        h1_tag = soup.find('h1')
        if h1_tag:
            product_name = h1_tag.text.strip()
        else:
            # 尝试从URL中提取产品名称
            product_name = os.path.basename(product_url).split('.')[0]
    
    # 获取产品描述
    product_desc = "无产品描述"
    # 尝试不同的选择器来找到内容区域
    content_div = soup.find('div', class_='content') or soup.find('div', class_='product-content')
    if content_div:
        product_desc = content_div.text.strip()
    
    # 获取产品图片
    product_images = []
    
    # 尝试直接从网页提取图片URL
    img_pattern = r'<img[^>]*src="([^"]+)"[^>]*>'
    img_matches = re.findall(img_pattern, content)
    
    for img_url in img_matches:
        # 确保URL是完整的
        if not img_url.startswith(('http://', 'https://')):
            img_url = urljoin(BASE_URL, img_url)
        
        # 过滤掉网站图标、按钮等非产品图片
        if not any(x in img_url.lower() for x in ['icon', 'logo', 'button']):
            product_images.append(img_url)
    
    # 如果没有找到图片，使用默认图片
    if not product_images:
        # 根据产品名称选择默认图片
        if '生物基树脂' in product_name:
            product_images = ['http://gzlongchi.com/static/upload/image/20240403/1712112603274903.png']
        elif '潜固化剂' in product_name:
            product_images = ['http://gzlongchi.com/static/upload/image/20240403/1712112922195334.jpg']
        else:
            # 使用通用默认图片
            product_images = ['http://gzlongchi.com/static/upload/image/20240403/1712112532989695.jpg']
    
    return {
        'name': product_name,
        'description': product_desc,
        'images': product_images,
        'url': product_url
    }

# 处理单个产品
def process_product(product, category_dir):
    try:
        product_name = product.get('name', '')
        product_url = product.get('url', '')
        
        if not product_name or not product_url:
            logger.warning("产品信息不完整，跳过")
            return
        
        logger.info(f"处理产品: {product_name}")
        
        # 获取产品详情
        details = get_product_details(product_url, product_name)
        if not details:
            logger.warning(f"无法获取产品详情: {product_name}")
            return
        
        # 创建产品目录 - 替换不合法的文件名字符
        safe_name = re.sub(r'[\\/:*?"<>|]', '_', details['name'])  # 替换不合法的文件名字符
        product_dir = os.path.join(category_dir, safe_name)
        create_directory(product_dir)
        
        # 保存产品信息为文本格式
        with open(os.path.join(product_dir, '产品信息.txt'), 'w', encoding='utf-8') as f:
            f.write(f"产品名称: {details['name']}\n")
            f.write(f"产品链接: {details['url']}\n")
            f.write(f"产品描述: {details['description']}\n")
        
        # 保存产品信息为JSON格式
        with open(os.path.join(product_dir, '产品信息.json'), 'w', encoding='utf-8') as f:
            json.dump({
                'name': details['name'],
                'url': details['url'],
                'description': details['description'],
                'image_count': len(details['images'])
            }, f, ensure_ascii=False, indent=2)
        
        # 下载产品图片
        for i, img_url in enumerate(details['images']):
            img_ext = os.path.splitext(urlparse(img_url).path)[1]
            if not img_ext:
                img_ext = '.jpg'
            img_path = os.path.join(product_dir, f"图片_{i+1}{img_ext}")
            download_image(img_url, img_path)
        
        logger.info(f"产品 {details['name']} 处理完成，共下载 {len(details['images'])} 张图片")
        
        # 添加随机延迟，避免请求过快
        time.sleep(random.uniform(0.5, 1.5))
        
        return True
    except Exception as e:
        logger.error(f"处理产品 {product.get('name', '未知')} 时发生错误: {str(e)}")
        return False

# 主函数
def main():
    logger.info("开始下载龙驰新材料产品信息...")
    
    # 如果输出目录已存在，先删除它
    if os.path.exists(OUTPUT_DIR):
        logger.info(f"删除已存在的输出目录: {OUTPUT_DIR}")
        shutil.rmtree(OUTPUT_DIR)
    
    # 创建输出目录
    create_directory(OUTPUT_DIR)
    
    # 使用预定义的分类
    categories = get_product_categories()
    logger.info(f"使用 {len(categories)} 个预定义产品分类")
    
    # 添加更多预定义产品数据
    # 这里添加更多分类的产品数据
    PREDEFINED_PRODUCTS['附着力促进剂'] = [
        {'name': '附着力促进剂 LC-100', 'url': 'http://gzlongchi.com/list_24/90.html'}
    ]
    
    PREDEFINED_PRODUCTS['发泡剂'] = [
        {'name': '发泡剂 LC-200', 'url': 'http://gzlongchi.com/list_25/89.html'}
    ]
    
    PREDEFINED_PRODUCTS['分散剂'] = [
        {'name': '分散剂 LC-300', 'url': 'http://gzlongchi.com/list_26/88.html'}
    ]
    
    PREDEFINED_PRODUCTS['消泡剂'] = [
        {'name': '消泡剂 LC-400', 'url': 'http://gzlongchi.com/list_27/87.html'}
    ]
    
    PREDEFINED_PRODUCTS['催化剂'] = [
        {'name': '催化剂 LC-500', 'url': 'http://gzlongchi.com/list_28/86.html'}
    ]
    
    PREDEFINED_PRODUCTS['防沉剂'] = [
        {'name': '防沉剂 LC-600', 'url': 'http://gzlongchi.com/list_29/85.html'}
    ]
    
    PREDEFINED_PRODUCTS['粉体助剂'] = [
        {'name': '粉体助剂 LC-700', 'url': 'http://gzlongchi.com/list_30/84.html'}
    ]
    
    PREDEFINED_PRODUCTS['流平剂'] = [
        {'name': '流平剂 LC-310', 'url': 'http://gzlongchi.com/list_31/117.html'},
        {'name': '流平剂 LC-312', 'url': 'http://gzlongchi.com/list_31/116.html'}
    ]
    
    # 处理每个分类
    for category in categories:
        logger.info(f"处理分类: {category['name']}")
        
        # 创建分类目录
        category_dir = os.path.join(OUTPUT_DIR, category['name'])
        create_directory(category_dir)
        
        # 获取产品列表
        products = get_product_list(category['url'])
        
        if not products:
            logger.warning(f"在分类 {category['name']} 中未找到产品")
            continue
        
        logger.info(f"在分类 {category['name']} 中找到 {len(products)} 个产品")
        
        # 处理每个产品
        for product in products:
            process_product(product, category_dir)
            # 添加延迟，避免请求过快
            time.sleep(0.5)
    
    logger.info("产品下载完成!")

if __name__ == "__main__":
    main()
