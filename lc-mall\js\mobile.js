/**
 * 移动端专用JavaScript
 * 处理移动端特有的逻辑和交互
 */

console.log('Mobile JS loaded');

// 检测是否为移动设备
const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
const isIPhone = /iPhone/i.test(navigator.userAgent);
const isAndroid = /Android/i.test(navigator.userAgent);

// 只在移动设备上执行
if (isMobile) {
    console.log('检测到移动设备，启用移动端专用功能');
    console.log('设备类型:', {
        isIOS: isIOS,
        isIPhone: isIPhone,
        isAndroid: isAndroid
    });

    // 在DOM加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 添加设备特定的类到body元素
        if (isIOS) {
            document.body.classList.add('ios-device');
        }

        if (isIPhone) {
            document.body.classList.add('iphone-device');
        }

        if (isAndroid) {
            document.body.classList.add('android-device');
        }

        // 初始化移动端功能
        initMobile();
    });
}

/**
 * 初始化移动端功能
 */
function initMobile() {
    // 创建移动端分类选择器
    createMobileCategorySelector();

    // 优化商品列表显示
    optimizeProductList();

    // 添加移动端手势支持
    addMobileGestures();

    // 添加移动端调试信息
    addMobileDebugInfo();
}

/**
 * 创建移动端分类选择器
 * 将左侧分类导航转换为下拉选择框
 */
function createMobileCategorySelector() {
    // 获取分类列表
    const categoryLinks = document.querySelectorAll('.category-link');
    if (!categoryLinks.length) return;

    // 创建移动端分类选择器容器
    const mobileCategories = document.createElement('div');
    mobileCategories.className = 'mobile-categories';

    // 创建选择框
    const select = document.createElement('select');
    select.id = 'mobile-category-select';

    // 添加选项
    categoryLinks.forEach(link => {
        const option = document.createElement('option');
        option.value = link.getAttribute('data-category');
        option.textContent = link.textContent;

        // 设置默认选中项
        if (link.classList.contains('active')) {
            option.selected = true;
        }

        select.appendChild(option);
    });

    // 添加事件监听器
    select.addEventListener('change', function() {
        const category = this.value;

        // 调用原有的分类切换函数
        if (typeof setActiveCategory === 'function') {
            setActiveCategory(category);
        } else {
            console.warn('setActiveCategory函数不存在');

            // 备用方案：手动触发对应分类链接的点击事件
            const categoryLink = document.querySelector(`.category-link[data-category="${category}"]`);
            if (categoryLink) {
                categoryLink.click();
            }
        }
    });

    // 将选择框添加到容器
    mobileCategories.appendChild(select);

    // 将容器添加到产品头部
    const productHeader = document.querySelector('.product-header');
    if (productHeader) {
        productHeader.parentNode.insertBefore(mobileCategories, productHeader);
    }
}

/**
 * 优化移动端商品列表显示
 */
function optimizeProductList() {
    // 获取商品列表容器
    const productList = document.getElementById('product-list');
    if (!productList) return;

    // 添加移动端特有的类
    productList.classList.add('mobile-product-list');

    // 监听商品卡片创建
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // 处理新添加的商品卡片
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1 && node.classList.contains('product-card')) {
                        // 为移动端优化商品卡片
                        optimizeProductCard(node);
                    }
                });
            }
        });
    });

    // 开始观察
    observer.observe(productList, { childList: true });

    // 处理已有的商品卡片
    const existingCards = productList.querySelectorAll('.product-card');
    existingCards.forEach(card => {
        optimizeProductCard(card);
    });
}

/**
 * 优化单个商品卡片
 * @param {HTMLElement} card - 商品卡片元素
 */
function optimizeProductCard(card) {
    // 添加移动端特有的类
    card.classList.add('mobile-product-card');

    // 确保图片加载正确
    const img = card.querySelector('img');
    if (img) {
        // 添加图片加载错误处理
        img.onerror = function() {
            console.log('产品图片加载失败，使用占位图');
            this.src = '/lc-mall/images/product-placeholder.jpg';
        };

        // 设置加载优先级
        img.loading = 'eager'; // 立即加载图片

        // 添加图片加载完成事件
        img.onload = function() {
            // 图片加载完成后添加类
            card.classList.add('image-loaded');
        };

        // 如果图片已经加载完成
        if (img.complete) {
            card.classList.add('image-loaded');
        } else {
            // 强制重新加载图片
            const originalSrc = img.src;
            img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
            setTimeout(() => {
                img.src = originalSrc;
            }, 10);
        }
    }

    // 优化产品信息布局
    const productInfo = card.querySelector('.product-info');
    if (productInfo) {
        // 确保产品信息区域有足够的高度
        productInfo.style.minHeight = '120px';
    }

    // 优化价格和购物车按钮行
    const priceCartRow = card.querySelector('.price-cart-row');
    if (priceCartRow) {
        // 确保价格和按钮在同一行且对齐
        priceCartRow.style.display = 'flex';
        priceCartRow.style.justifyContent = 'space-between';
        priceCartRow.style.alignItems = 'center';
    }

    // 设备特定优化
    if (isIPhone) {
        card.classList.add('iphone-product-card');
    }
}

/**
 * 添加移动端手势支持
 */
function addMobileGestures() {
    // 实现简单的左右滑动手势
    let touchStartX = 0;
    let touchEndX = 0;

    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    }, false);

    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    }, false);

    function handleSwipe() {
        const swipeThreshold = 100; // 最小滑动距离

        if (touchEndX < touchStartX - swipeThreshold) {
            // 向左滑动 - 可以用于显示购物车
            const floatingCart = document.getElementById('floating-cart');
            if (floatingCart) {
                floatingCart.click();
            }
        }

        if (touchEndX > touchStartX + swipeThreshold) {
            // 向右滑动 - 可以用于返回或其他功能
            // 这里暂不实现具体功能
        }
    }
}

/**
 * 添加移动端调试信息
 */
function addMobileDebugInfo() {
    // 创建调试信息元素
    const debugInfo = document.createElement('div');
    debugInfo.style.position = 'fixed';
    debugInfo.style.bottom = '10px';
    debugInfo.style.left = '10px';
    debugInfo.style.right = '10px';
    debugInfo.style.backgroundColor = 'rgba(0,0,0,0.7)';
    debugInfo.style.color = 'white';
    debugInfo.style.padding = '10px';
    debugInfo.style.fontSize = '12px';
    debugInfo.style.zIndex = '9999';
    debugInfo.style.maxHeight = '150px';
    debugInfo.style.overflow = 'auto';
    debugInfo.id = 'mobile-debug-info';

    // 添加到页面
    document.body.appendChild(debugInfo);

    // 记录设备信息
    const deviceInfo = `
        <p>设备: ${navigator.userAgent}</p>
        <p>屏幕: ${window.innerWidth}x${window.innerHeight}</p>
        <p>商品数量: ${window.products ? window.products.length : '未知'}</p>
        <p>产品列表元素: ${document.getElementById('product-list') ? '存在' : '不存在'}</p>
    `;

    debugInfo.innerHTML = deviceInfo;

    // 添加日志函数
    window.mobileLog = function(message) {
        const logItem = document.createElement('p');
        logItem.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
        debugInfo.appendChild(logItem);
        debugInfo.scrollTop = debugInfo.scrollHeight;
        console.log(message);
    };

    window.mobileLog('移动端调试已启用');

    // 添加关闭按钮
    const closeButton = document.createElement('button');
    closeButton.textContent = '关闭调试';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '5px';
    closeButton.style.right = '5px';
    closeButton.style.padding = '2px 5px';
    closeButton.style.fontSize = '10px';

    closeButton.addEventListener('click', function() {
        debugInfo.style.display = 'none';
    });

    debugInfo.appendChild(closeButton);
}
