/* 产品按钮样式 */
.btn-product {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
    margin: 5px;
    min-width: 120px;
    border: 1px solid;
}

/* 查看详情按钮 */
.view-product {
    background-color: #ffffff;
    color: #0066cc;
    border-color: #0066cc;
}

.view-product:hover {
    background-color: #f0f8ff;
    box-shadow: 0 2px 5px rgba(0, 102, 204, 0.2);
}

.view-product:active {
    transform: translateY(1px);
}

/* 加入购物车按钮 */
.add-to-cart {
    background-color: #ff3366;
    color: #ffffff;
    border: none;
    border-radius: 0;
    font-weight: bold;
    text-align: center;
    padding: 10px 15px;
    transition: background-color 0.3s;
}

.add-to-cart:hover {
    background-color: #ff4477;
}

.add-to-cart:active {
    background-color: #e62e5c;
}

/* 禁用状态 */
.btn-product.disabled {
    background-color: #f0f0f0;
    color: #999999;
    border-color: #dddddd;
    cursor: not-allowed;
    pointer-events: none;
}

/* 图标样式 */
.btn-product i {
    margin-right: 5px;
    font-size: 16px;
}

/* 产品卡片中的按钮布局 */
.product-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .btn-product {
        padding: 8px 12px;
        font-size: 13px;
        min-width: 100px;
    }

    .product-actions {
        flex-direction: column;
    }

    .btn-product {
        margin: 5px 0;
    }
}

/* 产品详情模态框中的按钮 */
.add-to-cart-modal {
    background-color: #ff3366;
    color: #ffffff;
    border: none;
    padding: 12px 20px;
    border-radius: 0;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
}

.add-to-cart-modal:hover {
    background-color: #ff4477;
}

.add-to-cart-modal:active {
    background-color: #e62e5c;
}

.add-to-cart-modal:disabled {
    background-color: #f0f0f0;
    color: #999999;
    cursor: not-allowed;
}

/* 按钮点击波纹效果 */
.btn-product, .add-to-cart-modal {
    position: relative;
    overflow: hidden;
}

.btn-product::after, .add-to-cart-modal::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.btn-product:active::after, .add-to-cart-modal:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0) translate(-50%, -50%);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20) translate(-50%, -50%);
        opacity: 0;
    }
}

/* 产品名称飞入购物车动画 */
.product-name-fly {
    position: fixed;
    z-index: 9999;
    background-color: #ff6600;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    text-align: center;
    pointer-events: none; /* 防止干扰用户交互 */
    will-change: transform, opacity; /* 优化性能 */
}

.product-name-fly.animate {
    transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
