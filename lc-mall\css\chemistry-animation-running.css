/* 化学元素小人跑动动画 */

/* 小人形状 */
.chem-runner {
    position: absolute;
    z-index: 30;
    width: 20px;
    height: 20px;
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    filter: blur(0px);
    box-shadow: 0 0 15px currentColor;
    transform-origin: center bottom;
}

/* 小人头部 */
.chem-runner::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: inherit;
    box-shadow: 0 0 10px currentColor;
}

/* 小人手臂和腿部 */
.chem-runner::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: inherit;
    border-radius: 2px;
    box-shadow: 0 0 8px currentColor;
}

/* 小人跑动轨迹1 - 左上到右下 */
@keyframes runner-path1 {
    0% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    10% { 
        transform: translate(100px, -50px) rotate(15deg) scale(1.1); 
        opacity: 1;
    }
    20% { 
        transform: translate(200px, 0px) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    30% { 
        transform: translate(300px, 50px) rotate(-15deg) scale(1.1); 
        opacity: 1;
    }
    40% { 
        transform: translate(400px, 0px) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    50% { 
        transform: translate(300px, -50px) rotate(15deg) scale(1.1); 
        opacity: 1;
    }
    60% { 
        transform: translate(200px, 0px) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    70% { 
        transform: translate(100px, 50px) rotate(-15deg) scale(1.1); 
        opacity: 1;
    }
    80% { 
        transform: translate(0px, 0px) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    90% { 
        transform: translate(-50px, -25px) rotate(15deg) scale(1.1); 
        opacity: 1;
    }
    100% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
}

/* 小人跑动轨迹2 - 右上到左下 */
@keyframes runner-path2 {
    0% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    10% { 
        transform: translate(-100px, -50px) rotate(-15deg) scale(1.1); 
        opacity: 1;
    }
    20% { 
        transform: translate(-200px, 0px) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    30% { 
        transform: translate(-300px, 50px) rotate(15deg) scale(1.1); 
        opacity: 1;
    }
    40% { 
        transform: translate(-400px, 0px) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    50% { 
        transform: translate(-300px, -50px) rotate(-15deg) scale(1.1); 
        opacity: 1;
    }
    60% { 
        transform: translate(-200px, 0px) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    70% { 
        transform: translate(-100px, 50px) rotate(15deg) scale(1.1); 
        opacity: 1;
    }
    80% { 
        transform: translate(0px, 0px) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    90% { 
        transform: translate(50px, -25px) rotate(-15deg) scale(1.1); 
        opacity: 1;
    }
    100% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
}

/* 小人跑动轨迹3 - 上下跑动 */
@keyframes runner-path3 {
    0% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    25% { 
        transform: translate(0, -200px) rotate(15deg) scale(1.1); 
        opacity: 1;
    }
    50% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    75% { 
        transform: translate(0, 200px) rotate(-15deg) scale(1.1); 
        opacity: 1;
    }
    100% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
}

/* 小人跑动轨迹4 - 左右跑动 */
@keyframes runner-path4 {
    0% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    25% { 
        transform: translate(-200px, 0) rotate(-15deg) scale(1.1); 
        opacity: 1;
    }
    50% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    75% { 
        transform: translate(200px, 0) rotate(15deg) scale(1.1); 
        opacity: 1;
    }
    100% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
}

/* 小人跑动轨迹5 - 圆形跑动 */
@keyframes runner-path5 {
    0% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.9;
    }
    12.5% { 
        transform: translate(150px, -150px) rotate(45deg) scale(1.1); 
        opacity: 1;
    }
    25% { 
        transform: translate(200px, 0) rotate(90deg) scale(1); 
        opacity: 0.9;
    }
    37.5% { 
        transform: translate(150px, 150px) rotate(135deg) scale(1.1); 
        opacity: 1;
    }
    50% { 
        transform: translate(0, 200px) rotate(180deg) scale(1); 
        opacity: 0.9;
    }
    62.5% { 
        transform: translate(-150px, 150px) rotate(225deg) scale(1.1); 
        opacity: 1;
    }
    75% { 
        transform: translate(-200px, 0) rotate(270deg) scale(1); 
        opacity: 0.9;
    }
    87.5% { 
        transform: translate(-150px, -150px) rotate(315deg) scale(1.1); 
        opacity: 1;
    }
    100% { 
        transform: translate(0, 0) rotate(360deg) scale(1); 
        opacity: 0.9;
    }
}

/* 碰撞效果 */
.chem-collision-large {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0) 70%);
    filter: blur(5px);
    transform: scale(0);
    opacity: 0;
    z-index: 35;
    animation: collision-large 2s ease-out forwards;
}

@keyframes collision-large {
    0% { transform: scale(0); opacity: 0.9; }
    50% { transform: scale(3); opacity: 0.5; }
    100% { transform: scale(6); opacity: 0; }
}

/* 小人腿部跑动动画 */
.chem-runner-legs {
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 8px;
    animation: runner-legs 0.5s linear infinite;
}

@keyframes runner-legs {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(0.7); }
}

/* 小人手臂跑动动画 */
.chem-runner-arms {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    animation: runner-arms 0.5s linear infinite;
}

@keyframes runner-arms {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(5deg); }
    75% { transform: rotate(-5deg); }
}
