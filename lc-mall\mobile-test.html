<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端优化测试页面 - 龙驰新材料</title>
    <link rel="icon" type="image/x-icon" href="./public/favicon/favicon.ico">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/pages.css">
    <link rel="stylesheet" href="css/footer-new.css">
    <link rel="stylesheet" href="css/cart-animation.css">
    <!-- 移动端优化样式 -->
    <link rel="stylesheet" href="css/mobile-optimizations.css">
    <!-- 移动端专用样式 -->
    <link rel="stylesheet" href="css/mobile.css">
    <!-- iPhone设备专用优化样式 -->
    <link rel="stylesheet" href="css/iphone-optimizations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .test-section {
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f5f5f5;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            margin-bottom: 10px;
            color: #333;
        }
        
        .test-description {
            font-size: 14px;
            margin-bottom: 15px;
            color: #666;
        }
        
        .test-result {
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .device-info {
            font-size: 12px;
            color: #888;
            margin-bottom: 5px;
        }
        
        .test-product-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 15px;
        }
        
        .test-button {
            display: inline-block;
            padding: 8px 15px;
            background-color: #4ecdc4;
            color: white;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <a href="index.html" class="back-link">
                <i class="fas fa-arrow-left"></i> 返回首页
            </a>
            <div class="logo-container mobile-logo">
                <img src="images/logo.png" alt="龙驰新材料" class="logo">
            </div>
        </div>
    </header>

    <div class="container">
        <h1>移动端优化测试页面</h1>
        <p>此页面用于测试移动端优化效果，特别是针对iPhone设备的优化。</p>
        
        <div class="test-section">
            <h2 class="test-title">设备检测</h2>
            <p class="test-description">检测当前设备类型并应用相应的优化。</p>
            <div class="test-result" id="device-detection-result">
                <p class="device-info">正在检测设备信息...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">产品卡片测试</h2>
            <p class="test-description">测试产品卡片在移动设备上的显示效果。</p>
            <div class="test-product-container" id="test-product-list">
                <!-- 测试产品卡片将在这里动态生成 -->
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">触摸交互测试</h2>
            <p class="test-description">测试触摸反馈效果。</p>
            <div class="test-result">
                <button class="test-button">点击测试按钮</button>
                <button class="test-button">另一个测试按钮</button>
                <div class="test-product-card" style="width: 100%; height: 100px; background-color: #e0e0e0; display: flex; align-items: center; justify-content: center; border-radius: 8px; margin-top: 10px;">
                    点击测试产品卡片
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">滚动性能测试</h2>
            <p class="test-description">测试滚动性能优化效果。</p>
            <div class="test-result" style="max-height: 150px; overflow-y: auto; -webkit-overflow-scrolling: touch;">
                <p>滚动测试内容 - 向下滚动查看更多</p>
                <p>测试行 1</p>
                <p>测试行 2</p>
                <p>测试行 3</p>
                <p>测试行 4</p>
                <p>测试行 5</p>
                <p>测试行 6</p>
                <p>测试行 7</p>
                <p>测试行 8</p>
                <p>测试行 9</p>
                <p>测试行 10</p>
            </div>
        </div>
    </div>

    <footer class="footer-new">
        <div class="copyright">
            移动端优化测试页面 - 龙驰新材料
        </div>
    </footer>

    <script src="js/api-client.js"></script>
    <script src="js/main.js"></script>
    <script src="js/mobile.js"></script>
    <script src="js/mobile-optimizations.js"></script>
    <script>
        // 测试页面专用脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 显示设备信息
            const deviceDetectionResult = document.getElementById('device-detection-result');
            const deviceInfo = {
                userAgent: navigator.userAgent,
                screenWidth: window.innerWidth,
                screenHeight: window.innerHeight,
                pixelRatio: window.devicePixelRatio || 1,
                isMobile: /iPhone|iPad|iPod|Android/i.test(navigator.userAgent),
                isIOS: /iPhone|iPad|iPod/i.test(navigator.userAgent),
                isIPhone: /iPhone/i.test(navigator.userAgent),
                isAndroid: /Android/i.test(navigator.userAgent)
            };
            
            let deviceInfoHTML = '';
            for (const [key, value] of Object.entries(deviceInfo)) {
                deviceInfoHTML += `<p class="device-info"><strong>${key}:</strong> ${value}</p>`;
            }
            
            deviceDetectionResult.innerHTML = deviceInfoHTML;
            
            // 添加设备特定的类到body
            if (deviceInfo.isIOS) {
                document.body.classList.add('ios-device');
            }
            
            if (deviceInfo.isIPhone) {
                document.body.classList.add('iphone-device');
            }
            
            if (deviceInfo.isAndroid) {
                document.body.classList.add('android-device');
            }
            
            // 生成测试产品卡片
            const testProductList = document.getElementById('test-product-list');
            const testProducts = [
                {
                    id: 1,
                    name: "测试产品 1",
                    category: "测试分类",
                    price: 580,
                    image: "/lc-mall/images/products/WL-1031.jpg",
                    description: "这是一个测试产品描述，用于测试在移动设备上的显示效果。",
                    stock: 120
                },
                {
                    id: 2,
                    name: "测试产品 2",
                    category: "测试分类",
                    price: 620,
                    image: "/lc-mall/images/products/WL-102.jpg",
                    description: "这是另一个测试产品描述，用于测试在移动设备上的显示效果。",
                    stock: 85
                }
            ];
            
            testProducts.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                productCard.setAttribute('data-id', product.id);
                productCard.innerHTML = `
                    <div class="product-card-header">
                        <img src="${product.image}" alt="${product.name}">
                        <div class="product-category-tag">${product.category}</div>
                    </div>
                    <div class="product-info">
                        <div class="product-title-row">
                            <h3>${product.name}</h3>
                            <div class="product-stock in-stock">
                                <span class="stock-indicator"></span>
                                有库存 (${product.stock})
                            </div>
                        </div>
                        <p>${product.description}</p>
                        <div class="price-cart-row">
                            <div class="price">¥${product.price.toFixed(2)}</div>
                            <a href="#" class="btn-product add-to-cart" data-id="${product.id}">
                                加入购物车
                            </a>
                        </div>
                    </div>
                `;
                testProductList.appendChild(productCard);
            });
            
            // 添加触摸反馈
            const testButtons = document.querySelectorAll('.test-button');
            testButtons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.97)';
                });
                
                button.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });
            
            const testProductCard = document.querySelector('.test-product-card');
            if (testProductCard) {
                testProductCard.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.97)';
                });
                
                testProductCard.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            }
        });
    </script>
</body>
</html>
