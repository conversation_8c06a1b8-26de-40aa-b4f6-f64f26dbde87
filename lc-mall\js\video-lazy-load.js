/**
 * 视频延迟加载脚本
 * 在页面完全加载后再加载视频，避免影响页面加载速度
 */
document.addEventListener('DOMContentLoaded', function() {
    // 等待页面完全加载后再初始化视频
    window.addEventListener('load', initVideoSection);
});

/**
 * 初始化视频部分
 */
function initVideoSection() {
    // 检查是否在首页
    const isHomePage = document.querySelector('body > header .nav-container nav ul li a.active[href="index.html"]');

    // 渲染视频列表
    if (isHomePage) {
        // 首页只显示精选视频
        renderVideoList(getHomePageVideos());
    } else {
        // 视频页面显示所有视频
        renderVideoList(getAllVideos());
    }

    // 初始化视频点击事件
    initVideoClickEvents();

    // 初始化视频模态框
    initVideoModal();

    // 初始化查看更多按钮
    initViewMoreButton();
}

/**
 * 渲染视频列表
 * @param {Array} videos - 要渲染的视频数据数组
 */
function renderVideoList(videos) {
    const videoListContainer = document.getElementById('video-list-container');
    if (!videoListContainer) return;

    // 清空列表内容
    videoListContainer.innerHTML = '';

    // 检查是否在首页
    const isHomePage = document.querySelector('body > header .nav-container nav ul li a.active[href="index.html"]');

    // 限制视频数量（首页只显示3个）
    const displayVideos = isHomePage ? videos.slice(0, 3) : videos;

    // 添加视频项目
    displayVideos.forEach((video, index) => {
        const videoItem = document.createElement('div');
        videoItem.className = 'video-item';
        videoItem.dataset.id = video.id;

        // 格式化日期
        const formattedDate = formatDate(video.date);

        // 格式化观看次数
        const formattedViews = formatViews(video.views);

        videoItem.innerHTML = `
            <div class="video-placeholder" data-video-src="${video.src}">
                <img src="${video.thumbnail}" alt="${video.title}">
                <div class="play-button">
                    <i class="fas fa-play"></i>
                </div>
                <div class="video-duration">${video.duration}</div>
                <div class="loading-indicator">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <div class="video-info">
                <h3>${video.title}</h3>
                <p>${video.description}</p>
                <div class="video-meta">
                    <div class="date">
                        <i class="far fa-calendar-alt"></i>
                        <span>${formattedDate}</span>
                    </div>
                    <div class="views">
                        <i class="far fa-eye"></i>
                        <span>${formattedViews}次观看</span>
                    </div>
                </div>
            </div>
        `;

        videoListContainer.appendChild(videoItem);
    });

    // 添加视频加载动画效果
    animateVideoItems();

    // 更新查看更多按钮状态
    updateViewMoreButtonVisibility(isHomePage);
}

/**
 * 格式化日期
 * @param {string} dateString - 日期字符串 (YYYY-MM-DD)
 * @returns {string} 格式化后的日期
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 格式化观看次数
 * @param {number} views - 观看次数
 * @returns {string} 格式化后的观看次数
 */
function formatViews(views) {
    if (views >= 10000) {
        return (views / 10000).toFixed(1) + '万';
    } else if (views >= 1000) {
        return (views / 1000).toFixed(1) + 'k';
    } else {
        return views.toString();
    }
}

/**
 * 添加视频项目加载动画
 */
function animateVideoItems() {
    const videoItems = document.querySelectorAll('.video-item');

    videoItems.forEach((item, index) => {
        // 设置初始状态
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';

        // 添加动画
        setTimeout(() => {
            item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 100 * index); // 错开时间，创造级联效果
    });
}

/**
 * 初始化视频点击事件
 */
function initVideoClickEvents() {
    // 等待DOM更新后绑定事件
    setTimeout(() => {
        const videoPlaceholders = document.querySelectorAll('.video-placeholder');

        videoPlaceholders.forEach(placeholder => {
            placeholder.addEventListener('click', function() {
                const videoSrc = this.getAttribute('data-video-src');
                const videoItem = this.closest('.video-item');
                const videoTitle = videoItem.querySelector('.video-info h3').textContent;
                const videoDescription = videoItem.querySelector('.video-info p').textContent;

                // 打开视频模态框
                openVideoModal(videoSrc, videoTitle, videoDescription);
            });
        });
    }, 500);
}

/**
 * 初始化查看更多按钮
 */
function initViewMoreButton() {
    const viewMoreBtn = document.getElementById('view-more-btn');
    if (!viewMoreBtn) return;

    viewMoreBtn.addEventListener('click', function() {
        // 检查是否在首页
        const isHomePage = document.querySelector('body > header .nav-container nav ul li a.active[href="index.html"]');

        if (isHomePage) {
            // 首页点击"查看更多"跳转到视频页面
            window.location.href = 'videos.html';
        } else {
            // 视频页面点击"加载更多"加载下一页视频
            if (typeof loadMoreVideos === 'function') {
                loadMoreVideos();
            } else {
                alert('更多视频正在准备中，敬请期待！');
            }
        }
    });
}

/**
 * 更新查看更多按钮可见性
 * @param {boolean} isHomePage - 是否在首页
 */
function updateViewMoreButtonVisibility(isHomePage) {
    const viewMoreBtn = document.getElementById('view-more-btn');
    if (!viewMoreBtn) return;

    // 首页始终显示"查看更多"按钮
    if (isHomePage) {
        viewMoreBtn.style.display = 'inline-block';
        viewMoreBtn.innerHTML = '查看更多视频 <i class="fas fa-arrow-right"></i>';
    } else {
        // 视频页面根据是否有更多视频决定是否显示"加载更多"按钮
        const videoListContainer = document.getElementById('video-list-container');
        const videoCount = videoListContainer ? videoListContainer.children.length : 0;

        if (videoCount < getAllVideos().length) {
            viewMoreBtn.style.display = 'inline-block';
            viewMoreBtn.innerHTML = '加载更多视频 <i class="fas fa-arrow-down"></i>';
        } else {
            viewMoreBtn.style.display = 'none';
        }
    }
}

/**
 * 初始化视频模态框
 */
function initVideoModal() {
    const videoModal = document.getElementById('video-modal');
    const closeButton = document.getElementById('video-modal-close');

    if (!videoModal || !closeButton) return;

    // 关闭模态框
    closeButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        closeVideoModal();
    });

    // 点击模态框背景关闭
    videoModal.addEventListener('click', function(event) {
        if (event.target === videoModal) {
            closeVideoModal();
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && videoModal.classList.contains('active')) {
            closeVideoModal();
        }
    });

    // 视频播放结束后自动关闭
    videoModal.addEventListener('ended', function(event) {
        if (event.target.id === 'modal-video') {
            // 延迟一秒关闭，让用户有时间看到视频结束
            setTimeout(closeVideoModal, 1000);
        }
    }, true);
}

/**
 * 打开视频模态框
 * @param {string} videoSrc - 视频源URL
 * @param {string} title - 视频标题
 * @param {string} description - 视频描述
 */
function openVideoModal(videoSrc, title, description) {
    const videoModal = document.getElementById('video-modal');
    const modalVideoContainer = document.querySelector('.modal-video-container');
    const modalTitle = document.getElementById('modal-video-title');
    const modalDescription = document.getElementById('modal-video-description');
    const loadingIndicator = document.getElementById('modal-loading-indicator');

    if (!videoModal || !modalVideoContainer) return;

    // 设置视频信息
    if (modalTitle) modalTitle.textContent = title || '视频播放';
    if (modalDescription) modalDescription.textContent = description || '';

    // 显示加载指示器
    if (loadingIndicator) loadingIndicator.classList.add('active');

    // 创建新的视频元素替换旧的
    const oldVideo = document.getElementById('modal-video');
    if (oldVideo) {
        oldVideo.remove();
    }

    const modalVideo = document.createElement('video');
    modalVideo.id = 'modal-video';
    modalVideo.controls = true;
    modalVideoContainer.insertBefore(modalVideo, loadingIndicator);

    // 视频加载完成后播放
    modalVideo.addEventListener('canplay', function onCanPlay() {
        // 隐藏加载指示器
        if (loadingIndicator) loadingIndicator.classList.remove('active');

        // 自动播放视频
        modalVideo.play().catch(error => {
            console.warn('自动播放失败:', error);
            // 显示播放按钮提示用户手动播放
            const playButton = document.createElement('div');
            playButton.className = 'modal-play-button';
            playButton.innerHTML = '<i class="fas fa-play"></i>';
            modalVideo.parentNode.appendChild(playButton);

            playButton.addEventListener('click', function() {
                modalVideo.play();
                playButton.remove();
            });
        });
    });

    // 处理加载错误
    modalVideo.addEventListener('error', function onError(e) {
        console.error('视频加载失败', e);
        if (loadingIndicator) loadingIndicator.classList.remove('active');

        // 创建错误提示元素
        const errorMessage = document.createElement('div');
        errorMessage.className = 'video-error-message';
        errorMessage.innerHTML = '<p>视频加载失败，请稍后再试</p><button class="btn">确定</button>';
        modalVideoContainer.appendChild(errorMessage);

        // 点击确定按钮关闭模态框
        const confirmButton = errorMessage.querySelector('button');
        if (confirmButton) {
            confirmButton.addEventListener('click', function() {
                closeVideoModal();
            });
        }
    });

    // 设置视频源 (在添加事件监听器后设置)
    modalVideo.src = videoSrc;

    // 显示模态框
    videoModal.classList.add('active');

    // 禁止页面滚动
    document.body.style.overflow = 'hidden';
}

/**
 * 关闭视频模态框
 */
function closeVideoModal() {
    const videoModal = document.getElementById('video-modal');
    const modalVideoContainer = document.querySelector('.modal-video-container');
    const loadingIndicator = document.getElementById('modal-loading-indicator');

    if (!videoModal) return;

    // 清理视频元素
    const modalVideo = document.getElementById('modal-video');
    if (modalVideo) {
        // 停止视频播放
        try {
            modalVideo.pause();
            modalVideo.removeAttribute('src');
            modalVideo.load();
        } catch (e) {
            console.warn('清理视频元素时出错:', e);
        }
    }

    // 隐藏加载指示器
    if (loadingIndicator) {
        loadingIndicator.classList.remove('active');
    }

    // 移除可能存在的播放按钮
    const playButton = document.querySelector('.modal-play-button');
    if (playButton) playButton.remove();

    // 移除可能存在的错误消息
    const errorMessage = document.querySelector('.video-error-message');
    if (errorMessage) errorMessage.remove();

    // 隐藏模态框
    videoModal.classList.remove('active');

    // 恢复页面滚动
    document.body.style.overflow = '';
}
