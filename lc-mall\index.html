<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>龙驰新材料 - 化工材料商城</title>
    <link rel="icon" type="image/x-icon" href="./public/favicon/favicon.ico">
    <link rel="icon" type="image/svg+xml" href="./public/favicon/favicon.svg">
    <link rel="apple-touch-icon" href="./public/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="96x96" href="./public/favicon/favicon-96x96.png">
    <link rel="manifest" href="./public/favicon/site.webmanifest">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/pages.css">
    <link rel="stylesheet" href="css/footer-new.css">
    <link rel="stylesheet" href="css/cart-animation.css">
    <!-- 移动端优化样式 -->
    <link rel="stylesheet" href="css/mobile-optimizations.css">
    <!-- 移动端专用样式 -->
    <link rel="stylesheet" href="css/mobile.css">
    <!-- iPhone设备专用优化样式 -->
    <link rel="stylesheet" href="css/iphone-optimizations.css">
    <!-- 桌面端Logo修复 -->
    <link rel="stylesheet" href="css/desktop-logo-fix.css">
    <!-- 友情链接样式 -->
    <link rel="stylesheet" href="css/friend-links.css">
    <!-- 头部背景样式 -->
    <link rel="stylesheet" href="css/header-background.css">
    <!-- 元素掉落动画样式 -->
    <link rel="stylesheet" href="css/element-drops.css">
    <!-- 产品按钮样式 -->
    <link rel="stylesheet" href="css/product-buttons.css">
    <!-- 产品标题行样式 -->
    <link rel="stylesheet" href="css/product-title-row.css">
    <!-- 用户相关样式 -->
    <link rel="stylesheet" href="css/user.css">
    <!-- 按钮修复样式 -->
    <link rel="stylesheet" href="css/button-fix.css">
    <!-- 导航栏按钮修复样式 -->
    <link rel="stylesheet" href="css/nav-buttons-fix.css">
    <!-- 用户状态显示修复样式 -->
    <link rel="stylesheet" href="css/user-status-fix.css">
    <!-- 社交登录样式 -->
    <link rel="stylesheet" href="css/social-login.css">
    <!-- 二维码模态框样式 -->
    <link rel="stylesheet" href="css/qrcode-modal.css">
    <!-- 底部二维码修复样式 -->
    <link rel="stylesheet" href="css/footer-qrcode-fix.css">
    <!-- 购物车数量修复样式 -->
    <link rel="stylesheet" href="css/cart-count-fix.css">
    <!-- 购物车位置修复样式 -->
    <link rel="stylesheet" href="css/cart-position-fix.css">
    <!-- 视频部分样式 -->
    <link rel="stylesheet" href="css/video-section.css">
    <!-- 视频部分紧凑版样式 -->
    <link rel="stylesheet" href="css/video-section-compact.css">
    <!-- 确保按钮交互样式在最后加载，以覆盖之前的样式 -->
    <link rel="stylesheet" href="css/button-interactions.css">
    <!-- 注册按钮气泡提示样式 -->
    <link rel="stylesheet" href="css/register-bubble.css">
    <!-- 微信支付样式 -->
    <link rel="stylesheet" href="css/wechat-pay.css">
    <!-- 产品懒加载样式 -->
    <link rel="stylesheet" href="css/lazy-load.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="logo-container">
                    <img src="images/logo.png" alt="龙驰新材料" class="logo">
                </div>
                <div class="header-company-info">
                    <h2>专业聚氨酯材料供应商</h2>
                    <p>潜固化剂系列、生物基树脂系列、功能助剂系列、扩链剂、粉体助剂等高品质化工材料</p>
                </div>
                <div class="hotline">全国咨询热线：<span>17620086966</span></div>
            </div>
        </div>
    </div>

    <header>
        <div class="header-container">
            <button class="mobile-nav-toggle">
                <i class="fas fa-bars"></i>
            </button>

            <div class="logo-container mobile-logo">
                <img src="images/logo.png" alt="龙驰新材料" class="logo">
            </div>

            <div class="nav-container">
                <nav>
                    <ul>
                        <li><a href="index.html" class="active">首页</a></li>
						<li><a href="product-intro.html">产品目录</a></li>
                        <li><a href="company.html">企业风貌</a></li>
                        <li><a href="videos.html">短视频</a></li>
                        <li><a href="news.html">新闻中心</a></li>
                        <li><a href="#products-section">产品展示</a></li>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="message.html">在线留言</a></li>
                    </ul>
                </nav>
            </div>

            <!-- 用户状态区域 -->
            <div id="user-status" class="user-status">
                <button id="login-btn" class="btn btn-sm" style="color: white !important; font-size: 14px !important; font-weight: 500 !important;">
                    <span style="color: white !important;">登录</span>
                </button>
                <div class="register-btn-container">
                    <div class="register-bubble">注册可免费领取一份50g样品</div>
                    <button id="register-btn" class="btn btn-sm" style="color: white !important; font-size: 14px !important; font-weight: 500 !important; background-color: #1a73e8 !important;">
                        <span style="color: white !important;">注册</span>
                    </button>
                </div>
            </div>

            <!-- 购物车图标 -->
            <div class="cart-icon">
                <a href="#cart"><i class="fas fa-shopping-cart"></i> <span id="cart-count">0</span></a>
            </div>
        </div>
    </header>

    <section class="header-background">
        <!-- 静态化学公式背景 -->
        <div class="chemical-formulas">
            <span style="position: absolute; top: 15%; left: 10%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 16px;">CH₃-CH₂-OH + O₂ → CH₃-COOH + H₂O</span>
            <span style="position: absolute; top: 40%; left: 25%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 18px;">C₆H₁₂O₆ → 2C₂H₅OH + 2CO₂</span>
            <span style="position: absolute; top: 70%; left: 15%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 14px;">2H₂O₂ → 2H₂O + O₂</span>
            <span style="position: absolute; top: 25%; left: 75%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 17px;">N₂ + 3H₂ → 2NH₃</span>
            <span style="position: absolute; top: 60%; left: 80%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 15px;">CaCO₃ → CaO + CO₂</span>
            <span style="position: absolute; top: 10%; left: 40%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 16px;">CH₄ + 2O₂ → CO₂ + 2H₂O</span>
            <span style="position: absolute; top: 80%; left: 45%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 14px;">2NaOH + H₂SO₄ → Na₂SO₄ + 2H₂O</span>
            <span style="position: absolute; top: 50%; left: 60%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 15px;">C₆H₁₂O₆ → 2C₃H₆O₃</span>
            <!-- 聚氨酯相关化学公式 -->
            <span style="position: absolute; top: 35%; left: 5%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 16px;">R-NCO + R'-OH → R-NH-COO-R'</span>
            <span style="position: absolute; top: 55%; left: 35%; opacity: 0.3; font-family: Arial, sans-serif; font-size: 17px;">OCN-R-NCO + HO-R'-OH → -[O-R'-O-CO-NH-R-NH-CO]n-</span>
        </div>

        <!-- 元素球体动画容器 -->
        <div id="element-balls-container" class="element-drops-container"></div>


    </section>

    <!-- 视频展示部分 -->
    <section class="video-section compact">
        <div class="container">


            <!-- 视频列表容器 -->
            <div id="video-list-container" class="video-list-container">
                <!-- 视频项目将通过JavaScript动态加载 -->
            </div>

            <!-- 查看更多按钮 -->
            <div class="view-more-container">
                <button id="view-more-btn" class="view-more-btn">
                    查看更多视频 <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- 视频模态框 -->
    <div id="video-modal" class="video-modal">
        <div class="video-modal-content">
            <div id="video-modal-close" class="video-modal-close">
                <i class="fas fa-times"></i>
            </div>
            <div class="modal-video-container">
                <video id="modal-video" controls></video>
                <div id="modal-loading-indicator" class="loading-indicator">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <div class="modal-video-info">
                <h3 id="modal-video-title"></h3>
                <p id="modal-video-description"></p>
            </div>
        </div>
    </div>

    <section id="products-section" class="products">
        <div class="container">

            <div class="product-layout">
                <!-- 左侧分类导航 -->
                <div class="category-sidebar">
                    <h3>产品分类</h3>
                    <ul class="category-nav">
                        <li><a href="#" class="category-link active" data-category="all">全部产品</a></li>
                        <li><a href="#" class="category-link" data-category="生物基树脂">生物基树脂</a></li>
                        <li><a href="#" class="category-link" data-category="潜固化剂">潜固化剂</a></li>
                        <li><a href="#" class="category-link" data-category="附着力促进剂">附着力促进剂</a></li>
                        <li><a href="#" class="category-link" data-category="发泡剂">发泡剂</a></li>
                        <li><a href="#" class="category-link" data-category="分散剂">分散剂</a></li>
                        <li><a href="#" class="category-link" data-category="消泡剂">消泡剂</a></li>
                        <li><a href="#" class="category-link" data-category="催化剂">催化剂</a></li>
                        <li><a href="#" class="category-link" data-category="防沉剂">防沉剂</a></li>
                        <li><a href="#" class="category-link" data-category="粉体助剂">粉体助剂</a></li>
                        <li><a href="#" class="category-link" data-category="流平剂">流平剂</a></li>
                    </ul>

                    <!-- 友情链接 -->
                    <div class="sidebar-section">
                        <h3>友情链接 / LINKS</h3>
                        <ul class="friend-links-nav">
                            <li><a href="https://www.longchichem.com" target="_blank">龙驰新材料官网</a></li>
                            <li><a href="https://b2b.baidu.com/shop/53311667?tpath=index" target="_blank">爱采购平台电脑版</a></li>
                            <li><a href="https://b2b.baidu.com/m/shop?xzhid=53311667&name=%E5%B9%BF%E5%B7%9E%E5%B8%82%E9%BE%99%E9%A9%B0%E6%96%B0%E6%9D%90%E6%96%99%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8" target="_blank">爱采购平台移动版</a></li>
                        </ul>
                    </div>
                </div>

                <!-- 右侧产品展示区 -->
                <div class="product-content">
                    <div class="product-header">
                        <h3 id="current-category">全部产品</h3>
                        <div class="product-results">
                            <p id="product-count" class="product-count">显示 <span id="product-count-number">0</span> 个产品</p>
                        </div>
                    </div>

                    <div class="product-container" id="product-list">
                        <!-- Products will be loaded dynamically -->
                    </div>

                    <div class="product-pagination" id="product-pagination">
                        <!-- Pagination will be added dynamically if needed -->
                    </div>
                </div>
            </div>
        </div>
    </section>







    <footer class="footer-new">
        <div class="footer-nav">
            <a href="index.html">网站首页</a>
            <a href="product-intro.html">产品目录</a>
			<a href="index.html#products-section">产品展示</a>
            <a href="about.html">关于我们</a>
            <a href="news.html">新闻中心</a>
            <a href="company.html">案例展示</a>
            <a href="message.html">在线留言</a>
            <a href="videos.html">短视频</a>
            <a href="about.html">联系我们</a>
        </div>

        <div class="footer-info">
            <div class="company-info">
                <p>广州市龙驰新材料科技有限公司</p>
                <p>吴经理：17620086966</p>
                <p>郑经理：17620026642</p>
                <p>邮箱：<a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p>地址：广州市白云区鹤龙街康庄路1号</p>
            </div>

            <div class="qrcode-container">
                <img src="images/erweima.png" alt="扫码关注">
                <p>扫码关注</p>
            </div>
        </div>

        <div class="copyright">
            Copyright © 2018-2023 广州市龙驰新材料科技有限公司 All Rights Reserved. 粤ICP备20241901285号
        </div>
    </footer>

    <!-- 固定在右侧的购物车图标 -->
    <div class="floating-cart" id="floating-cart">
        <i class="fas fa-shopping-cart"></i>
        <span class="cart-count" id="floating-cart-count">0</span>
    </div>

    <!-- 回到顶部按钮 -->
    <div class="back-to-top" id="back-to-top">
        <i class="fas fa-arrow-up"></i>
    </div>

    <!-- Cart Modal -->
    <div id="cart-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>购物车</h2>
            <div id="cart-items">
                <!-- Cart items will be loaded here -->
            </div>
            <div class="cart-total">
                <p>总计: <span id="cart-total-price">¥0.00</span></p>
            </div>

            <!-- 支付方式选择 -->
            <div class="payment-methods">
                <div class="payment-method selected" data-payment="wechat">
                    <img src="images/payment/wechat.png" alt="微信支付">
                    <span>微信支付</span>
                </div>
            </div>

            <button id="checkout-btn" class="btn btn-accent">结算 <i class="fas fa-check" style="margin-left: 8px;"></i></button>
        </div>
    </div>

    <!-- Product Modal -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="product-detail">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>用户登录</h2>
            <form id="login-form" class="user-form">
                <div class="form-group">
                    <label for="login-phone">手机号码</label>
                    <input type="tel" id="login-phone" name="phone" required placeholder="请输入注册时的手机号码">
                </div>
                <button type="submit" class="btn-primary">
                    <span style="color: white; font-size: 18px; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.2);">登录</span>
                </button>

                <div class="social-login">
                    <p class="social-login-title">第三方账号登录</p>
                    <div class="social-login-buttons">
                        <button type="button" class="social-login-btn wechat-btn" id="wechat-login-btn">
                            <i class="fab fa-weixin"></i>
                            <span>微信登录</span>
                        </button>
                        <button type="button" class="social-login-btn qq-btn" id="qq-login-btn">
                            <i class="fab fa-qq"></i>
                            <span>QQ登录</span>
                        </button>
                    </div>
                </div>

                <p class="form-switch">还没有账号？<a href="#" id="switch-to-register">立即注册</a></p>
            </form>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="register-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>用户注册</h2>
            <form id="register-form" class="user-form">
                <div class="form-group">
                    <label for="register-phone">手机号码 <span class="required">*</span></label>
                    <input type="tel" id="register-phone" name="phone" required placeholder="请输入手机号码（用于登录）">
                </div>
                <div class="form-group">
                    <label for="register-name">姓名 <span class="required">*</span></label>
                    <input type="text" id="register-name" name="name" required placeholder="请输入您的姓名">
                </div>
                <div class="form-group">
                    <label for="register-wechat">微信号</label>
                    <input type="text" id="register-wechat" name="wechat" placeholder="选填">
                </div>
                <div class="form-group">
                    <label for="register-company">公司名称 <span class="required">*</span></label>
                    <input type="text" id="register-company" name="company" required placeholder="请输入公司名称">
                </div>
                <div class="form-group">
                    <label for="register-position">职位</label>
                    <input type="text" id="register-position" name="position" placeholder="选填">
                </div>
                <div class="form-group">
                    <label for="register-address">联系地址</label>
                    <textarea id="register-address" name="address" rows="2" placeholder="选填"></textarea>
                </div>
                <button type="submit" class="btn-primary">
                    <span style="color: white; font-size: 18px; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.2);">注册</span>
                </button>
                <p class="form-switch">已有账号？<a href="#" id="switch-to-login">立即登录</a></p>
            </form>
        </div>
    </div>

    <!-- QRCode库 -->
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
    <script src="js/sms-service.js"></script>
    <script src="js/button-interactions.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/wechat-pay.js"></script>
    <script src="js/wechat-work-notify.js"></script>
    <script src="js/user.js"></script>
    <script src="js/social-login.js"></script>
    <script src="js/element-balls-animation.js"></script>
    <script src="js/videos-data.js"></script>
    <script src="js/video-lazy-load.js"></script>
    <!-- 产品懒加载脚本 -->
    <script src="js/product-lazy-load.js"></script>
    <script src="js/main.js"></script>
    <!-- 移动端专用脚本 -->
    <script src="js/mobile.js"></script>
    <!-- 移动端优化专用脚本 -->
    <script src="js/mobile-optimizations.js"></script>

    <!-- 源码保护脚本
    <script>
        // 禁用右键菜单
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });

        // 禁用F12、Ctrl+Shift+I等开发者工具快捷键
        document.addEventListener('keydown', function(e) {
            if (
                // F12
                e.keyCode === 123 ||
                // Ctrl+Shift+I
                (e.ctrlKey && e.shiftKey && e.keyCode === 73) ||
                // Ctrl+Shift+J
                (e.ctrlKey && e.shiftKey && e.keyCode === 74) ||
                // Ctrl+Shift+C
                (e.ctrlKey && e.shiftKey && e.keyCode === 67) ||
                // Ctrl+U
                (e.ctrlKey && e.keyCode === 85)
            ) {
                e.preventDefault();
                return false;
            }
        });

        // 检测开发者工具是否打开
        let devToolsOpen = false;

        function detectDevTools() {
            const widthThreshold = window.outerWidth - window.innerWidth > 160;
            const heightThreshold = window.outerHeight - window.innerHeight > 160;

            if (widthThreshold || heightThreshold) {
                if (!devToolsOpen) {
                    devToolsOpen = true;
                    alert('开发者工具已检测到，请关闭以继续使用本工具。');
                }
            } else {
                devToolsOpen = false;
            }
        }

        // 定期检查开发者工具状态
        setInterval(detectDevTools, 1000);

        // 防止页面被嵌入iframe
        if (window.self !== window.top) {
            window.top.location.href = window.self.location.href;
        }
    </script>-->
</body>
</html>
