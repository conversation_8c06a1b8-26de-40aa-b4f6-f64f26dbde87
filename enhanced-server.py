#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高性能Web服务器 - 使用aiohttp提供异步处理能力
支持静态文件服务、API服务、缓存控制和CORS
"""

import os
import sys
import json
import time
import argparse
import logging
import mimetypes
import asyncio
import aiohttp
from pathlib import Path
import datetime
from datetime import timedelta

# 导入微信支付模块
try:
    from wechat_pay import wechat_pay
except ImportError:
    print("警告: 微信支付模块导入失败，微信支付功能将不可用")
    wechat_pay = None

try:
    from aiohttp import web
    import aiohttp_cors
except ImportError:
    print("请先安装必要的依赖: pip install aiohttp aiohttp_cors")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("server.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 确保所有文件类型都有正确的MIME类型
mimetypes.add_type("application/javascript", ".js")
mimetypes.add_type("text/css", ".css")
mimetypes.add_type("image/svg+xml", ".svg")

# 缓存控制 - 静态资源缓存时间（秒）
CACHE_MAX_AGE = 3600  # 1小时

# 数据文件路径
DATA_DIR = os.path.join(os.getcwd(), "lc-mall", "data")
PRODUCTS_FILE = os.path.join(DATA_DIR, "products.json")
ORDERS_FILE = os.path.join(DATA_DIR, "orders.json")
MESSAGES_FILE = os.path.join(DATA_DIR, "messages.json")
SMS_RECORDS_FILE = os.path.join(DATA_DIR, "sms-records.json")
USERS_FILE = os.path.join(DATA_DIR, "users.json")

# 确保数据目录存在
os.makedirs(DATA_DIR, exist_ok=True)

# 初始化数据文件
def init_data_files():
    # 初始化产品数据
    if not os.path.exists(PRODUCTS_FILE):
        with open(PRODUCTS_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

    # 初始化订单数据
    if not os.path.exists(ORDERS_FILE):
        with open(ORDERS_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

    # 初始化留言数据
    if not os.path.exists(MESSAGES_FILE):
        with open(MESSAGES_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

    # 初始化SMS记录数据
    if not os.path.exists(SMS_RECORDS_FILE):
        with open(SMS_RECORDS_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

    # 初始化用户数据
    if not os.path.exists(USERS_FILE):
        with open(USERS_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

# 读取数据
async def read_data(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return []

# 写入数据
async def write_data(file_path, data):
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, ensure_ascii=False, indent=2, fp=f)

class EnhancedServer:
    def __init__(self, host="0.0.0.0", port=8000, root_dir=None):
        self.host = host
        self.port = port
        self.root_dir = Path(root_dir or os.getcwd())
        self.app = web.Application()
        self.setup_routes()
        self.setup_cors()

        # 请求计数器和限流
        self.request_count = 0
        self.last_reset = datetime.datetime.now()
        self.request_limit = 1000  # 每分钟最大请求数

    def setup_routes(self):
        # 静态文件路由
        self.app.router.add_get('/', self.handle_index)
        self.app.router.add_get('/{path:(?!api/).+}', self.handle_static)

        # 数据文件路由 - 特殊处理admin.json
        self.app.router.add_get('/lc-mall/data/admin.json', self.handle_admin_json)

        # API路由
        self.app.router.add_get('/api/products', self.handle_get_products)
        self.app.router.add_post('/api/products', self.handle_add_product)
        self.app.router.add_put('/api/products/{product_id}', self.handle_update_product)
        self.app.router.add_delete('/api/products/{product_id}', self.handle_delete_product)
        self.app.router.add_get('/api/orders', self.handle_get_orders)
        self.app.router.add_post('/api/orders', self.handle_add_order)
        self.app.router.add_get('/api/messages', self.handle_get_messages)
        self.app.router.add_post('/api/messages', self.handle_add_message)
        self.app.router.add_get('/api/sms-records', self.handle_get_sms_records)
        self.app.router.add_post('/api/sms-records', self.handle_add_sms_record)

        # 用户API路由
        self.app.router.add_get('/api/users', self.handle_get_users)
        self.app.router.add_get('/api/users/check', self.handle_check_username)
        self.app.router.add_post('/api/users/register', self.handle_register_user)
        self.app.router.add_post('/api/users/login', self.handle_login_user)
        self.app.router.add_get('/api/social/config', self.handle_get_social_config)

        # 微信支付API路由
        self.app.router.add_post('/api/wechat-pay/create-order', self.handle_wechat_pay_create_order)
        self.app.router.add_get('/api/wechat-pay/query-order', self.handle_wechat_pay_query_order)
        self.app.router.add_post('/api/wechat-pay/notify', self.handle_wechat_pay_notify)

        # 企业微信通知API路由
        self.app.router.add_post('/api/wechat-work/notify', self.handle_wechat_work_notify)
        self.app.router.add_post('/api/wechat-work/test', self.handle_wechat_work_test)

    def setup_cors(self):
        # 设置CORS，允许所有来源的请求
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                max_age=3600,
            )
        })

        # 为所有路由应用CORS设置
        for route in list(self.app.router.routes()):
            cors.add(route)

    async def rate_limit_check(self, request):
        """请求限流检查"""
        self.request_count += 1

        # 每分钟重置计数器
        now = datetime.datetime.now()
        if (now - self.last_reset).total_seconds() > 60:
            self.request_count = 1
            self.last_reset = now

        # 如果请求数超过限制，返回429错误
        if self.request_count > self.request_limit:
            return web.Response(
                status=429,
                text="Too Many Requests - Please try again later",
                content_type="text/plain"
            )

        return None

    async def handle_index(self, request):
        """处理根路径请求，重定向到index.html"""
        # 创建一个新的请求对象，而不是使用clone方法
        new_request = request.clone()
        # 手动设置match_info
        new_request._match_info = {'path': 'index.html'}
        return await self.handle_static(new_request)

    async def handle_static(self, request):
        """处理静态文件请求"""
        # 请求限流检查
        rate_limit_response = await self.rate_limit_check(request)
        if rate_limit_response:
            return rate_limit_response

        path = request.match_info.get('path', '')
        file_path = self.root_dir / path

        try:
            # 安全检查：确保请求的文件在根目录下
            file_path = file_path.resolve()
            if not str(file_path).startswith(str(self.root_dir)):
                logger.warning(f"安全警告: 尝试访问根目录外的文件: {path}")
                return web.Response(status=403, text="Forbidden")

            # 如果是目录，尝试提供index.html
            if file_path.is_dir():
                file_path = file_path / "index.html"

            # 检查文件是否存在
            if not file_path.exists() or not file_path.is_file():
                logger.warning(f"文件未找到: {file_path}")
                return web.Response(status=404, text=f"File not found: {path}")

            # 获取文件的MIME类型
            content_type = mimetypes.guess_type(str(file_path))[0] or 'application/octet-stream'

            # 读取文件内容
            with open(file_path, 'rb') as f:
                content = f.read()

            # 创建响应
            response = web.Response(body=content, content_type=content_type)

            # 设置缓存控制
            if any(file_path.suffix == ext for ext in ['.html', '.htm', '.json']):
                # HTML和JSON文件不缓存
                response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
            else:
                # 静态资源使用缓存
                cache_time = datetime.datetime.now(datetime.timezone.utc) + timedelta(seconds=CACHE_MAX_AGE)
                response.headers['Cache-Control'] = f'public, max-age={CACHE_MAX_AGE}'
                response.headers['Expires'] = cache_time.strftime('%a, %d %b %Y %H:%M:%S GMT')

            logger.info(f"提供文件: {path} ({content_type})")
            return response

        except Exception as e:
            logger.error(f"处理请求 {path} 时出错: {str(e)}", exc_info=True)
            return web.Response(status=500, text=f"Internal Server Error: {str(e)}")

    # API处理函数
    async def handle_get_products(self, request):
        """获取所有产品"""
        products = await read_data(PRODUCTS_FILE)
        return web.json_response(products)

    async def handle_add_product(self, request):
        """添加产品"""
        try:
            # 检查请求大小
            content_length = request.content_length
            if content_length and content_length > 1024 * 1024:  # 如果大于1MB
                logger.warning(f"请求数据过大: {content_length / (1024 * 1024):.2f}MB")
                return web.json_response({"error": "请求数据过大，请减小图片大小或使用图片URL而不是base64编码"}, status=413)

            try:
                data = await request.json()
            except Exception as json_error:
                logger.error(f"解析JSON数据失败: {str(json_error)}")
                return web.json_response({"error": f"无效的JSON数据: {str(json_error)}"}, status=400)

            # 验证必要字段
            required_fields = ['name', 'category', 'price']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return web.json_response({"error": f"缺少必要字段: {', '.join(missing_fields)}"}, status=400)

            products = await read_data(PRODUCTS_FILE)

            # 生成新ID
            new_id = 1
            if products:
                new_id = max(p.get('id', 0) for p in products) + 1

            data['id'] = new_id

            # 确保description字段存在
            if 'description' not in data or not data['description']:
                data['description'] = '暂无产品描述'

            products.append(data)

            await write_data(PRODUCTS_FILE, products)
            logger.info(f"成功添加产品: ID={new_id}, 名称={data.get('name')}")

            return web.json_response(data)
        except Exception as e:
            logger.error(f"添加产品时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_update_product(self, request):
        """更新产品"""
        try:
            # 获取产品ID
            product_id = int(request.match_info.get('product_id', 0))
            if not product_id:
                return web.json_response({"error": "无效的产品ID"}, status=400)

            # 检查请求大小
            content_length = request.content_length
            if content_length and content_length > 1024 * 1024:  # 如果大于1MB
                logger.warning(f"请求数据过大: {content_length / (1024 * 1024):.2f}MB")
                return web.json_response({"error": "请求数据过大，请减小图片大小或使用图片URL而不是base64编码"}, status=413)

            try:
                data = await request.json()
            except Exception as json_error:
                logger.error(f"解析JSON数据失败: {str(json_error)}")
                return web.json_response({"error": f"无效的JSON数据: {str(json_error)}"}, status=400)

            # 验证必要字段
            required_fields = ['name', 'category', 'price']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return web.json_response({"error": f"缺少必要字段: {', '.join(missing_fields)}"}, status=400)

            products = await read_data(PRODUCTS_FILE)

            # 查找产品
            product_index = next((i for i, p in enumerate(products) if p.get('id') == product_id), -1)
            if product_index == -1:
                return web.json_response({"error": f"未找到ID为{product_id}的产品"}, status=404)

            # 保留原始ID
            data['id'] = product_id

            # 确保description字段存在
            if 'description' not in data or not data['description']:
                data['description'] = '暂无产品描述'

            # 更新产品
            products[product_index] = data

            await write_data(PRODUCTS_FILE, products)
            logger.info(f"成功更新产品: ID={product_id}, 名称={data.get('name')}")

            return web.json_response(data)
        except Exception as e:
            logger.error(f"更新产品时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_delete_product(self, request):
        """删除产品"""
        try:
            # 获取产品ID
            product_id = int(request.match_info.get('product_id', 0))
            if not product_id:
                return web.json_response({"error": "无效的产品ID"}, status=400)

            products = await read_data(PRODUCTS_FILE)

            # 查找产品
            product_index = next((i for i, p in enumerate(products) if p.get('id') == product_id), -1)
            if product_index == -1:
                return web.json_response({"error": f"未找到ID为{product_id}的产品"}, status=404)

            # 删除产品
            deleted_product = products.pop(product_index)

            await write_data(PRODUCTS_FILE, products)
            logger.info(f"成功删除产品: ID={product_id}, 名称={deleted_product.get('name')}")

            return web.json_response({"success": True, "message": f"产品 {deleted_product.get('name')} 已成功删除"})
        except Exception as e:
            logger.error(f"删除产品时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_get_orders(self, request):
        """获取所有订单"""
        orders = await read_data(ORDERS_FILE)
        return web.json_response(orders)

    async def handle_add_order(self, request):
        """添加订单"""
        try:
            data = await request.json()
            orders = await read_data(ORDERS_FILE)

            # 生成订单ID
            order_id = f"LC-{int(time.time())}-{len(orders) + 1}"
            data['id'] = order_id

            # 添加订单时间
            if 'date' not in data:
                data['date'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())

            orders.append(data)
            await write_data(ORDERS_FILE, orders)

            # 更新产品库存
            products = await read_data(PRODUCTS_FILE)
            for item in data.get('items', []):
                product_id = item.get('id')
                quantity = item.get('quantity', 0)

                for product in products:
                    if product.get('id') == product_id:
                        current_stock = product.get('stock', 0)
                        if current_stock > 0:
                            product['stock'] = max(0, current_stock - quantity)

            await write_data(PRODUCTS_FILE, products)

            return web.json_response(data)
        except Exception as e:
            logger.error(f"添加订单时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_get_messages(self, request):
        """获取所有留言"""
        messages = await read_data(MESSAGES_FILE)
        return web.json_response(messages)

    async def handle_add_message(self, request):
        """添加留言"""
        try:
            data = await request.json()
            messages = await read_data(MESSAGES_FILE)

            # 生成留言ID
            message_id = f"MSG-{int(time.time())}-{len(messages) + 1}"
            data['id'] = message_id

            # 添加留言时间
            if 'date' not in data:
                data['date'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())

            messages.append(data)
            await write_data(MESSAGES_FILE, messages)

            return web.json_response(data)
        except Exception as e:
            logger.error(f"添加留言时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_get_sms_records(self, request):
        """获取所有SMS记录"""
        sms_records = await read_data(SMS_RECORDS_FILE)
        return web.json_response(sms_records)

    async def handle_add_sms_record(self, request):
        """添加SMS记录"""
        try:
            data = await request.json()
            sms_records = await read_data(SMS_RECORDS_FILE)

            # 生成记录ID
            record_id = f"SMS-{int(time.time())}-{len(sms_records) + 1}"
            data['id'] = record_id

            # 添加记录时间
            if 'date' not in data:
                data['date'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())

            sms_records.append(data)
            await write_data(SMS_RECORDS_FILE, sms_records)

            return web.json_response(data)
        except Exception as e:
            logger.error(f"添加SMS记录时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_admin_json(self, request):
        """处理管理员凭据请求"""
        try:
            # 获取admin.json文件路径
            admin_file = os.path.join(DATA_DIR, "admin.json")

            # 检查文件是否存在
            if not os.path.exists(admin_file):
                logger.warning(f"管理员凭据文件不存在: {admin_file}")
                return web.Response(status=404, text="File not found: admin.json")

            # 读取文件内容
            with open(admin_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 创建响应
            response = web.Response(text=content, content_type='application/json')

            # 设置缓存控制 - 不缓存敏感信息
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'

            logger.info(f"提供管理员凭据文件")
            return response

        except Exception as e:
            logger.error(f"处理管理员凭据请求时出错: {str(e)}", exc_info=True)
            return web.Response(status=500, text=f"Internal Server Error: {str(e)}")

    async def handle_get_users(self, request):
        """获取所有用户"""
        try:
            users = await read_data(USERS_FILE)
            return web.json_response(users)
        except Exception as e:
            logger.error(f"获取用户时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_check_username(self, request):
        """检查手机号是否存在"""
        try:
            # 获取查询参数
            phone = request.query.get('phone', '')

            if not phone:
                return web.json_response({"error": "Missing phone parameter"}, status=400)

            users = await read_data(USERS_FILE)
            exists = any(user.get('phone') == phone for user in users)

            return web.json_response({"exists": exists})
        except Exception as e:
            logger.error(f"检查手机号时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_register_user(self, request):
        """用户注册"""
        try:
            data = await request.json()

            # 验证必要字段
            required_fields = ['phone', 'name', 'company']
            for field in required_fields:
                if field not in data:
                    return web.json_response({"error": f"Missing required field: {field}"}, status=400)

            users = await read_data(USERS_FILE)

            # 检查手机号是否已存在
            if any(user.get('phone') == data['phone'] for user in users):
                return web.json_response({"error": "手机号已注册，请直接登录"}, status=400)

            # 生成用户ID
            new_id = 1
            if users:
                new_id = max(user.get('id', 0) for user in users) + 1

            # 创建新用户
            new_user = {
                'id': new_id,
                'phone': data['phone'],
                'name': data['name'],
                'company': data['company'],
                'wechat': data.get('wechat', ''),
                'position': data.get('position', ''),
                'address': data.get('address', ''),
                'created_at': time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime()),
                'last_login': None
            }

            users.append(new_user)
            await write_data(USERS_FILE, users)

            # 返回用户信息
            return web.json_response(new_user)
        except Exception as e:
            logger.error(f"用户注册时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_login_user(self, request):
        """用户登录"""
        try:
            data = await request.json()

            # 社交登录
            if 'social_platform' in data and 'social_openid' in data:
                return await self.handle_social_login(data)

            # 手机号登录
            if 'phone' not in data:
                return web.json_response({"error": "请输入手机号码"}, status=400)

            users = await read_data(USERS_FILE)

            # 查找用户
            user = next((user for user in users if user.get('phone') == data['phone']), None)

            if not user:
                return web.json_response({"error": "该手机号未注册，请先注册"}, status=401)

            # 更新最后登录时间
            user['last_login'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
            await write_data(USERS_FILE, users)

            # 返回用户信息
            return web.json_response(user)
        except Exception as e:
            logger.error(f"用户登录时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_social_login(self, data):
        """处理社交登录"""
        try:
            platform = data.get('social_platform')
            openid = data.get('social_openid')

            if not platform or not openid:
                return web.json_response({"error": "缺少社交平台信息"}, status=400)

            users = await read_data(USERS_FILE)

            # 查找是否已存在该社交账号
            user = next((user for user in users if user.get('social_platform') == platform and user.get('social_openid') == openid), None)

            # 如果不存在，创建新用户
            if not user:
                # 生成用户ID
                new_id = 1
                if users:
                    new_id = max(user.get('id', 0) for user in users) + 1

                # 创建新用户
                user = {
                    'id': new_id,
                    'name': data.get('name', f'{platform}用户'),
                    'phone': '',
                    'company': data.get('company', f'{platform}用户'),
                    'avatar': data.get('avatar', ''),
                    'social_platform': platform,
                    'social_openid': openid,
                    'created_at': time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime()),
                    'last_login': time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
                }

                users.append(user)
                await write_data(USERS_FILE, users)
            else:
                # 更新最后登录时间
                user['last_login'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
                await write_data(USERS_FILE, users)

            # 返回用户信息
            return web.json_response(user)
        except Exception as e:
            logger.error(f"社交登录时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_get_social_config(self, request):
        """获取社交登录配置"""
        try:
            # 尝试从配置文件读取
            social_config_path = os.path.join(DATA_DIR, "social_config.json")

            if os.path.exists(social_config_path):
                try:
                    with open(social_config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    logger.info("从配置文件加载社交登录配置")
                except Exception as e:
                    logger.error(f"读取社交登录配置文件失败: {str(e)}")
                    # 使用默认配置
                    config = self._get_default_social_config()
            else:
                # 如果配置文件不存在，使用默认配置
                logger.warning(f"社交登录配置文件不存在: {social_config_path}")
                config = self._get_default_social_config()

            # 确保配置中不包含敏感信息
            if "wechat" in config and "app_secret" in config["wechat"]:
                del config["wechat"]["app_secret"]
            if "qq" in config and "app_secret" in config["qq"]:
                del config["qq"]["app_secret"]

            return web.json_response(config)
        except Exception as e:
            logger.error(f"获取社交登录配置时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_wechat_pay_create_order(self, request):
        """创建微信支付订单"""
        try:
            # 检查微信支付模块是否可用
            if wechat_pay is None:
                return web.json_response({"error": "微信支付功能未启用"}, status=400)

            # 获取订单数据
            data = await request.json()

            # 验证必要字段
            if 'order_id' not in data:
                # 如果没有提供订单ID，尝试获取订单数据并创建订单
                if 'items' not in data or not data['items']:
                    return web.json_response({"error": "缺少订单项"}, status=400)

                # 创建订单
                orders = await read_data(ORDERS_FILE)

                # 生成订单ID
                order_id = f"LC-{int(time.time())}-{len(orders) + 1}"
                data['id'] = order_id

                # 添加订单时间
                if 'date' not in data:
                    data['date'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())

                # 设置订单状态为等待支付
                data['status'] = 'waiting_payment'

                # 保存订单
                orders.append(data)
                await write_data(ORDERS_FILE, orders)

                # 更新产品库存
                products = await read_data(PRODUCTS_FILE)
                for item in data.get('items', []):
                    product_id = item.get('id')
                    quantity = item.get('quantity', 0)

                    for product in products:
                        if product.get('id') == product_id:
                            current_stock = product.get('stock', 0)
                            if current_stock > 0:
                                product['stock'] = max(0, current_stock - quantity)

                await write_data(PRODUCTS_FILE, products)

                order_data = data
            else:
                # 如果提供了订单ID，查找现有订单
                order_id = data['order_id']
                orders = await read_data(ORDERS_FILE)
                order_data = next((order for order in orders if order.get('id') == order_id), None)

                if not order_data:
                    return web.json_response({"error": f"未找到订单: {order_id}"}, status=404)

            # 创建微信支付订单
            result = wechat_pay.create_order(order_data)

            if result['success']:
                # 更新订单状态
                orders = await read_data(ORDERS_FILE)
                for order in orders:
                    if order.get('id') == order_data['id']:
                        order['payment'] = {
                            'method': 'wechat',
                            'code_url': result['code_url'],
                            'status': 'pending',
                            'created_at': time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
                        }
                        break

                await write_data(ORDERS_FILE, orders)

                return web.json_response({
                    "success": True,
                    "order_id": order_data['id'],
                    "code_url": result['code_url']
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get('error', '创建微信支付订单失败')
                }, status=400)

        except Exception as e:
            logger.error(f"创建微信支付订单时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_wechat_pay_query_order(self, request):
        """查询微信支付订单状态"""
        try:
            # 检查微信支付模块是否可用
            if wechat_pay is None:
                return web.json_response({"error": "微信支付功能未启用"}, status=400)

            # 获取订单ID
            order_id = request.query.get('order_id')
            if not order_id:
                return web.json_response({"error": "缺少订单ID"}, status=400)

            # 查询订单
            orders = await read_data(ORDERS_FILE)
            order_data = next((order for order in orders if order.get('id') == order_id), None)

            if not order_data:
                return web.json_response({"error": f"未找到订单: {order_id}"}, status=404)

            # 如果订单已支付，直接返回成功
            if order_data.get('status') == 'paid':
                return web.json_response({
                    "success": True,
                    "order_id": order_id,
                    "status": "paid",
                    "message": "订单已支付"
                })

            # 查询微信支付订单状态
            result = wechat_pay.query_order(out_trade_no=order_id)

            if result['success']:
                payment_data = result['data']

                # 更新订单状态
                if payment_data.get('trade_state') == 'SUCCESS':
                    # 订单已支付
                    for order in orders:
                        if order.get('id') == order_id:
                            order['status'] = 'paid'
                            order['payment']['status'] = 'success'
                            order['payment']['paid_at'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
                            order['payment']['transaction_id'] = payment_data.get('transaction_id')
                            break

                    await write_data(ORDERS_FILE, orders)

                    return web.json_response({
                        "success": True,
                        "order_id": order_id,
                        "status": "paid",
                        "message": "订单已支付"
                    })
                else:
                    # 订单未支付
                    return web.json_response({
                        "success": True,
                        "order_id": order_id,
                        "status": "pending",
                        "message": "订单等待支付"
                    })
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get('error', '查询微信支付订单失败')
                }, status=400)

        except Exception as e:
            logger.error(f"查询微信支付订单时出错: {str(e)}", exc_info=True)
            return web.json_response({"error": str(e)}, status=400)

    async def handle_wechat_pay_notify(self, request):
        """处理微信支付通知"""
        try:
            # 检查微信支付模块是否可用
            if wechat_pay is None:
                return web.Response(status=500, text="微信支付功能未启用")

            # 获取通知数据
            data = await request.read()
            headers = request.headers

            # 验证通知
            if not wechat_pay.verify_notify(headers, data):
                return web.Response(status=400, text="通知验证失败")

            # 解析通知数据
            notify_data = json.loads(data)

            # 获取商户订单号
            out_trade_no = notify_data.get('resource', {}).get('ciphertext', {}).get('out_trade_no')
            if not out_trade_no:
                return web.Response(status=400, text="缺少商户订单号")

            # 更新订单状态
            orders = await read_data(ORDERS_FILE)
            order_updated = False

            for order in orders:
                if order.get('id') == out_trade_no:
                    order['status'] = 'paid'
                    if 'payment' not in order:
                        order['payment'] = {}
                    order['payment']['status'] = 'success'
                    order['payment']['paid_at'] = time.strftime("%Y-%m-%dT%H:%M:%S", time.localtime())
                    order['payment']['transaction_id'] = notify_data.get('resource', {}).get('ciphertext', {}).get('transaction_id')
                    order_updated = True
                    break

            if order_updated:
                await write_data(ORDERS_FILE, orders)

                # 返回成功
                return web.Response(
                    text=json.dumps({"code": "SUCCESS", "message": "成功"}),
                    content_type="application/json"
                )
            else:
                return web.Response(status=404, text=f"未找到订单: {out_trade_no}")

        except Exception as e:
            logger.error(f"处理微信支付通知时出错: {str(e)}", exc_info=True)
            return web.Response(status=500, text=f"Internal Server Error: {str(e)}")

    async def handle_wechat_work_notify(self, request):
        """处理企业微信通知请求"""
        try:
            data = await request.json()

            # 企业微信webhook URL
            webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d5b6e5ec-d39e-4e17-9bba-3edaa5e97c1d"

            # 格式化订单消息
            message = self.format_order_message(data)

            # 发送到企业微信
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=message) as response:
                    result = await response.json()

                    if result.get('errcode') == 0:
                        logger.info("企业微信通知发送成功")
                        return web.json_response({"success": True, "message": "通知发送成功"})
                    else:
                        logger.error(f"企业微信通知发送失败: {result}")
                        return web.json_response({"success": False, "error": result.get('errmsg', '未知错误')}, status=400)

        except Exception as e:
            logger.error(f"发送企业微信通知时出错: {str(e)}", exc_info=True)
            return web.json_response({"success": False, "error": str(e)}, status=500)

    async def handle_wechat_work_test(self, request):
        """测试企业微信通知连接"""
        try:
            # 企业微信webhook URL
            webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d5b6e5ec-d39e-4e17-9bba-3edaa5e97c1d"

            # 测试消息
            test_message = {
                "msgtype": "text",
                "text": {
                    "content": "🔔 龙驰新材料订单系统测试消息\n\n系统运行正常，企业微信通知功能已启用！"
                }
            }

            # 发送测试消息
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=test_message) as response:
                    result = await response.json()

                    if result.get('errcode') == 0:
                        logger.info("企业微信测试通知发送成功")
                        return web.json_response({"success": True, "message": "测试通知发送成功"})
                    else:
                        logger.error(f"企业微信测试通知发送失败: {result}")
                        return web.json_response({"success": False, "error": result.get('errmsg', '未知错误')}, status=400)

        except Exception as e:
            logger.error(f"发送企业微信测试通知时出错: {str(e)}", exc_info=True)
            return web.json_response({"success": False, "error": str(e)}, status=500)

    def format_order_message(self, order_data):
        """格式化订单消息"""
        customer = order_data.get('customer', {})
        items = order_data.get('items', [])
        total = order_data.get('total', 0)

        # 格式化商品列表
        items_text = '\n'.join([
            f"• {item.get('name', '未知商品')} × {item.get('quantity', 0)} = ¥{(item.get('price', 0) * item.get('quantity', 0)):.2f}"
            for item in items
        ])

        # 格式化时间
        order_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if 'date' in order_data:
            try:
                order_time = datetime.datetime.fromisoformat(order_data['date'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass

        content = f"""🎉 **新订单通知**

📋 **订单信息**
订单号：{order_data.get('id', '未知')}
下单时间：{order_time}
支付方式：微信支付
订单状态：等待支付确认

👤 **客户信息**
姓名：{customer.get('name', '未提供')}
电话：{customer.get('phone', '未提供')}
公司：{customer.get('company', '未提供')}
职位：{customer.get('position', '未提供')}
微信：{customer.get('wechat', '未提供')}
地址：{customer.get('address', '未提供')}

🛒 **商品清单**
{items_text}

💰 **订单金额**
总计：¥{total:.2f}

⚠️ **处理提醒**
客户已确认完成微信支付，请及时确认收款并处理订单。

🔗 **管理链接**
[查看订单详情](http://localhost:8080/lc-mall/admin/orders.html)"""

        return {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }

    async def start(self):
        """启动服务器"""
        # 初始化数据文件
        init_data_files()

        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)

        logger.info(f"启动增强型服务器 - http://{self.host}:{self.port}/")
        logger.info(f"根目录: {self.root_dir}")
        logger.info(f"API端点: http://{self.host}:{self.port}/api/")

        await site.start()

        # 保持服务器运行
        while True:
            await asyncio.sleep(3600)  # 每小时检查一次

    def _get_default_social_config(self):
        """获取默认的社交登录配置"""
        return {
            "wechat": {
                "enabled": True,
                "app_id": os.environ.get('WECHAT_APP_ID', 'qq_app_id_placeholder'),
                "redirect_uri": os.environ.get('WECHAT_REDIRECT_URI', 'http://localhost:8000/auth/wechat/callback')
            },
            "qq": {
                "enabled": True,
                "app_id": os.environ.get('QQ_APP_ID', 'qq_app_id_placeholder'),
                "redirect_uri": os.environ.get('QQ_REDIRECT_URI', 'http://localhost:8000/auth/qq/callback')
            }
        }

    async def start(self):
        """启动服务器"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)

        logger.info(f"高性能服务器启动 - http://{self.host}:{self.port}/")
        logger.info(f"根目录: {self.root_dir}")

        await site.start()

        # 保持服务器运行
        while True:
            await asyncio.sleep(3600)  # 每小时检查一次

    def run(self):
        """运行服务器"""
        try:
            asyncio.run(self.start())
        except KeyboardInterrupt:
            logger.info("服务器已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强型Web服务器 - 支持静态文件和API")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--root", help="网站根目录")
    args = parser.parse_args()

    server = EnhancedServer(host=args.host, port=args.port, root_dir=args.root)
    server.run()

if __name__ == "__main__":
    main()
