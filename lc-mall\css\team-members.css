/* 团队成员样式 */

/* 团队成员卡片样式 */
.team-members {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.team-member {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* 团队成员照片样式 */
.member-photo {
    position: relative;
    height: 300px;
    overflow: hidden;
    background-color: #f0f5ff;
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.team-member:hover .member-photo img {
    transform: scale(1.05);
}

/* 团队成员信息样式 */
.member-info {
    padding: 25px;
    position: relative;
    z-index: 1;
}

.member-info h3 {
    font-size: 22px;
    color: #333;
    margin: 0 0 5px;
    font-weight: 600;
}

.member-position {
    font-size: 16px;
    color: #0066cc;
    margin: 0 0 15px;
    font-weight: 500;
    display: inline-block;
    padding: 5px 12px;
    background-color: rgba(0, 102, 204, 0.1);
    border-radius: 20px;
}

.member-description {
    font-size: 15px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* 联系信息样式 */
.member-contact {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.member-contact a {
    display: flex;
    align-items: center;
    color: #555;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.member-contact a:hover {
    color: #0066cc;
}

.member-contact i {
    margin-right: 10px;
    color: #0066cc;
    width: 20px;
    text-align: center;
}

/* 职位图标 */
.team-member::before {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #0066cc;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    z-index: 2;
    opacity: 0.9;
    transition: transform 0.3s ease;
}

.team-member.ceo::before {
    content: '\f521';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    background-color: #e74c3c;
}

.team-member.tech::before {
    content: '\f0c3';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    background-color: #3498db;
}

.team-member.sales::before {
    content: '\f201';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    background-color: #2ecc71;
}

/* 默认图片样式 */
.team-member.ceo .member-photo {
    background: linear-gradient(135deg, #ff9966, #ff5e62);
}

.team-member.tech .member-photo {
    background: linear-gradient(135deg, #56ccf2, #2f80ed);
}

.team-member.sales .member-photo {
    background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .team-members {
        grid-template-columns: 1fr;
    }
    
    .member-photo {
        height: 250px;
    }
}
