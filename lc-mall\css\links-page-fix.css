/* 友情链接页面优化样式 */

/* 链接项目样式优化 */
.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.link-item {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #f0f0f0;
    text-align: center;
}

.link-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.link-item a {
    display: block;
    padding: 25px;
    text-decoration: none;
}

.link-item img {
    width: 100%;
    max-width: 200px;
    height: auto;
    margin-bottom: 15px;
    transition: transform 0.5s ease;
}

.link-item:hover img {
    transform: scale(1.05);
}

.link-item h4 {
    font-size: 18px;
    color: #333;
    margin: 0;
    transition: color 0.3s ease;
}

.link-item:hover h4 {
    color: #0066cc;
}

/* 链接分类标题样式 */
.links-category {
    margin-bottom: 50px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .links-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .link-item a {
        padding: 15px;
    }
    
    .link-item h4 {
        font-size: 16px;
    }
}
