/* 公司位置部分样式优化 */

/* 位置信息容器 */
.location-section {
    padding: 60px 0;
    background-color: #f8f9fa;
    margin-top: 50px;
}

.location-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.location-info > div {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    align-items: flex-start;
}

.location-info > div:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.location-info i {
    font-size: 24px;
    color: #0066cc;
    margin-right: 20px;
    margin-top: 5px;
}

.location-info h3 {
    font-size: 20px;
    color: #333;
    margin: 0 0 15px;
    font-weight: 600;
}

.location-info p {
    font-size: 16px;
    color: #666;
    margin: 0 0 10px;
    line-height: 1.6;
}

.location-info p:last-child {
    margin-bottom: 0;
}

/* 地图容器样式 */
.map-container {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 50px;
}

.map-container iframe {
    display: block;
    border-radius: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .location-info {
        grid-template-columns: 1fr;
    }
    
    .location-info > div {
        padding: 20px;
    }
    
    .map-container {
        margin-bottom: 30px;
    }
    
    .map-container iframe {
        height: 300px;
    }
}
