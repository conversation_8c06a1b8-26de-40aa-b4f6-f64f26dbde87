/* 新的底部样式 */
.footer-new {
    background-color: #1a2a3a;
    color: #fff;
    padding: 0;
    width: 100%;
}

.footer-nav {
    max-width: 1280px;
    margin: 0 auto;
    padding: 15px 20px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.footer-nav a {
    color: #fff;
    text-decoration: none;
    padding: 10px 20px;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-nav a:hover {
    color: #4a90e2;
}

.footer-info {
    max-width: 1280px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.company-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.company-info p {
    margin: 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
}

.company-info a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
}

.company-info a:hover {
    color: #4a90e2;
}

.qrcode-container {
    text-align: center;
}

.qrcode-container img {
    width: 120px;
    height: 120px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.qrcode-container p {
    margin-top: 5px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.copyright {
    width: 100%;
    text-align: center;
    padding: 15px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 20px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .footer-info {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .company-info {
        align-items: center;
    }
    
    .footer-nav {
        justify-content: center;
    }
    
    .footer-nav a {
        padding: 8px 12px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .footer-nav a {
        padding: 6px 8px;
        font-size: 12px;
    }
}
