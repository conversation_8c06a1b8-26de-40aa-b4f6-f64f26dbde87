/* 社交登录样式 */

.social-login {
    margin-top: 20px;
    text-align: center;
}

.social-login-title {
    position: relative;
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-login-title::before,
.social-login-title::after {
    content: "";
    flex-grow: 1;
    height: 1px;
    background-color: #ddd;
    margin: 0 10px;
}

.social-login-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.social-login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 15px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.social-login-btn i {
    margin-right: 8px;
    font-size: 18px;
}

.wechat-btn {
    background-color: #07C160;
}

.wechat-btn:hover {
    background-color: #06ad55;
}

.qq-btn {
    background-color: #12B7F5;
}

.qq-btn:hover {
    background-color: #0fa3d9;
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .social-login-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .social-login-btn {
        width: 100%;
    }
}
