/* 关于我们页面优化样式 */

/* 移除公司位置下面的装饰线 */
.section-title h2:after {
    display: none !important;
}

/* 优化关于我们页面的展示 */
.about-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 40px;
    align-items: flex-start;
}

.about-image {
    flex: 0 0 40%;
    max-width: 40%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.about-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.about-image:hover img {
    transform: scale(1.03);
}

.about-text {
    flex: 0 0 calc(60% - 30px);
    max-width: calc(60% - 30px);
}

.about-text h3 {
    font-size: 24px;
    color: #333;
    margin: 0 0 20px;
    position: relative;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.about-text p {
    font-size: 16px;
    line-height: 1.7;
    color: #555;
    margin-bottom: 20px;
    text-align: justify;
}

.about-text p:last-child {
    margin-bottom: 0;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-top: 40px;
}

.feature-item {
    background-color: #fff;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #f0f0f0;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: #f5f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: #0066cc;
    font-size: 24px;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    background-color: #0066cc;
    color: #fff;
}

.feature-content h3 {
    font-size: 18px;
    color: #333;
    margin: 0 0 15px;
}

.feature-content p {
    font-size: 15px;
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* 团队成员样式优化 */
.team-members {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.team-member {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
    border: 1px solid #f0f0f0;
}

.team-member:hover {
    transform: translateY(-8px);
}

.member-photo {
    overflow: hidden;
}

.member-photo img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.team-member:hover .member-photo img {
    transform: scale(1.05);
}

.member-info {
    padding: 20px;
}

.member-info h3 {
    font-size: 20px;
    color: #333;
    margin: 0 0 5px;
}

.member-position {
    font-size: 14px;
    color: #0066cc;
    margin: 0 0 15px;
    font-weight: 600;
}

.member-description {
    font-size: 15px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.member-contact {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.member-contact a {
    display: flex;
    align-items: center;
    color: #555;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.member-contact a:hover {
    color: #0066cc;
}

.member-contact i {
    margin-right: 8px;
    color: #0066cc;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .about-image, .about-text {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .about-image {
        margin-bottom: 20px;
    }
    
    .team-members {
        grid-template-columns: 1fr;
    }
}
