/* 产品标题行样式 */
.product-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
}

.product-title-row h3,
.product-title-row h2 {
    margin: 0;
    padding-right: 10px;
    flex: 1;
}

.product-stock {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;
}

.product-stock .stock-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

/* 库存状态颜色 */
.in-stock {
    color: #10b981;
    background-color: rgba(16, 185, 129, 0.1);
}

.in-stock .stock-indicator {
    background-color: #10b981;
}

.low-stock {
    color: #f59e0b;
    background-color: rgba(245, 158, 11, 0.1);
}

.low-stock .stock-indicator {
    background-color: #f59e0b;
}

.out-of-stock {
    color: #ef4444;
    background-color: rgba(239, 68, 68, 0.1);
}

.out-of-stock .stock-indicator {
    background-color: #ef4444;
}

/* 产品详情模态框中的库存状态 */
.product-modal-info .product-stock {
    margin-bottom: 1rem;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-in-stock {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.status-low-stock {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.status-out-of-stock {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* 价格和购物车按钮行 */
.price-cart-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.price {
    font-size: 1.25rem;
    font-weight: bold;
    color: #ff3366;
}

/* 使整个产品卡片可点击 */
.product-card {
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .product-title-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .product-title-row h3,
    .product-title-row h2 {
        margin-bottom: 0.5rem;
        padding-right: 0;
    }

    .product-stock {
        margin-bottom: 0.5rem;
    }

    .price-cart-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .price {
        margin-bottom: 0.5rem;
    }
}
