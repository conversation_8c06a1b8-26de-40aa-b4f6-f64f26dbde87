/* 微信支付样式 */

/* 支付方式选择 */
.payment-methods {
    margin: 20px 0;
    display: flex;
    justify-content: center;
}

.payment-method {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border: 2px solid #07c160;
    border-radius: 8px;
    background: #f0f9ff;
    box-shadow: 0 2px 8px rgba(7, 193, 96, 0.2);
    cursor: default;
}

.payment-method img {
    width: 24px;
    height: 24px;
    margin-right: 10px;
}

.payment-method span {
    font-weight: 500;
    color: #07c160;
    font-size: 16px;
}

/* 微信支付模态框 */
.wechat-pay-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease;
}

.wechat-pay-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 30px;
    border-radius: 8px;
    width: 350px;
    max-width: 90%;
    text-align: center;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease;
}

.wechat-pay-content h2 {
    color: #333;
    margin-bottom: 20px;
}

.wechat-pay-content p {
    color: #666;
    margin-bottom: 20px;
}

.wechat-pay-content .close {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
}

.wechat-pay-content .close:hover {
    color: #333;
}

#wechat-pay-qrcode {
    margin: 0 auto 20px;
    padding: 10px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: inline-block;
}

.payment-status {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.payment-status p {
    margin: 0 0 10px;
    color: #333;
}

/* 加载动画 */
.spinner {
    width: 30px;
    height: 30px;
    margin: 0 auto;
    border: 3px solid rgba(0, 136, 255, 0.2);
    border-top-color: #0088ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 响应式样式 */
@media (max-width: 768px) {
    .wechat-pay-content {
        width: 300px;
        padding: 20px;
        margin: 20% auto;
    }

    #wechat-pay-qrcode {
        transform: scale(0.9);
    }
}

/* 支付按钮 */
.pay-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #0088ff;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 20px;
}

.pay-button:hover {
    background-color: #0066cc;
}

.pay-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}
