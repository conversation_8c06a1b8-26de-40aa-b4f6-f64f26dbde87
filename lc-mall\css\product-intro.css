/* 产品介绍页面样式 */
.product-intro-section {
    padding: 20px 0;
    background-color: #f9f9f9;
}

.product-categories {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.category-card {
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.category-header {
    background-color: #0088ff;
    color: #fff;
    padding: 15px;
    text-align: center;
}

.category-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.category-content {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.category-content p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
}

.category-content ul {
    margin: 0;
    padding-left: 20px;
}

.category-content li {
    margin-bottom: 8px;
    color: #555;
}

/* 产品详情表格样式 */
.product-details {
    margin-top: 30px;
    margin-bottom: 30px;
}

.product-details h2 {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #0088ff;
}

.product-table {
    overflow-x: auto;
}

.product-table table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ddd;
}

.product-table th {
    background-color: #f2f2f2;
    padding: 12px 15px;
    text-align: center;
    font-weight: 500;
    color: #333;
    border: 1px solid #ddd;
}

.product-table td {
    padding: 12px 15px;
    text-align: center;
    border: 1px solid #ddd;
    color: #555;
}

.product-table .product-name {
    font-weight: 500;
    background-color: #f9f9f9;
}

/* 响应式布局 */
@media (max-width: 992px) {
    .product-categories {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .product-categories {
        grid-template-columns: 1fr;
    }

    .product-table {
        font-size: 14px;
    }

    .product-table th,
    .product-table td {
        padding: 8px 10px;
    }
}

/* 编辑按钮样式 */
.edit-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card {
    position: relative;
}

.category-card:hover .edit-button {
    opacity: 1;
}

.table-edit-button {
    margin-left: 15px;
    background-color: #0088ff;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
    vertical-align: middle;
}

/* 编辑模态框样式 */
.edit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.edit-modal-content {
    background-color: #fff;
    border-radius: 5px;
    padding: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.table-edit-modal {
    max-width: 800px;
}

.close-modal {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.close-modal:hover {
    color: #333;
}

.edit-modal h2 {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-actions {
    margin-top: 20px;
    text-align: right;
}

.save-btn,
.cancel-btn {
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
}

.save-btn {
    background-color: #0088ff;
    color: #fff;
    border: none;
}

.cancel-btn {
    background-color: #f2f2f2;
    color: #333;
    border: 1px solid #ddd;
}

/* 表格编辑样式 */
.edit-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

.edit-table th,
.edit-table td {
    border: 1px solid #ddd;
    padding: 8px;
}

.edit-table th {
    background-color: #f2f2f2;
    text-align: center;
}

.edit-table input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.delete-row-btn {
    background-color: #ff3333;
    color: #fff;
    border: none;
    border-radius: 3px;
    padding: 5px 8px;
    cursor: pointer;
}

#add-row-btn {
    background-color: #0088ff;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    margin-bottom: 15px;
    cursor: pointer;
}
