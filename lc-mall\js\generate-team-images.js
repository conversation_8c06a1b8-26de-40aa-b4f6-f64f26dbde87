/**
 * 生成团队成员默认图片
 * 这个脚本会在页面加载时检查团队成员图片是否存在，如果不存在则生成默认图片
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有团队成员图片
    const teamImages = document.querySelectorAll('.team-member .member-photo img');
    
    // 为每个图片添加错误处理
    teamImages.forEach(img => {
        img.addEventListener('error', function() {
            const memberType = this.closest('.team-member').classList.contains('ceo') ? 'ceo' : 
                              this.closest('.team-member').classList.contains('tech') ? 'tech' : 
                              this.closest('.team-member').classList.contains('sales') ? 'sales' : 'default';
            
            // 获取成员名称和职位
            const memberName = this.closest('.team-member').querySelector('h3').textContent;
            const memberPosition = this.closest('.team-member').querySelector('.member-position').textContent;
            
            // 创建一个canvas元素来生成图片
            const canvas = document.createElement('canvas');
            canvas.width = 300;
            canvas.height = 300;
            const ctx = canvas.getContext('2d');
            
            // 设置背景颜色
            let gradient;
            switch(memberType) {
                case 'ceo':
                    gradient = ctx.createLinearGradient(0, 0, 300, 300);
                    gradient.addColorStop(0, '#ff9966');
                    gradient.addColorStop(1, '#ff5e62');
                    break;
                case 'tech':
                    gradient = ctx.createLinearGradient(0, 0, 300, 300);
                    gradient.addColorStop(0, '#56ccf2');
                    gradient.addColorStop(1, '#2f80ed');
                    break;
                case 'sales':
                    gradient = ctx.createLinearGradient(0, 0, 300, 300);
                    gradient.addColorStop(0, '#a1c4fd');
                    gradient.addColorStop(1, '#c2e9fb');
                    break;
                default:
                    gradient = ctx.createLinearGradient(0, 0, 300, 300);
                    gradient.addColorStop(0, '#8e9eab');
                    gradient.addColorStop(1, '#eef2f3');
            }
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 300, 300);
            
            // 添加职位图标
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.beginPath();
            ctx.arc(150, 100, 50, 0, Math.PI * 2);
            ctx.fill();
            
            // 添加文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(memberName, 150, 200);
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.font = '18px Arial';
            ctx.fillText(memberPosition, 150, 230);
            
            // 添加公司名称
            ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.font = '14px Arial';
            ctx.fillText('广州市龙驰新材料科技有限公司', 150, 260);
            
            // 将canvas转换为图片URL
            const dataUrl = canvas.toDataURL('image/png');
            
            // 设置图片源
            this.src = dataUrl;
        });
    });
});
