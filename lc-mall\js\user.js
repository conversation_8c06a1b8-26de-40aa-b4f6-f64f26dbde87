/**
 * 用户管理模块
 * 处理用户注册、登录、登出和会话管理
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('用户模块初始化...');

    // DOM元素
    const userStatusContainer = document.getElementById('user-status');
    const loginModal = document.getElementById('login-modal');
    const registerModal = document.getElementById('register-modal');
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    // 初始化 - 检查用户登录状态
    updateUserStatus();

    // 直接在文档上添加事件委托，处理登录按钮点击
    document.addEventListener('click', function(event) {
        // 登录按钮
        if (event.target.id === 'login-btn' || event.target.closest('#login-btn')) {
            console.log('登录按钮被点击');
            openLoginModal();
        }

        // 注册按钮
        if (event.target.id === 'register-btn' || event.target.closest('#register-btn')) {
            console.log('注册按钮被点击');
            openRegisterModal();
        }

        // 登出按钮
        if (event.target.id === 'logout-btn' || event.target.closest('#logout-btn')) {
            console.log('登出按钮被点击');
            logoutUser();
        }

        // 切换到注册
        if (event.target.id === 'switch-to-register' || event.target.closest('#switch-to-register')) {
            console.log('切换到注册');
            closeLoginModal();
            openRegisterModal();
        }

        // 切换到登录
        if (event.target.id === 'switch-to-login' || event.target.closest('#switch-to-login')) {
            console.log('切换到登录');
            closeRegisterModal();
            openLoginModal();
        }
    });

    // 关闭模态框按钮
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal.id === 'login-modal') {
                closeLoginModal();
            } else if (modal.id === 'register-modal') {
                closeRegisterModal();
            }
        });
    });

    // 点击模态框外部关闭
    window.addEventListener('click', function(e) {
        if (e.target === loginModal) {
            closeLoginModal();
        }
        if (e.target === registerModal) {
            closeRegisterModal();
        }
    });

    // 登录表单提交
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const phone = document.getElementById('login-phone').value;

            // 显示加载中提示
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
            submitBtn.disabled = true;

            try {
                // 调用API进行登录
                const user = await ApiClient.loginUser({ phone });

                // 登录成功
                showNotification(`登录成功，欢迎回来，${user.name}！`, 'success');

                // 更新用户状态
                updateUserStatus();

                // 关闭模态框
                closeLoginModal();

                // 重置表单
                loginForm.reset();
            } catch (error) {
                // 登录失败
                showNotification(error.message || '登录失败，请检查手机号', 'error');
            } finally {
                // 恢复按钮状态
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            }
        });
    }

    // 注册表单提交
    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const phone = document.getElementById('register-phone').value;
            const name = document.getElementById('register-name').value;
            const wechat = document.getElementById('register-wechat').value;
            const company = document.getElementById('register-company').value;
            const position = document.getElementById('register-position').value;
            const address = document.getElementById('register-address').value;

            // 验证手机号格式
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                showNotification('请输入正确的手机号码', 'error');
                return;
            }

            // 显示加载中提示
            const submitBtn = registerForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';
            submitBtn.disabled = true;

            try {
                // 调用API进行注册
                const userData = {
                    phone,
                    name,
                    wechat,
                    company,
                    position,
                    address
                };

                const user = await ApiClient.registerUser(userData);

                // 注册成功
                showNotification(`注册成功，欢迎加入，${user.name}！`, 'success');

                // 更新用户状态
                updateUserStatus();

                // 关闭模态框
                closeRegisterModal();

                // 重置表单
                registerForm.reset();
            } catch (error) {
                // 注册失败
                showNotification(error.message || '注册失败，请稍后重试', 'error');
            } finally {
                // 恢复按钮状态
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            }
        });
    }
});

// 更新用户状态显示
function updateUserStatus() {
    console.log('更新用户状态...');
    const userStatusContainer = document.getElementById('user-status');
    if (!userStatusContainer) {
        console.log('未找到用户状态容器');
        return;
    }

    const loginStatus = ApiClient.checkUserLogin();
    console.log('当前登录状态:', loginStatus);

    if (loginStatus.loggedIn) {
        // 用户已登录
        const userCompany = loginStatus.user.company ? ` (${loginStatus.user.company})` : '';
        const socialIcon = loginStatus.user.social_platform === 'wechat' ? 'fa-weixin' :
                          loginStatus.user.social_platform === 'qq' ? 'fa-qq' : 'fa-user-circle';

        userStatusContainer.innerHTML = `
            <span class="user-welcome" style="color: white !important;">
                <i class="fab ${socialIcon}"></i>
                <span style="color: white !important; font-weight: 500;">${loginStatus.user.name}${userCompany}</span>
            </span>
            <button id="logout-btn" class="btn btn-sm" style="color: white !important; font-size: 14px !important; font-weight: 500 !important; background-color: #1a73e8 !important;">
                <span style="color: white !important;">退出</span>
            </button>
        `;
    } else {
        // 用户未登录
        userStatusContainer.innerHTML = `
            <button id="login-btn" class="btn btn-sm">登录</button>
            <div class="register-btn-container">
                <div class="register-bubble">注册可免费领取一份50g样品</div>
                <button id="register-btn" class="btn btn-sm btn-outline">注册</button>
            </div>
        `;
    }
}

// 打开登录模态框
function openLoginModal() {
    console.log('打开登录模态框');
    const loginModal = document.getElementById('login-modal');
    if (loginModal) {
        // 先设置display为block
        loginModal.style.display = 'block';

        // 强制重绘
        loginModal.offsetHeight;

        // 添加show类以触发动画
        loginModal.classList.add('show');

        // 获取模态框内容元素
        const modalContent = loginModal.querySelector('.modal-content');
        if (modalContent) {
            // 强制重绘
            modalContent.offsetHeight;

            // 添加show类以触发内容动画
            modalContent.style.opacity = '1';
            modalContent.style.transform = 'translateY(0)';
        }

        console.log('登录模态框已显示');
    } else {
        console.error('未找到登录模态框元素');
    }
}

// 关闭登录模态框
function closeLoginModal() {
    console.log('关闭登录模态框');
    const loginModal = document.getElementById('login-modal');
    if (loginModal) {
        // 移除show类以触发关闭动画
        loginModal.classList.remove('show');

        // 获取模态框内容元素
        const modalContent = loginModal.querySelector('.modal-content');
        if (modalContent) {
            // 重置内容样式
            modalContent.style.opacity = '0';
            modalContent.style.transform = 'translateY(-20px)';
        }

        // 延迟隐藏模态框，等待动画完成
        setTimeout(() => {
            loginModal.style.display = 'none';
        }, 300);
    }
}

// 打开注册模态框
function openRegisterModal() {
    console.log('打开注册模态框');
    const registerModal = document.getElementById('register-modal');
    if (registerModal) {
        // 先设置display为block
        registerModal.style.display = 'block';

        // 强制重绘
        registerModal.offsetHeight;

        // 添加show类以触发动画
        registerModal.classList.add('show');

        // 获取模态框内容元素
        const modalContent = registerModal.querySelector('.modal-content');
        if (modalContent) {
            // 强制重绘
            modalContent.offsetHeight;

            // 添加show类以触发内容动画
            modalContent.style.opacity = '1';
            modalContent.style.transform = 'translateY(0)';
        }

        console.log('注册模态框已显示');
    } else {
        console.error('未找到注册模态框元素');
    }
}

// 关闭注册模态框
function closeRegisterModal() {
    console.log('关闭注册模态框');
    const registerModal = document.getElementById('register-modal');
    if (registerModal) {
        // 移除show类以触发关闭动画
        registerModal.classList.remove('show');

        // 获取模态框内容元素
        const modalContent = registerModal.querySelector('.modal-content');
        if (modalContent) {
            // 重置内容样式
            modalContent.style.opacity = '0';
            modalContent.style.transform = 'translateY(-20px)';
        }

        // 延迟隐藏模态框，等待动画完成
        setTimeout(() => {
            registerModal.style.display = 'none';
        }, 300);
    }
}

// 用户登出
function logoutUser() {
    ApiClient.logoutUser();
    updateUserStatus();
    showNotification('您已成功退出登录', 'info');
}

// 显示通知
function showNotification(message, type = 'info') {
    // 如果已有showNotification函数，则使用它
    if (window.showNotification) {
        window.showNotification(message, type);
        return;
    }

    // 否则创建一个简单的通知
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 3000);
}
