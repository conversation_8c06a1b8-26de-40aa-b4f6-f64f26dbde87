/* 公司介绍文本排版优化 */

/* 标题样式 */
.about-text h3 {
    font-size: 24px;
    color: #333;
    margin: 0 0 20px;
    letter-spacing: 1px; /* 标题字间距 */
    line-height: 1.4;
    font-weight: 600;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 15px;
}

/* 段落样式 */
.about-text p {
    font-size: 16px;
    line-height: 1.8; /* 增加行高 */
    color: #555;
    margin-bottom: 20px;
    text-align: justify;
    letter-spacing: 0.5px; /* 增加字间距 */
    word-spacing: 2px; /* 增加词间距 */
    text-indent: 2em; /* 首行缩进 */
}

/* 强调文本 */
.about-text strong, 
.about-text b {
    font-weight: 600;
    color: #333;
}

/* 引用文本 */
.about-text em,
.about-text i {
    font-style: italic;
    color: #0066cc;
}

/* 关键词高亮 */
.keyword {
    color: #0066cc;
    font-weight: 500;
}

/* 技术名词样式 */
.tech-term {
    font-family: "Microsoft YaHei", sans-serif;
    background-color: rgba(0, 102, 204, 0.05);
    padding: 0 5px;
    border-radius: 3px;
    font-weight: 500;
}

/* 公司特色列表 */
.company-features {
    margin: 20px 0;
    padding-left: 20px;
}

.company-features li {
    margin-bottom: 10px;
    position: relative;
    padding-left: 20px;
    list-style-type: none;
}

.company-features li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #0066cc;
    font-weight: bold;
    font-size: 18px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .about-text h3 {
        font-size: 20px;
        letter-spacing: 0.5px;
    }
    
    .about-text p {
        font-size: 15px;
        line-height: 1.7;
        letter-spacing: 0.3px;
        word-spacing: 1px;
    }
}
