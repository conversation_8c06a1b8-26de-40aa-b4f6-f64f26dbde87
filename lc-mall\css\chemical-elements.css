/* 化学元素样式 */
.chemical-elements-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 2; /* 确保在网格背景之上，但不会干扰其他元素 */
}

.chemical-element {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    color: rgba(0, 102, 204, 0.8);
    opacity: 0.7;
    text-shadow: 0 0 5px rgba(0, 102, 204, 0.4);
    animation: float 20s linear infinite;
    text-align: center;
    user-select: none;
    transition: opacity 0.3s;
    z-index: 1;
}

.chemical-element.formula {
    width: auto;
    height: auto;
    font-size: 18px; /* 稍微减小字体大小 */
    color: rgba(0, 0, 0, 0.85); /* 黑色文本，稍微增加对比度 */
    text-shadow: none; /* 移除文字阴影 */
    white-space: nowrap;
    font-family: 'Arial', sans-serif;
    font-weight: 300; /* 更细的字体 */
    letter-spacing: 0.3px; /* 减小字母间距 */
    padding: 5px 10px; /* 添加一些内边距 */
    background-color: rgba(255, 255, 255, 0.5); /* 半透明白色背景 */
    border-radius: 4px; /* 轻微圆角 */
}

.chemical-element.formula.small {
    font-size: 15px;
    font-weight: 300;
    padding: 4px 8px;
}

.chemical-element.formula.large {
    font-size: 20px;
    font-weight: 400; /* 减小粗细 */
    padding: 6px 12px;
}

.chemical-element:hover {
    opacity: 0.8;
}

.chemical-element.small {
    font-size: 14px;
}

.chemical-element.large {
    font-size: 22px;
}

/* 元素颜色 */
.element-H {
    color: rgba(51, 51, 51, 0.8);
    text-shadow: 0 0 5px rgba(51, 51, 51, 0.4);
}

.element-C {
    color: rgba(51, 51, 51, 0.8);
    text-shadow: 0 0 5px rgba(51, 51, 51, 0.4);
}

.element-N {
    color: rgba(0, 102, 204, 0.8);
    text-shadow: 0 0 5px rgba(0, 102, 204, 0.4);
}

.element-O {
    color: rgba(255, 51, 51, 0.8);
    text-shadow: 0 0 5px rgba(255, 51, 51, 0.4);
}

.element-Na {
    color: rgba(102, 102, 255, 0.8);
    text-shadow: 0 0 5px rgba(102, 102, 255, 0.4);
}

.element-Mg {
    color: rgba(51, 204, 51, 0.8);
    text-shadow: 0 0 5px rgba(51, 204, 51, 0.4);
}

.element-Al {
    color: rgba(153, 153, 153, 0.8);
    text-shadow: 0 0 5px rgba(153, 153, 153, 0.4);
}

.element-Si {
    color: rgba(153, 102, 51, 0.8);
    text-shadow: 0 0 5px rgba(153, 102, 51, 0.4);
}

.element-P {
    color: rgba(255, 102, 0, 0.8);
    text-shadow: 0 0 5px rgba(255, 102, 0, 0.4);
}

.element-S {
    color: rgba(204, 204, 0, 0.8);
    text-shadow: 0 0 5px rgba(204, 204, 0, 0.4);
}

.element-Cl {
    color: rgba(0, 204, 0, 0.8);
    text-shadow: 0 0 5px rgba(0, 204, 0, 0.4);
}

.element-K {
    color: rgba(204, 0, 204, 0.8);
    text-shadow: 0 0 5px rgba(204, 0, 204, 0.4);
}

.element-Ca {
    color: rgba(255, 153, 0, 0.8);
    text-shadow: 0 0 5px rgba(255, 153, 0, 0.4);
}

.element-Fe {
    color: rgba(204, 102, 0, 0.8);
    text-shadow: 0 0 5px rgba(204, 102, 0, 0.4);
}

.element-Cu {
    color: rgba(204, 102, 102, 0.8);
    text-shadow: 0 0 5px rgba(204, 102, 102, 0.4);
}

.element-Zn {
    color: rgba(102, 102, 102, 0.8);
    text-shadow: 0 0 5px rgba(102, 102, 102, 0.4);
}

.element-Li {
    color: rgba(204, 51, 51, 0.8);
    text-shadow: 0 0 5px rgba(204, 51, 51, 0.4);
}

.element-B {
    color: rgba(0, 153, 153, 0.8);
    text-shadow: 0 0 5px rgba(0, 153, 153, 0.4);
}

.element-F {
    color: rgba(153, 204, 255, 0.8);
    text-shadow: 0 0 5px rgba(153, 204, 255, 0.4);
}

.element-Ne {
    color: rgba(255, 153, 153, 0.8);
    text-shadow: 0 0 5px rgba(255, 153, 153, 0.4);
}

.element-Ar {
    color: rgba(153, 204, 153, 0.8);
    text-shadow: 0 0 5px rgba(153, 204, 153, 0.4);
}

/* 浮动动画 - 增加浮动范围 */
@keyframes float {
    0% {
        transform: translateY(0) translateX(0) rotate(0deg);
    }
    25% {
        transform: translateY(15px) translateX(20px) rotate(5deg);
    }
    50% {
        transform: translateY(0) translateX(40px) rotate(0deg);
    }
    75% {
        transform: translateY(-15px) translateX(20px) rotate(-5deg);
    }
    100% {
        transform: translateY(0) translateX(0) rotate(0deg);
    }
}

/* 第二种浮动动画 - 用于公式，增加左右浮动范围 */
@keyframes float2 {
    0% {
        transform: translateY(0) translateX(0) rotateY(0deg);
    }
    33% {
        transform: translateY(20px) translateX(-30px) rotateY(10deg);
    }
    66% {
        transform: translateY(-15px) translateX(35px) rotateY(-10deg);
    }
    100% {
        transform: translateY(0) translateX(0) rotateY(0deg);
    }
}

/* 第三种浮动动画 - 更慢的动画，增加左右浮动范围 */
@keyframes float3 {
    0% {
        transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    }
    25% {
        transform: translateY(10px) translateX(25px) rotate(3deg) scale(1.05);
    }
    50% {
        transform: translateY(15px) translateX(-20px) rotate(0deg) scale(1);
    }
    75% {
        transform: translateY(5px) translateX(-30px) rotate(-3deg) scale(0.95);
    }
    100% {
        transform: translateY(0) translateX(0) rotate(0deg) scale(1);
    }
}

/* 头部区域的特殊样式 - 已移除，避免与element-balls-animation.css冲突 */
