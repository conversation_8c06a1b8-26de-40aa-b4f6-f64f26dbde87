/**
 * 简单地图显示
 * Simple map display for LongChi Mall - No API key required
 */

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 检查页面上是否有地图容器
    if (document.getElementById('baidu-map')) {
        createSimpleMap();
    }
});

/**
 * 创建简单地图显示
 * 不需要任何API密钥，完全静态实现
 */
function createSimpleMap() {
    var mapContainer = document.getElementById('baidu-map');
    if (!mapContainer) return;

    // 设置容器样式
    mapContainer.style.position = 'relative';
    mapContainer.style.backgroundColor = '#f0f0f0';
    mapContainer.style.overflow = 'hidden';
    mapContainer.style.borderRadius = '4px';

    // 创建地图背景
    var mapBackground = document.createElement('div');
    mapBackground.className = 'map-background';
    mapBackground.style.position = 'absolute';
    mapBackground.style.top = '0';
    mapBackground.style.left = '0';
    mapBackground.style.width = '100%';
    mapBackground.style.height = '100%';
    mapBackground.style.backgroundColor = '#e8eef7';

    // 添加网格线
    mapBackground.style.backgroundImage = 'linear-gradient(rgba(200, 200, 200, 0.2) 1px, transparent 1px), linear-gradient(90deg, rgba(200, 200, 200, 0.2) 1px, transparent 1px)';
    mapBackground.style.backgroundSize = '20px 20px';

    // 添加到容器
    mapContainer.appendChild(mapBackground);

    // 创建地图内容
    var mapContent = document.createElement('div');
    mapContent.className = 'map-content';
    mapContent.style.position = 'absolute';
    mapContent.style.top = '0';
    mapContent.style.left = '0';
    mapContent.style.width = '100%';
    mapContent.style.height = '100%';
    mapContent.style.display = 'flex';
    mapContent.style.justifyContent = 'center';
    mapContent.style.alignItems = 'center';

    // 添加到容器
    mapContainer.appendChild(mapContent);

    // 创建位置标记
    var locationMarker = document.createElement('div');
    locationMarker.className = 'location-marker';
    locationMarker.style.position = 'absolute';
    locationMarker.style.top = '50%';
    locationMarker.style.left = '50%';
    locationMarker.style.transform = 'translate(-50%, -50%)';
    locationMarker.style.width = '30px';
    locationMarker.style.height = '30px';

    // 创建标记图标
    var markerIcon = document.createElement('div');
    markerIcon.style.width = '30px';
    markerIcon.style.height = '30px';
    markerIcon.style.borderRadius = '50% 50% 50% 0';
    markerIcon.style.backgroundColor = '#1a73e8';
    markerIcon.style.transform = 'rotate(-45deg)';
    markerIcon.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.3)';

    // 创建标记内部圆点
    var markerDot = document.createElement('div');
    markerDot.style.width = '14px';
    markerDot.style.height = '14px';
    markerDot.style.borderRadius = '50%';
    markerDot.style.backgroundColor = 'white';
    markerDot.style.position = 'absolute';
    markerDot.style.top = '50%';
    markerDot.style.left = '50%';
    markerDot.style.transform = 'translate(-50%, -50%) rotate(45deg)';

    markerIcon.appendChild(markerDot);
    locationMarker.appendChild(markerIcon);

    // 添加脉冲效果
    var pulseEffect = document.createElement('div');
    pulseEffect.style.position = 'absolute';
    pulseEffect.style.top = '50%';
    pulseEffect.style.left = '50%';
    pulseEffect.style.transform = 'translate(-50%, -50%)';
    pulseEffect.style.width = '50px';
    pulseEffect.style.height = '50px';
    pulseEffect.style.borderRadius = '50%';
    pulseEffect.style.backgroundColor = 'rgba(26, 115, 232, 0.2)';
    pulseEffect.style.animation = 'pulse 2s infinite';

    // 添加脉冲动画样式
    var style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(0.5);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(1.5);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    locationMarker.appendChild(pulseEffect);
    mapContent.appendChild(locationMarker);

    // 添加地址信息卡片
    var addressCard = document.createElement('div');
    addressCard.className = 'address-card';
    addressCard.style.position = 'absolute';
    addressCard.style.bottom = '20px';
    addressCard.style.left = '20px';
    addressCard.style.backgroundColor = 'white';
    addressCard.style.padding = '15px';
    addressCard.style.borderRadius = '4px';
    addressCard.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
    addressCard.style.maxWidth = '300px';
    addressCard.style.zIndex = '10';

    // 添加地址内容
    addressCard.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fas fa-map-marker-alt" style="color: #1a73e8; margin-right: 8px;"></i>
            <strong style="font-size: 16px; color: #333;">广州市龙驰新材料科技有限公司</strong>
        </div>
        <div style="margin-bottom: 5px; color: #555; font-size: 14px;">
            <i class="fas fa-building" style="width: 16px; margin-right: 8px;"></i>
            广州市白云区鹤龙街康庄路1号
        </div>
        <div style="color: #555; font-size: 14px;">
            <i class="fas fa-phone" style="width: 16px; margin-right: 8px;"></i>
            17620086966
        </div>
        <div style="margin-top: 10px;">
            <a href="https://map.baidu.com/search/%E5%B9%BF%E5%B7%9E%E5%B8%82%E7%99%BD%E4%BA%91%E5%8C%BA%E9%B9%A4%E9%BE%99%E8%A1%97%E5%BA%B7%E5%BA%84%E8%B7%AF1%E5%8F%B7/@12618261.095,2631444.97,19z?querytype=s&da_src=shareurl&wd=%E5%B9%BF%E5%B7%9E%E5%B8%82%E7%99%BD%E4%BA%91%E5%8C%BA%E9%B9%A4%E9%BE%99%E8%A1%97%E5%BA%B7%E5%BA%84%E8%B7%AF1%E5%8F%B7&c=257&src=0&wd2=%E5%B9%BF%E5%B7%9E%E5%B8%82%E7%99%BD%E4%BA%91%E5%8C%BA&pn=0&sug=0&l=13&from=webmap"
               target="_blank"
               style="display: inline-block; background-color: #1a73e8; color: white; padding: 5px 10px; border-radius: 4px; text-decoration: none; font-size: 12px; margin-top: 5px;">
                <i class="fas fa-directions" style="margin-right: 5px;"></i>查看导航
            </a>
        </div>
    `;

    mapContainer.appendChild(addressCard);

    // 添加点击标记时的交互
    locationMarker.addEventListener('click', function() {
        // 创建一个简单的弹出信息
        var popup = document.createElement('div');
        popup.style.position = 'absolute';
        popup.style.top = '40%';
        popup.style.left = '50%';
        popup.style.transform = 'translate(-50%, -100%)';
        popup.style.backgroundColor = 'white';
        popup.style.padding = '10px';
        popup.style.borderRadius = '4px';
        popup.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
        popup.style.zIndex = '20';
        popup.style.minWidth = '150px';
        popup.style.textAlign = 'center';
        popup.innerHTML = '<strong>龙驰新材料</strong><br>点击下方卡片查看详情';

        // 添加小三角形指向标记
        var triangle = document.createElement('div');
        triangle.style.position = 'absolute';
        triangle.style.bottom = '-8px';
        triangle.style.left = '50%';
        triangle.style.transform = 'translateX(-50%)';
        triangle.style.width = '0';
        triangle.style.height = '0';
        triangle.style.borderLeft = '8px solid transparent';
        triangle.style.borderRight = '8px solid transparent';
        triangle.style.borderTop = '8px solid white';

        popup.appendChild(triangle);

        // 检查是否已经有弹出窗口
        var existingPopup = mapContainer.querySelector('.map-popup');
        if (existingPopup) {
            mapContainer.removeChild(existingPopup);
        }

        popup.className = 'map-popup';
        mapContainer.appendChild(popup);

        // 3秒后自动关闭
        setTimeout(function() {
            if (popup.parentNode === mapContainer) {
                mapContainer.removeChild(popup);
            }
        }, 3000);
    });

    // 添加道路
    var roads = [
        { top: '40%', left: '0', width: '100%', height: '10px', rotate: '0deg', name: '康庄路', main: true },
        { top: '30%', left: '30%', width: '40%', height: '8px', rotate: '90deg', name: '鹤龙街', main: true },
        { top: '60%', left: '20%', width: '60%', height: '6px', rotate: '15deg', name: '白云大道', main: true },
        { top: '20%', left: '10%', width: '40%', height: '5px', rotate: '30deg', name: '云山路', main: false },
        { top: '70%', left: '40%', width: '50%', height: '5px', rotate: '-10deg', name: '白云路', main: false },
        { top: '50%', left: '15%', width: '30%', height: '4px', rotate: '60deg', name: '龙岗路', main: false },
        { top: '25%', left: '60%', width: '35%', height: '4px', rotate: '120deg', name: '云城路', main: false }
    ];

    roads.forEach(function(road) {
        // 创建道路元素
        var roadElement = document.createElement('div');
        roadElement.className = 'map-road';
        roadElement.style.position = 'absolute';
        roadElement.style.top = road.top;
        roadElement.style.left = road.left;
        roadElement.style.width = road.width;
        roadElement.style.height = road.height;

        // 主要道路和次要道路样式不同
        if (road.main) {
            roadElement.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
            roadElement.style.boxShadow = '0 0 3px rgba(0, 0, 0, 0.15)';
            roadElement.style.zIndex = '2';
        } else {
            roadElement.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
            roadElement.style.boxShadow = '0 0 1px rgba(0, 0, 0, 0.1)';
            roadElement.style.zIndex = '1';
        }

        roadElement.style.transform = 'rotate(' + road.rotate + ')';
        roadElement.style.transformOrigin = 'center left';
        mapBackground.appendChild(roadElement);

        // 添加道路名称
        if (road.name) {
            var roadNameElement = document.createElement('div');
            roadNameElement.className = 'road-name';

            // 计算道路名称位置
            var angle = parseInt(road.rotate.replace('deg', ''));
            var nameTop, nameLeft, nameTransform;

            // 根据道路角度调整名称位置
            if (angle >= -45 && angle <= 45) {
                // 水平道路
                nameTop = 'calc(' + road.top + ' - 15px)';
                nameLeft = 'calc(' + road.left + ' + ' + (parseInt(road.width) / 4) + 'px)';
                nameTransform = 'none';
            } else if (angle > 45 && angle < 135) {
                // 垂直道路
                nameTop = 'calc(' + road.top + ' + ' + (parseInt(road.height) * 2) + 'px)';
                nameLeft = 'calc(' + road.left + ' - 15px)';
                nameTransform = 'rotate(-90deg)';
                roadNameElement.style.transformOrigin = 'left bottom';
            } else {
                // 斜向道路
                nameTop = 'calc(' + road.top + ' + 5px)';
                nameLeft = 'calc(' + road.left + ' + ' + (parseInt(road.width) / 4) + 'px)';
                nameTransform = 'rotate(' + road.rotate + ')';
            }

            roadNameElement.style.position = 'absolute';
            roadNameElement.style.top = nameTop;
            roadNameElement.style.left = nameLeft;
            roadNameElement.style.transform = nameTransform;
            roadNameElement.style.fontSize = road.main ? '11px' : '9px';
            roadNameElement.style.fontWeight = road.main ? 'bold' : 'normal';
            roadNameElement.style.color = '#555';
            roadNameElement.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
            roadNameElement.style.padding = '1px 3px';
            roadNameElement.style.borderRadius = '2px';
            roadNameElement.style.whiteSpace = 'nowrap';
            roadNameElement.style.zIndex = '3';
            roadNameElement.textContent = road.name;

            mapBackground.appendChild(roadNameElement);
        }
    });

    // 添加建筑物和周边地标
    var buildings = [
        {
            top: '30%',
            left: '20%',
            width: '40px',
            height: '40px',
            color: '#d1e6fa',
            name: '白云区政府',
            type: 'government'
        },
        {
            top: '35%',
            left: '70%',
            width: '30px',
            height: '30px',
            color: '#d1e6fa',
            name: '白云万达广场',
            type: 'shopping'
        },
        {
            top: '60%',
            left: '30%',
            width: '35px',
            height: '35px',
            color: '#d1e6fa',
            name: '白云区医院',
            type: 'hospital'
        },
        {
            top: '65%',
            left: '60%',
            width: '25px',
            height: '25px',
            color: '#d1e6fa',
            name: '白云公园',
            type: 'park'
        },
        {
            top: '25%',
            left: '40%',
            width: '38px',
            height: '38px',
            color: '#d1e6fa',
            name: '白云山风景区',
            type: 'scenic'
        },
        {
            top: '55%',
            left: '75%',
            width: '32px',
            height: '32px',
            color: '#d1e6fa',
            name: '白云区第一中学',
            type: 'school'
        },
        {
            top: '15%',
            left: '65%',
            width: '28px',
            height: '28px',
            color: '#d1e6fa',
            name: '鹤龙地铁站',
            type: 'subway'
        }
    ];

    // 建筑物图标
    var buildingIcons = {
        'government': '<i class="fas fa-landmark" style="color: #3b5998;"></i>',
        'shopping': '<i class="fas fa-shopping-bag" style="color: #e4405f;"></i>',
        'hospital': '<i class="fas fa-hospital" style="color: #ff0000;"></i>',
        'park': '<i class="fas fa-tree" style="color: #00a651;"></i>',
        'scenic': '<i class="fas fa-mountain" style="color: #8a5a44;"></i>',
        'school': '<i class="fas fa-school" style="color: #f57c00;"></i>',
        'subway': '<i class="fas fa-subway" style="color: #0078d7;"></i>'
    };

    buildings.forEach(function(building) {
        // 创建建筑物元素
        var buildingElement = document.createElement('div');
        buildingElement.className = 'map-building';
        buildingElement.style.position = 'absolute';
        buildingElement.style.top = building.top;
        buildingElement.style.left = building.left;
        buildingElement.style.width = building.width;
        buildingElement.style.height = building.height;
        buildingElement.style.backgroundColor = building.color;
        buildingElement.style.borderRadius = '2px';
        buildingElement.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
        buildingElement.style.zIndex = '2';

        // 添加建筑物图标
        if (building.type && buildingIcons[building.type]) {
            var iconElement = document.createElement('div');
            iconElement.style.position = 'absolute';
            iconElement.style.top = '50%';
            iconElement.style.left = '50%';
            iconElement.style.transform = 'translate(-50%, -50%)';
            iconElement.style.fontSize = '14px';
            iconElement.innerHTML = buildingIcons[building.type];
            buildingElement.appendChild(iconElement);
        }

        mapBackground.appendChild(buildingElement);

        // 添加建筑物点击交互
        buildingElement.addEventListener('click', function() {
            // 创建建筑物信息弹窗
            var buildingInfo = document.createElement('div');
            buildingInfo.className = 'map-popup building-info';
            buildingInfo.style.position = 'absolute';
            buildingInfo.style.top = 'calc(' + building.top + ' - 10px)';
            buildingInfo.style.left = 'calc(' + building.left + ' + ' + (parseInt(building.width) / 2) + 'px)';
            buildingInfo.style.transform = 'translate(-50%, -100%)';
            buildingInfo.style.backgroundColor = 'white';
            buildingInfo.style.padding = '8px 12px';
            buildingInfo.style.borderRadius = '4px';
            buildingInfo.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
            buildingInfo.style.zIndex = '100';
            buildingInfo.style.minWidth = '120px';
            buildingInfo.style.textAlign = 'center';

            // 根据建筑物类型显示不同信息
            var buildingTypeText = '';
            switch(building.type) {
                case 'government': buildingTypeText = '政府机构'; break;
                case 'shopping': buildingTypeText = '购物中心'; break;
                case 'hospital': buildingTypeText = '医疗机构'; break;
                case 'park': buildingTypeText = '公园'; break;
                case 'scenic': buildingTypeText = '景区'; break;
                case 'school': buildingTypeText = '学校'; break;
                case 'subway': buildingTypeText = '地铁站'; break;
                default: buildingTypeText = '建筑'; break;
            }

            buildingInfo.innerHTML = '<strong>' + building.name + '</strong><br><span style="font-size: 11px; color: #666;">' + buildingTypeText + '</span>';

            // 添加小三角形指向建筑物
            var triangle = document.createElement('div');
            triangle.style.position = 'absolute';
            triangle.style.bottom = '-8px';
            triangle.style.left = '50%';
            triangle.style.transform = 'translateX(-50%)';
            triangle.style.width = '0';
            triangle.style.height = '0';
            triangle.style.borderLeft = '8px solid transparent';
            triangle.style.borderRight = '8px solid transparent';
            triangle.style.borderTop = '8px solid white';

            buildingInfo.appendChild(triangle);

            // 检查是否已经有弹出窗口
            var existingPopup = mapBackground.querySelector('.building-info');
            if (existingPopup) {
                mapBackground.removeChild(existingPopup);
            }

            mapBackground.appendChild(buildingInfo);

            // 添加关闭按钮
            var closeButton = document.createElement('div');
            closeButton.style.position = 'absolute';
            closeButton.style.top = '2px';
            closeButton.style.right = '2px';
            closeButton.style.width = '16px';
            closeButton.style.height = '16px';
            closeButton.style.borderRadius = '50%';
            closeButton.style.backgroundColor = '#f0f0f0';
            closeButton.style.color = '#666';
            closeButton.style.fontSize = '10px';
            closeButton.style.lineHeight = '16px';
            closeButton.style.textAlign = 'center';
            closeButton.style.cursor = 'pointer';
            closeButton.innerHTML = '×';
            closeButton.title = '关闭';

            closeButton.addEventListener('click', function(e) {
                e.stopPropagation();
                if (buildingInfo.parentNode === mapBackground) {
                    mapBackground.removeChild(buildingInfo);
                }
            });

            buildingInfo.appendChild(closeButton);

            // 3秒后自动关闭
            setTimeout(function() {
                if (buildingInfo.parentNode === mapBackground) {
                    mapBackground.removeChild(buildingInfo);
                }
            }, 3000);
        });

        // 添加建筑物名称标签
        if (building.name) {
            var labelElement = document.createElement('div');
            labelElement.className = 'building-label';
            labelElement.style.position = 'absolute';
            labelElement.style.top = 'calc(' + building.top + ' + ' + building.height + ' + 5px)';
            labelElement.style.left = building.left;
            labelElement.style.transform = 'translateX(-25%)';
            labelElement.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
            labelElement.style.padding = '2px 5px';
            labelElement.style.borderRadius = '2px';
            labelElement.style.fontSize = '10px';
            labelElement.style.whiteSpace = 'nowrap';
            labelElement.style.color = '#333';
            labelElement.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            labelElement.style.zIndex = '3';
            labelElement.textContent = building.name;
            mapBackground.appendChild(labelElement);
        }
    });

    // 移动端优化
    function optimizeForMobile() {
        if (window.innerWidth <= 768) {
            // 调整地址卡片位置和大小
            addressCard.style.left = '10px';
            addressCard.style.bottom = '10px';
            addressCard.style.maxWidth = 'calc(100% - 40px)';

            // 隐藏部分建筑物和标签，保留重要的
            var buildingLabels = mapBackground.querySelectorAll('.building-label');
            var roadNames = mapBackground.querySelectorAll('.road-name');
            var buildings = mapBackground.querySelectorAll('.map-building');

            // 保留主要道路名称，隐藏次要道路名称
            roadNames.forEach(function(roadName, index) {
                if (index % 2 === 1 || roadName.style.fontWeight !== 'bold') {
                    roadName.style.display = 'none';
                }
            });

            // 保留重要建筑物，隐藏次要建筑物
            buildings.forEach(function(building, index) {
                if (index > 3) { // 只保留前4个建筑物
                    building.style.display = 'none';
                }
            });

            // 保留重要建筑物标签，隐藏次要建筑物标签
            buildingLabels.forEach(function(label, index) {
                if (index > 3) { // 只保留前4个建筑物标签
                    label.style.display = 'none';
                } else {
                    // 调整标签字体大小
                    label.style.fontSize = '8px';
                    label.style.padding = '1px 3px';
                }
            });

            // 添加公司位置标记的特殊样式，使其更突出
            locationMarker.style.zIndex = '10';
            var markerIcon = locationMarker.querySelector('div');
            if (markerIcon) {
                markerIcon.style.width = '35px';
                markerIcon.style.height = '35px';
            }
        } else {
            // 恢复桌面版显示
            var buildingLabels = mapBackground.querySelectorAll('.building-label');
            var roadNames = mapBackground.querySelectorAll('.road-name');
            var buildings = mapBackground.querySelectorAll('.map-building');

            roadNames.forEach(function(roadName) {
                roadName.style.display = '';
            });

            buildings.forEach(function(building) {
                building.style.display = '';
            });

            buildingLabels.forEach(function(label) {
                label.style.display = '';
                label.style.fontSize = '10px';
                label.style.padding = '2px 5px';
            });
        }
    }

    // 初始化时检查
    optimizeForMobile();

    // 窗口大小改变时重新检查
    window.addEventListener('resize', optimizeForMobile);
}
