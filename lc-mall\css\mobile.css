/* 移动端专用样式 */
@media only screen and (max-width: 767px) {
    /* 化学公式优化 */
    .chemical-formulas span {
        font-family: Arial, sans-serif !important;
        font-weight: 400 !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }
    /* 基础布局调整 */
    body {
        font-size: 14px;
    }

    .container {
        padding: 0 10px;
    }

    /* 头部调整 */
    header {
        padding: 10px 0;
    }

    .logo img {
        max-height: 40px;
    }

    /* 导航栏调整 */
    nav {
        padding: 0;
    }

    .nav-links {
        flex-direction: column;
        padding: 10px 0;
    }

    .nav-links li {
        margin: 5px 0;
    }

    /* 商品列表调整 */
    #product-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-top: 15px;
    }

    .product-card {
        width: 100%;
        margin: 0 0 15px 0;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    /* 确保商品卡片不重叠 */
    .product-card {
        clear: both;
        float: none;
        display: block;
        margin-bottom: 20px;
    }

    /* 商品卡片内部布局调整 */
    .product-card-header img {
        height: 150px;
    }

    .product-info {
        padding: 10px;
    }

    .product-title-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .product-title-row h3 {
        font-size: 16px;
        margin-bottom: 5px;
    }

    .product-stock {
        font-size: 12px;
        margin-bottom: 5px;
    }

    .product-meta {
        margin-bottom: 5px;
    }

    .product-meta .product-sku {
        font-size: 12px;
    }

    .product-info p {
        font-size: 12px;
        margin-bottom: 10px;
        line-height: 1.4;
    }

    /* 价格和购物车按钮行 */
    .price-cart-row {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
    }

    .price {
        font-size: 18px;
        font-weight: bold;
    }

    .add-to-cart {
        padding: 8px 12px;
        font-size: 13px;
    }

    /* 侧边栏调整 */
    .sidebar {
        display: none; /* 在移动端隐藏侧边栏 */
    }

    /* 移动端分类导航 */
    .mobile-categories {
        display: block;
        margin: 10px 0;
    }

    .mobile-categories select {
        width: 100%;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }

    /* 搜索框调整 */
    .search-container {
        margin: 10px 0;
    }

    .search-container input {
        width: 70%;
    }

    .search-container button {
        width: 28%;
    }

    /* 购物车模态框调整 */
    .modal-content {
        width: 95%;
        margin: 10% auto;
        max-height: 80vh;
    }

    .cart-item {
        flex-direction: column;
        padding: 10px;
    }

    .cart-item-image {
        width: 80px;
        height: 80px;
        margin-bottom: 10px;
    }

    .cart-item-details {
        width: 100%;
        padding: 0;
    }

    .cart-item-actions {
        width: 100%;
        justify-content: space-between;
        margin-top: 10px;
    }

    /* 产品详情模态框调整 */
    .product-modal-content {
        flex-direction: column;
    }

    .product-modal-image {
        width: 100%;
        height: auto;
        margin-bottom: 15px;
    }

    .product-modal-info {
        width: 100%;
        padding: 0;
    }

    /* 浮动购物车按钮调整 */
    #floating-cart {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
    }

    #floating-cart-count {
        width: 20px;
        height: 20px;
        font-size: 12px;
    }

    /* 回到顶部按钮调整 */
    #back-to-top {
        bottom: 80px;
        right: 20px;
        width: 40px;
        height: 40px;
    }

    /* 移动端调试信息 */
    #mobile-debug-info {
        font-size: 10px;
        max-height: 100px;
    }

    /* 视频部分移动端样式 */
    .video-section {
        padding: 20px 0;
    }

    .video-section-title h2 {
        font-size: 20px;
    }

    .video-section-title p {
        font-size: 14px;
    }

    .video-carousel {
        margin: 0 -10px;
    }

    .video-carousel-inner {
        margin: 0;
    }

    .video-item {
        flex: 0 0 calc(100% - 20px);
        margin: 0 10px;
    }

    .play-button {
        width: 50px;
        height: 50px;
    }

    .play-button i {
        font-size: 20px;
    }

    .video-info h3 {
        font-size: 16px;
    }

    .video-info p {
        font-size: 12px;
    }

    .carousel-nav button {
        width: 36px;
        height: 36px;
    }

    .carousel-nav .prev {
        margin-left: 5px;
    }

    .carousel-nav .next {
        margin-right: 5px;
    }

    .video-modal-content {
        width: 95%;
    }

    .video-modal-close {
        top: -30px;
        right: 0;
        font-size: 24px;
    }
}
