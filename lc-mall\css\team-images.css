/* 团队成员图片样式 */
.team-member .member-photo {
    position: relative;
    overflow: hidden;
    height: 300px;
    background-color: #f5f9ff;
}

.team-member .member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.team-member:hover .member-photo img {
    transform: scale(1.05);
}

/* 团队成员图片覆盖层 */
.member-photo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.4));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.team-member:hover .member-photo::before {
    opacity: 1;
}

/* 团队成员职位图标 */
.member-photo::after {
    content: '';
    position: absolute;
    bottom: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #0066cc;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    z-index: 2;
    opacity: 0.9;
    transition: transform 0.3s ease;
}

.team-member:hover .member-photo::after {
    transform: scale(1.1);
}

/* 特定职位的图标 */
.team-member.ceo .member-photo::after {
    content: '\f521'; /* FontAwesome 用户领带图标 */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.team-member.tech .member-photo::after {
    content: '\f0c3'; /* FontAwesome 烧杯图标 */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.team-member.sales .member-photo::after {
    content: '\f201'; /* FontAwesome 图表图标 */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}
