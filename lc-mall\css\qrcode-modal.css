/* 二维码模态框样式 */

.qrcode-modal {
    display: none;
    position: fixed;
    z-index: 1100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(15, 23, 42, 0.75);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.qrcode-modal.show {
    display: block;
    opacity: 1;
}

.qrcode-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 2rem;
    border-radius: 8px;
    max-width: 350px;
    position: relative;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.qrcode-modal.show .qrcode-content {
    transform: translateY(0);
    opacity: 1;
}

.qrcode-modal .close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    font-weight: bold;
    color: #666;
    cursor: pointer;
}

.qrcode-modal .close:hover {
    color: #333;
}

.qrcode-modal h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
}

.modal-qrcode-container {
    width: 200px;
    height: 200px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #eee;
}

.qrcode-loading {
    text-align: center;
    color: #666;
}

.qrcode-loading i {
    font-size: 40px;
    margin-bottom: 10px;
    color: #1a73e8;
}

.qrcode-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.qrcode-tip {
    text-align: center;
    color: #666;
    font-size: 14px;
    margin-top: 15px;
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .qrcode-content {
        width: 90%;
        margin: 20% auto;
        padding: 1.5rem;
    }

    .modal-qrcode-container {
        width: 180px;
        height: 180px;
    }
}
