/* 按钮样式修复 */
.btn-primary {
    background-color: #1a73e8 !important; /* 使用固定的蓝色 */
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 16px !important;
    font-weight: 600 !important; /* 增加字体粗细 */
    padding: 12px !important; /* 增加内边距 */
    text-align: center !important;
    display: block !important;
    width: 100% !important;
    margin-top: 10px !important;
    position: relative !important;
    z-index: 10 !important;
    text-transform: none !important; /* 防止文字被转换 */
    letter-spacing: normal !important; /* 防止字母间距被改变 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important; /* 添加阴影增强可见性 */
}

.btn-primary:hover {
    background-color: #0d62d0 !important; /* 深蓝色 */
    color: white !important;
}

/* 登录按钮文字内容 */
#login-form button[type="submit"]::after {
    content: "登录" !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 16px !important;
    font-weight: 500 !important;
}

/* 注册按钮文字内容 */
#register-form button[type="submit"]::after {
    content: "注册" !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 16px !important;
    font-weight: 500 !important;
}

/* 顶部导航栏按钮样式修复 */
.user-status .btn {
    color: white !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    display: inline-block !important;
    text-align: center !important;
}

/* 顶部导航栏登录按钮 */
#login-btn::after {
    content: "登录" !important;
    color: white !important;
}

/* 顶部导航栏注册按钮 */
#register-btn::after {
    content: "注册" !important;
    color: white !important;
}
