// Admin shared functionality
document.addEventListener('DOMContentLoaded', async function() {
    // 如果在登录页面，不需要检查登录状态
    if (window.location.href.includes('login.html')) {
        return;
    }

    try {
        // 检查用户是否登录
        const loginStatus = await ApiClient.checkAdminLogin();
        const isLoggedIn = loginStatus && loginStatus.loggedIn === true;

        // 如果未登录，重定向到登录页面
        if (!isLoggedIn) {
            window.location.href = 'login.html';
            return;
        }
    } catch (error) {
        console.error('检查登录状态失败:', error);
        window.location.href = 'login.html';
        return;
    }

    // 登出功能
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', async function(e) {
            e.preventDefault();

            try {
                // 调用API登出
                await ApiClient.adminLogout();

                // 重定向到登录页面
                window.location.href = 'login.html';
            } catch (error) {
                console.error('登出失败:', error);
                alert('登出失败，请重试');
            }
        });
    }

    // Close modals when clicking on close button
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.style.display = 'none';
            });
        });
    });

    // Close modals when clicking outside
    window.addEventListener('click', function(e) {
        document.querySelectorAll('.modal').forEach(modal => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });
});
