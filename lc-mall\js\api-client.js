/**
 * 龙驰商城API客户端
 * 提供与后端API交互的方法
 */

console.log('API Client loaded');

// API基础URL - 获取当前主机和端口
const getApiBaseUrl = () => {
    // 获取当前URL的主机部分（包括协议、主机名和端口）
    const currentHost = window.location.origin;
    // 返回完整的API基础URL
    return `${currentHost}/api`;
};

// 使用函数获取API基础URL
const API_BASE_URL = getApiBaseUrl();

// API客户端
const ApiClient = {
    /**
     * 获取所有商品
     * @param {Object} params - 查询参数 (category, featured, search)
     * @returns {Promise<Array>} - 商品数组
     */
    async getProducts(params = {}) {
        try {
            // 构建查询字符串
            const queryParams = new URLSearchParams();
            Object.keys(params).forEach(key => {
                if (params[key] !== undefined && params[key] !== null) {
                    queryParams.append(key, params[key]);
                }
            });

            const queryString = queryParams.toString();
            const url = `${API_BASE_URL}/products${queryString ? '?' + queryString : ''}`;

            const response = await fetch(url);

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${response.statusText}${errorText ? ' - ' + errorText : ''}`);
            }

            const data = await response.json();

            // 检查返回的数据格式
            if (!Array.isArray(data)) {
                throw new Error('服务器返回的数据格式不正确，期望数组格式');
            }

            return data;
        } catch (error) {
            console.error('获取商品错误:', error);
            // 重新抛出错误，让调用者处理
            throw error;
        }
    },

    /**
     * 获取单个商品
     * @param {number} productId - 商品ID
     * @returns {Promise<Object|null>} - 商品对象或null
     */
    async getProduct(productId) {
        try {
            const response = await fetch(`${API_BASE_URL}/products/${productId}`);

            if (!response.ok) {
                throw new Error(`获取商品失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`获取商品 ${productId} 错误:`, error);
            return null;
        }
    },

    /**
     * 添加商品
     * @param {Object} productData - 商品数据
     * @returns {Promise<Object|null>} - 添加的商品或null
     */
    async addProduct(productData) {
        try {
            // 检查数据大小，如果太大可能会导致请求失败
            const jsonData = JSON.stringify(productData);
            const dataSizeInMB = jsonData.length / (1024 * 1024);

            if (dataSizeInMB > 5) {
                console.warn(`商品数据过大 (${dataSizeInMB.toFixed(2)}MB)，可能导致请求失败`);

                // 如果图片数据是base64编码，可能会导致数据过大
                if (productData.image && productData.image.startsWith('data:')) {
                    console.warn('检测到base64编码的图片数据，尝试压缩图片');
                    try {
                        // 尝试压缩图片而不是直接替换为占位图
                        productData.image = await this.compressImageData(productData.image);
                    } catch (compressionError) {
                        console.error('图片压缩失败:', compressionError);
                        console.warn('使用占位图替代');
                        productData.image = '../images/placeholder/product-placeholder.svg';
                    }
                }

                // 重新生成JSON数据
                const updatedJsonData = JSON.stringify(productData);
                const updatedSizeInMB = updatedJsonData.length / (1024 * 1024);
                console.log(`更新后的数据大小: ${updatedSizeInMB.toFixed(2)}MB`);

                if (updatedSizeInMB > 1) {
                    throw new Error('商品数据仍然过大，无法提交');
                }
            }

            const response = await fetch(`${API_BASE_URL}/products`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(productData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`添加商品失败: ${response.status} ${response.statusText}\n${errorText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('添加商品错误:', error);
            throw error; // 重新抛出错误，让调用者处理
        }
    },

    /**
     * 更新商品
     * @param {number} productId - 商品ID
     * @param {Object} productData - 商品数据
     * @returns {Promise<Object|null>} - 更新后的商品或null
     */
    async updateProduct(productId, productData) {
        try {
            // 检查数据大小，如果太大可能会导致请求失败
            const jsonData = JSON.stringify(productData);
            const dataSizeInMB = jsonData.length / (1024 * 1024);

            if (dataSizeInMB > 5) {
                console.warn(`商品数据过大 (${dataSizeInMB.toFixed(2)}MB)，可能导致请求失败`);

                // 如果图片数据是base64编码，可能会导致数据过大
                if (productData.image && productData.image.startsWith('data:')) {
                    console.warn('检测到base64编码的图片数据，尝试压缩图片');
                    try {
                        // 尝试压缩图片而不是直接替换为占位图
                        productData.image = await this.compressImageData(productData.image);
                    } catch (compressionError) {
                        console.error('图片压缩失败:', compressionError);
                        console.warn('使用占位图替代');
                        productData.image = '../images/placeholder/product-placeholder.svg';
                    }
                }

                // 重新生成JSON数据
                const updatedJsonData = JSON.stringify(productData);
                const updatedSizeInMB = updatedJsonData.length / (1024 * 1024);
                console.log(`更新后的数据大小: ${updatedSizeInMB.toFixed(2)}MB`);

                if (updatedSizeInMB > 1) {
                    throw new Error('商品数据仍然过大，无法提交');
                }
            }

            const response = await fetch(`${API_BASE_URL}/products/${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(productData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`更新商品失败: ${response.status} ${response.statusText}\n${errorText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`更新商品 ${productId} 错误:`, error);
            throw error; // 重新抛出错误，让调用者处理
        }
    },

    /**
     * 删除商品
     * @param {number} productId - 商品ID
     * @returns {Promise<boolean>} - 是否成功
     */
    async deleteProduct(productId) {
        try {
            const response = await fetch(`${API_BASE_URL}/products/${productId}`, {
                method: 'DELETE'
            });

            return response.ok;
        } catch (error) {
            console.error(`删除商品 ${productId} 错误:`, error);
            return false;
        }
    },

    /**
     * 获取所有订单
     * @returns {Promise<Array>} - 订单数组
     */
    async getOrders() {
        try {
            const response = await fetch(`${API_BASE_URL}/orders`);

            if (!response.ok) {
                throw new Error(`获取订单失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取订单错误:', error);
            return [];
        }
    },

    /**
     * 添加订单
     * @param {Object} orderData - 订单数据
     * @returns {Promise<Object|null>} - 添加的订单或null
     */
    async addOrder(orderData) {
        try {
            const response = await fetch(`${API_BASE_URL}/orders`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(orderData)
            });

            if (!response.ok) {
                throw new Error(`添加订单失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('添加订单错误:', error);
            return null;
        }
    },

    /**
     * 获取所有留言
     * @returns {Promise<Array>} - 留言数组
     */
    async getMessages() {
        try {
            const response = await fetch(`${API_BASE_URL}/messages`);

            if (!response.ok) {
                throw new Error(`获取留言失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取留言错误:', error);
            return [];
        }
    },

    /**
     * 添加留言
     * @param {Object} messageData - 留言数据
     * @returns {Promise<Object|null>} - 添加的留言或null
     */
    async addMessage(messageData) {
        try {
            const response = await fetch(`${API_BASE_URL}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(messageData)
            });

            if (!response.ok) {
                throw new Error(`添加留言失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('添加留言错误:', error);
            return null;
        }
    },

    /**
     * 获取所有SMS记录
     * @returns {Promise<Array>} - SMS记录数组
     */
    async getSmsRecords() {
        try {
            const response = await fetch(`${API_BASE_URL}/sms-records`);

            if (!response.ok) {
                throw new Error(`获取SMS记录失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取SMS记录错误:', error);
            return [];
        }
    },

    /**
     * 添加SMS记录
     * @param {Object} smsData - SMS记录数据
     * @returns {Promise<Object|null>} - 添加的SMS记录或null
     */
    async addSmsRecord(smsData) {
        try {
            const response = await fetch(`${API_BASE_URL}/sms-records`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(smsData)
            });

            if (!response.ok) {
                throw new Error(`添加SMS记录失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('添加SMS记录错误:', error);
            return null;
        }
    },

    /**
     * 更新订单状态
     * @param {string} orderId - 订单ID
     * @param {string} status - 新状态
     * @returns {Promise<Object|null>} - 更新后的订单或null
     */
    async updateOrderStatus(orderId, status) {
        try {
            const response = await fetch(`${API_BASE_URL}/orders/${orderId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ status })
            });

            if (!response.ok) {
                throw new Error(`更新订单状态失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`更新订单 ${orderId} 状态错误:`, error);
            return null;
        }
    },

    /**
     * 管理员登录
     * @param {Object} credentials - 登录凭证
     * @returns {Promise<Object|null>} - 登录结果或null
     */
    async adminLogin(credentials) {
        try {
            // 从JSON文件中获取管理员凭据
            const response = await fetch('/lc-mall/data/admin.json');
            if (!response.ok) {
                throw new Error('无法获取管理员凭据');
            }

            const validCredentials = await response.json();

            // 验证用户名和密码
            if (credentials.username === validCredentials.username &&
                credentials.password === validCredentials.password) {

                // 创建会话数据
                const sessionData = {
                    loggedIn: true,
                    username: credentials.username,
                    role: 'admin',
                    loginTime: new Date().toISOString()
                };

                // 将会话数据存储在会话存储中
                sessionStorage.setItem('adminSession', JSON.stringify(sessionData));

                // 返回成功响应
                return {
                    success: true,
                    message: '登录成功',
                    user: {
                        username: credentials.username,
                        role: 'admin'
                    }
                };
            } else {
                // 登录失败
                return {
                    success: false,
                    message: '用户名或密码错误'
                };
            }
        } catch (error) {
            console.error('登录错误:', error);
            return {
                success: false,
                message: '登录过程中发生错误: ' + error.message
            };
        }
    },

    /**
     * 检查管理员登录状态
     * @returns {Promise<Object>} - 登录状态对象
     */
    async checkAdminLogin() {
        try {
            // 从会话存储中获取登录状态
            const sessionDataStr = sessionStorage.getItem('adminSession');

            if (!sessionDataStr) {
                return {
                    loggedIn: false
                };
            }

            // 解析会话数据
            const sessionData = JSON.parse(sessionDataStr);

            // 返回登录状态对象
            return {
                loggedIn: sessionData.loggedIn === true,
                user: {
                    username: sessionData.username,
                    role: sessionData.role
                }
            };
        } catch (error) {
            console.error('检查登录状态错误:', error);
            return {
                loggedIn: false
            };
        }
    },

    /**
     * 管理员登出
     * @returns {Promise<boolean>} - 是否成功
     */
    async adminLogout() {
        try {
            // 从会话存储中移除登录状态
            sessionStorage.removeItem('adminSession');

            // 返回成功
            return true;
        } catch (error) {
            console.error('登出错误:', error);
            return false;
        }
    },

    /**
     * 检查手机号是否已注册
     * @param {string} phone - 要检查的手机号
     * @returns {Promise<boolean>} - 手机号是否已注册
     */
    async checkPhoneExists(phone) {
        try {
            const response = await fetch(`${API_BASE_URL}/users/check?phone=${encodeURIComponent(phone)}`);

            if (!response.ok) {
                throw new Error(`检查手机号失败: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            return result.exists;
        } catch (error) {
            console.error('检查手机号错误:', error);
            return false;
        }
    },

    /**
     * 用户注册
     * @param {Object} userData - 用户数据
     * @returns {Promise<Object|null>} - 注册的用户或null
     */
    async registerUser(userData) {
        try {
            console.log('注册用户数据:', userData);
            const response = await fetch(`${API_BASE_URL}/users/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `注册失败: ${response.status} ${response.statusText}`);
            }

            const user = await response.json();
            console.log('注册成功，用户数据:', user);

            // 保存用户会话
            this.saveUserSession(user);

            return user;
        } catch (error) {
            console.error('注册错误:', error);
            throw error;
        }
    },

    /**
     * 用户登录
     * @param {Object} credentials - 登录凭证 (只需要手机号)
     * @returns {Promise<Object|null>} - 登录的用户或null
     */
    async loginUser(credentials) {
        try {
            console.log('登录凭证:', credentials);
            const response = await fetch(`${API_BASE_URL}/users/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(credentials)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `登录失败: ${response.status} ${response.statusText}`);
            }

            const user = await response.json();
            console.log('登录成功，用户数据:', user);

            // 保存用户会话
            this.saveUserSession(user);

            return user;
        } catch (error) {
            console.error('登录错误:', error);
            throw error;
        }
    },

    /**
     * 保存用户会话
     * @param {Object} user - 用户数据
     */
    saveUserSession(user) {
        // 创建会话数据
        const sessionData = {
            loggedIn: true,
            userId: user.id,
            phone: user.phone || '',
            name: user.name,
            company: user.company || '',
            avatar: user.avatar || '',
            social_platform: user.social_platform || '',
            social_openid: user.social_openid || '',
            loginTime: new Date().toISOString()
        };

        // 将会话数据存储在会话存储中
        sessionStorage.setItem('userSession', JSON.stringify(sessionData));
    },

    /**
     * 压缩图片数据
     * @param {string} dataUrl - 图片的Data URL (base64编码)
     * @param {number} maxSizeMB - 最大大小(MB)
     * @returns {Promise<string>} - 压缩后的图片Data URL
     */
    async compressImageData(dataUrl, maxSizeMB = 1) {
        return new Promise((resolve, reject) => {
            try {
                // 创建图片对象
                const img = new Image();
                img.onload = function() {
                    // 创建canvas
                    const canvas = document.createElement('canvas');
                    let width = img.width;
                    let height = img.height;

                    // 计算最大尺寸，保持宽高比
                    const MAX_WIDTH = 1200;
                    const MAX_HEIGHT = 1200;

                    if (width > height) {
                        if (width > MAX_WIDTH) {
                            height = Math.round(height * (MAX_WIDTH / width));
                            width = MAX_WIDTH;
                        }
                    } else {
                        if (height > MAX_HEIGHT) {
                            width = Math.round(width * (MAX_HEIGHT / height));
                            height = MAX_HEIGHT;
                        }
                    }

                    // 设置canvas尺寸
                    canvas.width = width;
                    canvas.height = height;

                    // 绘制图片到canvas
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, width, height);

                    // 压缩图片
                    let quality = 0.8;  // 初始质量
                    let compressedDataUrl = canvas.toDataURL('image/jpeg', quality);

                    // 计算大小(MB)
                    let sizeInMB = compressedDataUrl.length / (1024 * 1024);

                    // 如果大小仍然超过限制，继续降低质量
                    while (sizeInMB > maxSizeMB && quality > 0.1) {
                        quality -= 0.1;
                        compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
                        sizeInMB = compressedDataUrl.length / (1024 * 1024);
                    }

                    console.log(`图片压缩完成: ${(sizeInMB).toFixed(2)}MB, 质量: ${quality.toFixed(1)}`);
                    resolve(compressedDataUrl);
                };

                img.onerror = function() {
                    reject(new Error('图片加载失败'));
                };

                // 设置图片源
                img.src = dataUrl;
            } catch (error) {
                reject(error);
            }
        });
    },

    /**
     * 检查用户登录状态
     * @returns {Object} - 登录状态对象
     */
    checkUserLogin() {
        try {
            // 从会话存储中获取登录状态
            const sessionDataStr = sessionStorage.getItem('userSession');

            if (!sessionDataStr) {
                return {
                    loggedIn: false
                };
            }

            // 解析会话数据
            const sessionData = JSON.parse(sessionDataStr);

            // 返回登录状态对象
            return {
                loggedIn: sessionData.loggedIn === true,
                user: {
                    id: sessionData.userId,
                    phone: sessionData.phone || '',
                    name: sessionData.name,
                    company: sessionData.company || '',
                    avatar: sessionData.avatar || '',
                    social_platform: sessionData.social_platform || '',
                    social_openid: sessionData.social_openid || ''
                }
            };
        } catch (error) {
            console.error('检查用户登录状态错误:', error);
            return {
                loggedIn: false
            };
        }
    },

    /**
     * 用户登出
     * @returns {boolean} - 是否成功
     */
    logoutUser() {
        try {
            // 从会话存储中移除登录状态
            sessionStorage.removeItem('userSession');

            // 返回成功
            return true;
        } catch (error) {
            console.error('用户登出错误:', error);
            return false;
        }
    },

    /**
     * 获取所有用户
     * @returns {Promise<Array>} - 用户数组
     */
    async getUsers() {
        try {
            const response = await fetch(`${API_BASE_URL}/users`);

            if (!response.ok) {
                throw new Error(`获取用户失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取用户错误:', error);
            return [];
        }
    },

    /**
     * 创建微信支付订单
     * @param {Object} orderData - 订单数据
     * @returns {Promise<Object>} - 支付结果
     */
    async createWechatPayOrder(orderData) {
        try {
            const response = await fetch(`${API_BASE_URL}/wechat-pay/create-order`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(orderData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `创建微信支付订单失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('创建微信支付订单错误:', error);
            throw error;
        }
    },

    /**
     * 查询微信支付订单状态
     * @param {string} orderId - 订单ID
     * @returns {Promise<Object>} - 订单状态
     */
    async queryWechatPayOrder(orderId) {
        try {
            const response = await fetch(`${API_BASE_URL}/wechat-pay/query-order?order_id=${orderId}`);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `查询微信支付订单失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('查询微信支付订单错误:', error);
            throw error;
        }
    }
};

// 导出API客户端
window.ApiClient = ApiClient;
