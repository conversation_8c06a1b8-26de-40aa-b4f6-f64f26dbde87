/**
 * 产品延迟加载脚本
 * 实现商品列表的懒加载功能，提高页面加载速度和用户体验
 */

// 配置参数
const LAZY_LOAD_CONFIG = {
    initialProductsToShow: 8,     // 初始显示的产品数量
    productsPerLoad: 8,           // 每次加载的产品数量
    loadingThreshold: 200,        // 距离底部多少像素时开始加载
    animationDelay: 50,           // 产品卡片动画延迟（毫秒）
    loadingIndicatorDelay: 500    // 加载指示器显示延迟（毫秒）
};

// 全局变量
let allProducts = [];             // 所有产品
let filteredProducts = [];        // 筛选后的产品
let currentlyDisplayedCount = 0;  // 当前显示的产品数量
let isLoading = false;            // 是否正在加载
let hasInitialized = false;       // 是否已初始化

// DOM 元素
let productListElement;
let loadingIndicator;
let noMoreProductsIndicator;

/**
 * 初始化懒加载功能
 */
function initProductLazyLoad() {
    console.log('初始化产品懒加载功能...');
    
    // 获取DOM元素
    productListElement = document.getElementById('product-list');
    
    // 如果不是产品页面，直接返回
    if (!productListElement) {
        console.log('当前页面不是产品页面，跳过懒加载初始化');
        return;
    }
    
    // 创建加载指示器
    createLoadingIndicator();
    
    // 创建"没有更多产品"指示器
    createNoMoreProductsIndicator();
    
    // 添加滚动事件监听器
    window.addEventListener('scroll', handleScroll);
    
    // 标记为已初始化
    hasInitialized = true;
    console.log('产品懒加载功能初始化完成');
}

/**
 * 创建加载指示器
 */
function createLoadingIndicator() {
    loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'loading-indicator';
    loadingIndicator.innerHTML = `
        <i class="fas fa-spinner fa-spin"></i>
        <p>正在加载更多产品...</p>
    `;
    loadingIndicator.style.display = 'none';
    
    // 添加到产品列表后面
    if (productListElement) {
        productListElement.parentNode.insertBefore(loadingIndicator, productListElement.nextSibling);
    }
}

/**
 * 创建"没有更多产品"指示器
 */
function createNoMoreProductsIndicator() {
    noMoreProductsIndicator = document.createElement('div');
    noMoreProductsIndicator.className = 'no-more-products';
    noMoreProductsIndicator.innerHTML = `
        <p>已显示全部产品</p>
    `;
    noMoreProductsIndicator.style.display = 'none';
    
    // 添加到产品列表后面
    if (productListElement && loadingIndicator) {
        productListElement.parentNode.insertBefore(noMoreProductsIndicator, loadingIndicator.nextSibling);
    }
}

/**
 * 处理滚动事件
 */
function handleScroll() {
    // 如果正在加载或没有更多产品，直接返回
    if (isLoading || currentlyDisplayedCount >= filteredProducts.length) {
        return;
    }
    
    // 计算是否需要加载更多
    const scrollPosition = window.innerHeight + window.scrollY;
    const productListBottom = productListElement.offsetTop + productListElement.offsetHeight;
    
    if (scrollPosition > productListBottom - LAZY_LOAD_CONFIG.loadingThreshold) {
        loadMoreProducts();
    }
}

/**
 * 加载更多产品
 */
function loadMoreProducts() {
    // 设置加载状态
    isLoading = true;
    
    // 显示加载指示器
    setTimeout(() => {
        if (isLoading && loadingIndicator) {
            loadingIndicator.style.display = 'flex';
        }
    }, LAZY_LOAD_CONFIG.loadingIndicatorDelay);
    
    // 计算要加载的产品
    const startIndex = currentlyDisplayedCount;
    const endIndex = Math.min(startIndex + LAZY_LOAD_CONFIG.productsPerLoad, filteredProducts.length);
    const productsToLoad = filteredProducts.slice(startIndex, endIndex);
    
    // 模拟网络延迟（实际应用中可以移除）
    setTimeout(() => {
        // 添加产品到DOM
        appendProductsToDom(productsToLoad);
        
        // 更新计数
        currentlyDisplayedCount = endIndex;
        
        // 隐藏加载指示器
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
        
        // 如果已加载所有产品，显示"没有更多产品"指示器
        if (currentlyDisplayedCount >= filteredProducts.length && noMoreProductsIndicator) {
            noMoreProductsIndicator.style.display = 'block';
        }
        
        // 重置加载状态
        isLoading = false;
    }, 800); // 模拟加载延迟
}

/**
 * 将产品添加到DOM
 * @param {Array} productsToLoad - 要加载的产品数组
 */
function appendProductsToDom(productsToLoad) {
    productsToLoad.forEach((product, index) => {
        const productCard = createProductCard(product);
        
        // 设置初始状态（透明度为0，向下偏移）
        productCard.style.opacity = '0';
        productCard.style.transform = 'translateY(20px)';
        
        // 添加到DOM
        productListElement.appendChild(productCard);
        
        // 添加动画效果
        setTimeout(() => {
            productCard.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            productCard.style.opacity = '1';
            productCard.style.transform = 'translateY(0)';
        }, LAZY_LOAD_CONFIG.animationDelay * index);
    });
}

/**
 * 创建产品卡片
 * @param {Object} product - 产品对象
 * @returns {HTMLElement} - 产品卡片DOM元素
 */
function createProductCard(product) {
    const productCard = document.createElement('div');
    productCard.className = 'product-card';
    productCard.setAttribute('data-id', product.id);
    productCard.innerHTML = `
        <div class="product-card-header">
            <img src="${product.image}" alt="${product.name}">
            <div class="product-badges">
                ${product.featured ? '<span class="product-badge badge-featured">推荐</span>' : ''}
            </div>
            <div class="product-category-tag">${product.category}</div>
        </div>
        <div class="product-info">
            <div class="product-title-row">
                <h3>${product.name}</h3>
            </div>
            <div class="product-meta">
                ${product.sku ? `<div class="product-sku">SKU: ${product.sku}</div>` : ''}
            </div>
            <p>${product.description ? product.description.substring(0, 100) + '...' : '暂无产品描述'}</p>
            <div class="price-cart-row">
                <div class="price">¥${product.price.toFixed(2)}</div>
                <a href="#" class="btn-product add-to-cart" data-id="${product.id}">
                    加入购物车
                </a>
            </div>
        </div>
    `;
    return productCard;
}

/**
 * 显示产品（替代原来的displayProducts函数）
 * @param {Array} products - 要显示的产品数组
 */
function displayProductsWithLazyLoad(products) {
    console.log('使用懒加载显示产品:', products);
    
    // 保存所有产品和筛选后的产品
    allProducts = window.products || [];
    filteredProducts = products || [];
    
    // 清空产品列表
    if (productListElement) {
        productListElement.innerHTML = '';
    }
    
    // 重置计数和状态
    currentlyDisplayedCount = 0;
    isLoading = false;
    
    // 隐藏"没有更多产品"指示器
    if (noMoreProductsIndicator) {
        noMoreProductsIndicator.style.display = 'none';
    }
    
    // 如果没有产品，显示无产品提示
    if (!filteredProducts || filteredProducts.length === 0) {
        if (productListElement) {
            const noProducts = document.createElement('div');
            noProducts.className = 'no-products';
            noProducts.innerHTML = `
                <i class="fas fa-search" style="font-size: 3rem; color: var(--text-lighter); margin-bottom: 1rem;"></i>
                <p>没有找到匹配的产品</p>
                <button class="btn btn-small" id="reset-filters">重置筛选条件</button>
            `;
            productListElement.appendChild(noProducts);
            
            // 添加重置按钮事件监听器
            const resetFiltersBtn = document.getElementById('reset-filters');
            if (resetFiltersBtn) {
                resetFiltersBtn.addEventListener('click', () => {
                    const productSearchInput = document.getElementById('product-search');
                    const categoryFilter = document.getElementById('category-filter');
                    const sortFilter = document.getElementById('sort-filter');
                    
                    if (productSearchInput) productSearchInput.value = '';
                    if (categoryFilter) categoryFilter.value = 'all';
                    if (sortFilter) sortFilter.value = 'default';
                    
                    // 使用原始产品数据重新显示
                    displayProductsWithLazyLoad(allProducts);
                });
            }
        }
        return;
    }
    
    // 加载初始产品
    const initialProducts = filteredProducts.slice(0, LAZY_LOAD_CONFIG.initialProductsToShow);
    appendProductsToDom(initialProducts);
    currentlyDisplayedCount = initialProducts.length;
    
    // 如果初始产品数量小于总数，触发一次滚动检查
    if (currentlyDisplayedCount < filteredProducts.length) {
        setTimeout(handleScroll, 500);
    } else if (noMoreProductsIndicator) {
        // 如果已显示所有产品，显示"没有更多产品"指示器
        noMoreProductsIndicator.style.display = 'block';
    }
}

// 在页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initProductLazyLoad);

// 导出函数供其他模块使用
window.displayProductsWithLazyLoad = displayProductsWithLazyLoad;
