/* 按钮交互效果增强 */

/* 基础按钮交互效果 */
.btn,
.category-link,
.product-card .btn,
.add-to-cart,
.view-product,
.pagination-link,
.close,
#checkout-btn,
nav ul li a,
.footer-nav a,
.mobile-nav-toggle {
    position: relative;
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28) !important;
}

/* 增强按钮的3D效果 */
.btn,
.product-card .btn,
.add-to-cart,
.view-product,
.pagination-link,
#checkout-btn {
    border-top: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-bottom: 3px solid rgba(0, 0, 0, 0.2) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 按钮点击效果 - 增强下沉感 */
.btn:active,
.category-link:active,
.product-card .btn:active,
.add-to-cart:active,
.view-product:active,
.pagination-link:active,
.close:active,
#checkout-btn:active {
    transform: translateY(3px) scale(0.98) !important;
    box-shadow: 0 0 0 rgba(0, 0, 0, 0.1) !important;
    border-top-width: 1px !important;
    border-bottom-width: 0 !important;
    position: relative;
    top: 2px;
}

/* 按钮悬停效果 */
.btn:hover,
.product-card .btn:hover,
.add-to-cart:hover,
.view-product:hover,
.pagination-link:hover,
#checkout-btn:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.2) !important;
}

/* 波纹点击效果 */
.btn::after,
.category-link::after,
.product-card .btn::after,
.add-to-cart::after,
.view-product::after,
.pagination-link::after,
.close::after,
#checkout-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.8);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.btn.clicked::after,
.category-link.clicked::after,
.product-card .btn.clicked::after,
.add-to-cart.clicked::after,
.view-product.clicked::after,
.pagination-link.clicked::after,
.close.clicked::after,
#checkout-btn.clicked::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0) translate(-50%, -50%);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20) translate(-50%, -50%);
        opacity: 0;
    }
}

/* 按钮焦点效果 */
.btn:focus,
.category-link:focus,
.product-card .btn:focus,
.add-to-cart:focus,
.view-product:focus,
.pagination-link:focus,
.close:focus,
#checkout-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.4) !important;
}

/* 导航链接交互效果 */
nav ul li a:hover {
    transform: translateY(-2px) !important;
}

nav ul li a:active {
    transform: translateY(0) !important;
}

/* 页脚链接交互效果 */
.footer-nav a:hover {
    transform: translateY(-2px) !important;
    color: var(--accent-color) !important;
}

.footer-nav a:active {
    transform: translateY(0) !important;
}

/* 关闭按钮交互效果 */
.close {
    transition: all 0.2s ease !important;
}

.close:hover {
    transform: rotate(90deg) scale(1.2) !important;
    color: var(--accent-color) !important;
}

.close:active {
    transform: rotate(90deg) scale(1) !important;
}

/* 分类链接交互效果 */
.category-link {
    transition: all 0.2s ease !important;
}

.category-link:hover {
    transform: translateX(5px) !important;
    background-color: rgba(249, 115, 22, 0.1) !important;
}

.category-link:active {
    transform: translateX(2px) !important;
}

.category-link.active {
    position: relative;
    font-weight: bold !important;
}

.category-link.active::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 70%;
    background-color: var(--accent-color);
    border-radius: 3px;
}

/* 产品卡片交互效果 - 完全移除移动效果，保留阴影变化 */
.product-card {
    transition: box-shadow 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28) !important;
    transform: none !important; /* 强制覆盖所有transform */
}

.product-card:hover {
    /* 完全移除位移效果，只保留阴影变化 */
    transform: none !important; /* 强制覆盖所有transform */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
}

.product-card:active {
    transform: none !important; /* 强制覆盖所有transform */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1) !important;
}

/* 防止产品图片在悬停时放大 */
.product-card:hover img {
    transform: none !important; /* 强制覆盖图片放大效果 */
}

/* 移动导航切换按钮交互效果 */
.mobile-nav-toggle:hover {
    transform: scale(1.1) !important;
}

.mobile-nav-toggle:active {
    transform: scale(0.95) !important;
}

/* 按钮加载状态 */
.btn.loading,
#checkout-btn.loading {
    position: relative;
    pointer-events: none;
    color: transparent !important;
}

.btn.loading::before,
#checkout-btn.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: button-loading-spinner 0.8s linear infinite;
}

@keyframes button-loading-spinner {
    from {
        transform: rotate(0turn);
    }
    to {
        transform: rotate(1turn);
    }
}

/* 禁用状态 */
.btn:disabled,
.btn.disabled,
.product-card .btn:disabled,
.add-to-cart:disabled,
.view-product:disabled,
#checkout-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* 按钮内容震动效果 */
@keyframes button-shake {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
}

.btn-shake {
    animation: button-shake 0.4s ease-in-out;
}
