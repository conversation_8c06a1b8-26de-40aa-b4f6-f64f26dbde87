/* 购物车抛物线动画效果 */

/* 飞入购物车的产品图片 */
.product-image-fly {
    position: fixed;
    z-index: 9999;
    pointer-events: none;
    opacity: 1; /* 改为1，确保可见 */
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    transform-origin: center center;
    transition: none;
    object-fit: cover;
    width: 70px;
    height: 70px;
    /* 添加明显的边框 */
    border: 3px solid var(--accent-color);
    /* 添加发光效果 */
    filter: drop-shadow(0 0 8px var(--accent-color));
}

/* 飞入购物车的购物车图标 */
.cart-item-fly {
    position: fixed;
    z-index: 9999;
    width: 50px; /* 增大尺寸 */
    height: 50px; /* 增大尺寸 */
    background-color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    pointer-events: none;
    opacity: 1; /* 改为1，确保可见 */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    transform-origin: center center;
    transition: none;
    /* 添加发光效果 */
    filter: drop-shadow(0 0 10px var(--accent-color));
}

/* 固定在右侧居中的购物车图标 */
.floating-cart {
    position: fixed !important; /* 使用!important确保优先级 */
    right: 20px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 9999 !important; /* 增加z-index确保在最上层 */
    width: 70px; /* 增大尺寸 */
    height: 70px; /* 增大尺寸 */
    background-color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    /* 添加内部发光效果 */
    overflow: hidden;
}

/* 购物车点击效果 */
.floating-cart:active {
    transform: translateY(-50%) scale(0.9) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    background-color: #e64a19; /* 更深的橙色 */
}

/* 点击波纹效果 */
.floating-cart::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transform: scale(0);
    transition: transform 0.4s ease-out, opacity 0.4s ease-out;
    pointer-events: none;
}

.floating-cart.clicked::after {
    opacity: 1;
    transform: scale(2);
    animation: ripple 0.6s ease-out forwards;
}

@keyframes ripple {
    0% { opacity: 1; transform: scale(0); }
    50% { opacity: 0.5; }
    100% { opacity: 0; transform: scale(2); }
}

.floating-cart:hover {
    transform: translateY(-50%) scale(1.1) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.floating-cart i {
    font-size: 30px; /* 增大图标尺寸 */
    color: white;
    transition: transform 0.2s ease;
}

/* 图标点击效果 */
.floating-cart:active i {
    transform: scale(0.8);
}

.floating-cart .cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: white;
    color: var(--accent-color);
    font-size: 14px;
    font-weight: 700;
    min-width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    padding: 0 5px; /* 添加水平内边距 */
    z-index: 10000; /* 确保在最上层 */
}

/* 数量指示器点击效果 */
.floating-cart:active .cart-count {
    transform: scale(1.2);
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
    background-color: #ffeb3b; /* 黄色高亮 */
    color: #e64a19; /* 深橙色 */
}

/* 购物车图标脉冲动画 */
.cart-icon-pulse {
    animation: cart-pulse 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}

@keyframes cart-pulse {
    0% { transform: translateY(-50%) scale(1) !important; }
    50% { transform: translateY(-50%) scale(1.4) !important; }
    100% { transform: translateY(-50%) scale(1) !important; }
}

/* 粒子爆炸效果 */
.particle {
    position: fixed;
    z-index: 9998;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    pointer-events: none;
    opacity: 0;
    animation: particleFade 1s forwards;
    /* 添加发光效果 */
    filter: blur(1px);
    /* 添加尾迹效果 */
    transform-style: preserve-3d;
    will-change: transform, opacity;
}

@keyframes particleFade {
    0% { opacity: 0; transform: scale(0); }
    10% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(0); }
}

/* 轨迹痕迹点 */
.trail-dot {
    position: fixed;
    z-index: 9997;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    pointer-events: none;
    opacity: 0.7;
    transition: opacity 0.3s ease; /* 减少过渡时间 */
    box-shadow: 0 0 10px currentColor;
    filter: blur(1px);
}

/* 购物车数量变化动画 */
@keyframes count-change {
    0% { transform: scale(1); }
    50% { transform: scale(1.5); color: #ff3b30; }
    100% { transform: scale(1); }
}

.count-change {
    animation: count-change 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}

/* 添加到购物车成功提示动画 */
.add-success-indicator {
    position: fixed;
    z-index: 10000;
    background-color: rgba(46, 213, 115, 0.9);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(46, 213, 115, 0.4);
    pointer-events: none;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.add-success-indicator.show {
    opacity: 1;
    transform: translateY(0);
}
